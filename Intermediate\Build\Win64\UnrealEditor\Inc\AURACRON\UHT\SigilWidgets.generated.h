// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UI/SigilWidgets.h"

#ifdef AURACRON_SigilWidgets_generated_h
#error "SigilWidgets.generated.h already included, missing '#pragma once' in SigilWidgets.h"
#endif
#define AURACRON_SigilWidgets_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ASigilItem;
class USigilDragDropOperation;
class USigilManagerComponent;
class USigilSlotWidget;
enum class ESigilSlotState : uint8;
struct FSigilNotificationData;
struct FSigilSystemStats;

// ********** Begin ScriptStruct FSigilSlotVisualConfig ********************************************
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_60_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilSlotVisualConfig;
// ********** End ScriptStruct FSigilSlotVisualConfig **********************************************

// ********** Begin ScriptStruct FSigilNotificationData ********************************************
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_113_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilNotificationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilNotificationData;
// ********** End ScriptStruct FSigilNotificationData **********************************************

// ********** Begin Class USigilDragDropOperation **************************************************
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsValidDrop); \
	DECLARE_FUNCTION(execCanDropOnSlot);


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_USigilDragDropOperation_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilDragDropOperation(); \
	friend struct Z_Construct_UClass_USigilDragDropOperation_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilDragDropOperation_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilDragDropOperation, UDragDropOperation, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilDragDropOperation_NoRegister) \
	DECLARE_SERIALIZER(USigilDragDropOperation)


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilDragDropOperation(USigilDragDropOperation&&) = delete; \
	USigilDragDropOperation(const USigilDragDropOperation&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilDragDropOperation); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilDragDropOperation); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilDragDropOperation) \
	NO_API virtual ~USigilDragDropOperation();


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_144_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_147_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilDragDropOperation;

// ********** End Class USigilDragDropOperation ****************************************************

// ********** Begin Class USigilSlotWidget *********************************************************
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnInteractionButtonUnhovered); \
	DECLARE_FUNCTION(execOnInteractionButtonHovered); \
	DECLARE_FUNCTION(execOnInteractionButtonClicked); \
	DECLARE_FUNCTION(execPlaySlotSound); \
	DECLARE_FUNCTION(execStopVFXEffect); \
	DECLARE_FUNCTION(execPlayVFXEffect); \
	DECLARE_FUNCTION(execPlaySlotAnimation); \
	DECLARE_FUNCTION(execUpdateRarityIndicator); \
	DECLARE_FUNCTION(execUpdateVisualState); \
	DECLARE_FUNCTION(execSetDragHighlight); \
	DECLARE_FUNCTION(execHandleDrop); \
	DECLARE_FUNCTION(execCreateDragDropOperation); \
	DECLARE_FUNCTION(execIsFusionReady); \
	DECLARE_FUNCTION(execCanAcceptSigil); \
	DECLARE_FUNCTION(execIsSlotLocked); \
	DECLARE_FUNCTION(execIsSlotEmpty); \
	DECLARE_FUNCTION(execUpdateTimerDisplay); \
	DECLARE_FUNCTION(execUpdateFusionProgress); \
	DECLARE_FUNCTION(execUpdateSlotState); \
	DECLARE_FUNCTION(execUnequipSigil); \
	DECLARE_FUNCTION(execEquipSigil); \
	DECLARE_FUNCTION(execInitializeSlot);


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_USigilSlotWidget_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilSlotWidget(); \
	friend struct Z_Construct_UClass_USigilSlotWidget_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilSlotWidget_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilSlotWidget, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilSlotWidget_NoRegister) \
	DECLARE_SERIALIZER(USigilSlotWidget)


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilSlotWidget(USigilSlotWidget&&) = delete; \
	USigilSlotWidget(const USigilSlotWidget&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilSlotWidget); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilSlotWidget); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(USigilSlotWidget) \
	NO_API virtual ~USigilSlotWidget();


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_204_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_207_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilSlotWidget;

// ********** End Class USigilSlotWidget ***********************************************************

// ********** Begin Class USigilInventoryWidget ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnStatsChanged); \
	DECLARE_FUNCTION(execOnSlotUnlockedCallback); \
	DECLARE_FUNCTION(execOnFusionCompleted); \
	DECLARE_FUNCTION(execOnSigilUnequipped); \
	DECLARE_FUNCTION(execOnSigilEquipped); \
	DECLARE_FUNCTION(execOnReforgeButtonClicked); \
	DECLARE_FUNCTION(execGetAllSlotWidgets); \
	DECLARE_FUNCTION(execGetSlotWidget); \
	DECLARE_FUNCTION(execUpdateReforgeButton); \
	DECLARE_FUNCTION(execUpdateStats); \
	DECLARE_FUNCTION(execUnlockSlot); \
	DECLARE_FUNCTION(execUpdateAllSlots); \
	DECLARE_FUNCTION(execCreateSlots); \
	DECLARE_FUNCTION(execInitializeInventory);


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_USigilInventoryWidget_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilInventoryWidget(); \
	friend struct Z_Construct_UClass_USigilInventoryWidget_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilInventoryWidget_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilInventoryWidget, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilInventoryWidget_NoRegister) \
	DECLARE_SERIALIZER(USigilInventoryWidget)


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilInventoryWidget(USigilInventoryWidget&&) = delete; \
	USigilInventoryWidget(const USigilInventoryWidget&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilInventoryWidget); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilInventoryWidget); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(USigilInventoryWidget) \
	NO_API virtual ~USigilInventoryWidget();


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_452_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_455_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilInventoryWidget;

// ********** End Class USigilInventoryWidget ******************************************************

// ********** Begin Class USigilNotificationWidget *************************************************
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execHideNotification); \
	DECLARE_FUNCTION(execShowNotification);


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_USigilNotificationWidget_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilNotificationWidget(); \
	friend struct Z_Construct_UClass_USigilNotificationWidget_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilNotificationWidget_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilNotificationWidget, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilNotificationWidget_NoRegister) \
	DECLARE_SERIALIZER(USigilNotificationWidget)


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilNotificationWidget(USigilNotificationWidget&&) = delete; \
	USigilNotificationWidget(const USigilNotificationWidget&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilNotificationWidget); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilNotificationWidget); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(USigilNotificationWidget) \
	NO_API virtual ~USigilNotificationWidget();


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_598_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_601_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilNotificationWidget;

// ********** End Class USigilNotificationWidget ***************************************************

// ********** Begin Class USigilHUDWidget **********************************************************
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearAllNotifications); \
	DECLARE_FUNCTION(execShowNotification); \
	DECLARE_FUNCTION(execInitializeHUD);


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_USigilHUDWidget_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilHUDWidget(); \
	friend struct Z_Construct_UClass_USigilHUDWidget_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilHUDWidget_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilHUDWidget, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilHUDWidget_NoRegister) \
	DECLARE_SERIALIZER(USigilHUDWidget)


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilHUDWidget(USigilHUDWidget&&) = delete; \
	USigilHUDWidget(const USigilHUDWidget&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilHUDWidget); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilHUDWidget); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(USigilHUDWidget) \
	NO_API virtual ~USigilHUDWidget();


#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_662_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h_665_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilHUDWidget;

// ********** End Class USigilHUDWidget ************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h

// ********** Begin Enum ESigilSlotState ***********************************************************
#define FOREACH_ENUM_ESIGILSLOTSTATE(op) \
	op(ESigilSlotState::Empty) \
	op(ESigilSlotState::Equipped) \
	op(ESigilSlotState::FusionReady) \
	op(ESigilSlotState::Fusing) \
	op(ESigilSlotState::Locked) \
	op(ESigilSlotState::Highlighted) \
	op(ESigilSlotState::DragTarget) \
	op(ESigilSlotState::Invalid) 

enum class ESigilSlotState : uint8;
template<> struct TIsUEnumClass<ESigilSlotState> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilSlotState>();
// ********** End Enum ESigilSlotState *************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
