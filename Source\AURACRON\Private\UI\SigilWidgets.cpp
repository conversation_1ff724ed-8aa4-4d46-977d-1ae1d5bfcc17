// SigilWidgets.cpp
// AURACRON - Sistema de Sígilos
// Implementação dos UMG Widgets para interface do usuário
// APIs verificadas: UserWidget.h, DragDropOperation.h, WidgetBlueprintLibrary.h

#include "UI/SigilWidgets.h"
#include "Sigils/SigilItem.h"
#include "Sigils/SigilManagerComponent.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "Components/CanvasPanelSlot.h"
#include "Components/HorizontalBoxSlot.h"
#include "Components/VerticalBoxSlot.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundBase.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Engine/Engine.h"

// ========================================
// DRAG & DROP OPERATION
// ========================================

USigilDragDropOperation::USigilDragDropOperation()
{
    DraggedSigil = nullptr;
    SourceSlotIndex = -1;
    SourceSlotWidget = nullptr;
    DragVisual = nullptr;
    DragOffset = FVector2D::ZeroVector;
    bCanDropOnEmpty = true;
    bCanSwapSigils = true;
    bCanDropOnSameSlot = false;
}

bool USigilDragDropOperation::CanDropOnSlot(USigilSlotWidget* TargetSlot) const
{
    if (!TargetSlot || !DraggedSigil)
    {
        return false;
    }

    // Não pode dropar no mesmo slot
    if (!bCanDropOnSameSlot && TargetSlot == SourceSlotWidget)
    {
        return false;
    }

    // Verificar se o slot está bloqueado
    if (TargetSlot->IsSlotLocked())
    {
        return false;
    }

    // Verificar se pode dropar em slot vazio
    if (TargetSlot->IsSlotEmpty())
    {
        return bCanDropOnEmpty && TargetSlot->CanAcceptSigil(DraggedSigil);
    }

    // Verificar se pode trocar sígilos
    if (!bCanSwapSigils)
    {
        return false;
    }

    // Verificar compatibilidade para troca
    return TargetSlot->CanAcceptSigil(DraggedSigil);
}

bool USigilDragDropOperation::IsValidDrop() const
{
    return DraggedSigil != nullptr && SourceSlotWidget != nullptr;
}

// ========================================
// SLOT WIDGET
// ========================================

USigilSlotWidget::USigilSlotWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SlotIndex = 0;
    EquippedSigil = nullptr;
    CurrentState = ESigilSlotState::Empty;
    SigilManager = nullptr;
    VFXComponent = nullptr;
    PreviousState = ESigilSlotState::Empty;
    AnimationTimer = 0.0f;
    bIsAnimating = false;
    bIsDragTarget = false;
    bIsHighlighted = false;
    bIsHovered = false;
}

void USigilSlotWidget::NativeConstruct()
{
    Super::NativeConstruct();

    // Bind eventos do botão de interação
    if (InteractionButton)
    {
        InteractionButton->OnClicked.AddDynamic(this, &USigilSlotWidget::OnInteractionButtonClicked);
        InteractionButton->OnHovered.AddDynamic(this, &USigilSlotWidget::OnInteractionButtonHovered);
        InteractionButton->OnUnhovered.AddDynamic(this, &USigilSlotWidget::OnInteractionButtonUnhovered);
    }

    // Configurar componente VFX
    SetupVFXComponent();

    // Atualizar estado visual inicial
    UpdateVisualState();
}

void USigilSlotWidget::NativeDestruct()
{
    // Limpar VFX
    if (VFXComponent)
    {
        VFXComponent->DestroyComponent();
        VFXComponent = nullptr;
    }

    Super::NativeDestruct();
}

void USigilSlotWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
    Super::NativeTick(MyGeometry, InDeltaTime);

    // Atualizar animações
    if (bIsAnimating)
    {
        AnimationTimer += InDeltaTime;
        if (AnimationTimer >= VisualConfig.SlotAnimationDuration)
        {
            bIsAnimating = false;
            AnimationTimer = 0.0f;
        }
    }

    // Atualizar progresso de fusão
    if (CurrentState == ESigilSlotState::Fusing && SigilManager && EquippedSigil)
    {
        float FusionProgress = SigilManager->GetFusionProgress(SlotIndex);
        UpdateFusionProgress(FusionProgress);

        float RemainingTime = SigilManager->GetTimeToFusion(SlotIndex);
        UpdateTimerDisplay(RemainingTime);
    }
}

void USigilSlotWidget::InitializeSlot(int32 InSlotIndex, USigilManagerComponent* InSigilManager)
{
    SlotIndex = InSlotIndex;
    SigilManager = InSigilManager;

    // Verificar se o slot está desbloqueado
    if (SigilManager && !SigilManager->IsSlotUnlocked(SlotIndex))
    {
        UpdateSlotState(ESigilSlotState::Locked);
    }
    else
    {
        UpdateSlotState(ESigilSlotState::Empty);
    }

    // Verificar se já tem sigilo equipado
    if (SigilManager)
    {
        ASigilItem* ExistingSigil = SigilManager->GetEquippedSigil(SlotIndex);
        if (ExistingSigil)
        {
            EquipSigil(ExistingSigil);
        }
    }
}

bool USigilSlotWidget::EquipSigil(ASigilItem* Sigil)
{
    if (!Sigil || !CanAcceptSigil(Sigil))
    {
        return false;
    }

    // Desequipar sigilo anterior se houver
    if (EquippedSigil)
    {
        UnequipSigil();
    }

    // Equipar novo sigilo
    EquippedSigil = Sigil;

    // Atualizar visual
    if (SigilImage && Sigil->SigilData.Icon.Get())
    {
        SigilImage->SetBrushFromTexture(Sigil->SigilData.Icon.Get());
        SigilImage->SetVisibility(ESlateVisibility::Visible);
    }

    // Atualizar estado
    if (SigilManager && SigilManager->IsSigilReadyForFusion(SlotIndex))
    {
        UpdateSlotState(ESigilSlotState::FusionReady);
    }
    else
    {
        UpdateSlotState(ESigilSlotState::Equipped);
    }

    // Atualizar indicador de raridade
    UpdateRarityIndicator();

    // Tocar som de equipar
    PlaySlotSound(ESigilSlotState::Equipped);

    // Tocar VFX de equipar
    PlayVFXEffect(ESigilSlotState::Equipped);

    // Chamar evento Blueprint
    OnSigilEquipped(Sigil);

    return true;
}

bool USigilSlotWidget::UnequipSigil()
{
    if (!EquippedSigil)
    {
        return false;
    }

    ASigilItem* PreviousSigil = EquippedSigil;
    EquippedSigil = nullptr;

    // Limpar visual
    if (SigilImage)
    {
        SigilImage->SetBrushFromTexture(nullptr);
        SigilImage->SetVisibility(ESlateVisibility::Hidden);
    }

    // Atualizar estado
    UpdateSlotState(ESigilSlotState::Empty);

    // Limpar indicador de raridade
    if (RarityIndicator)
    {
        RarityIndicator->SetVisibility(ESlateVisibility::Hidden);
    }

    // Parar VFX
    StopVFXEffect();

    // Tocar som de desequipar
    PlaySlotSound(ESigilSlotState::Empty);

    // Chamar evento Blueprint
    OnSigilUnequipped(PreviousSigil);

    return true;
}

void USigilSlotWidget::UpdateSlotState(ESigilSlotState NewState)
{
    if (CurrentState == NewState)
    {
        return;
    }

    PreviousState = CurrentState;
    CurrentState = NewState;

    // Tocar animação de transição
    PlaySlotAnimation(PreviousState, CurrentState);

    // Atualizar visual
    UpdateVisualState();

    // Eventos específicos por estado
    switch (NewState)
    {
        case ESigilSlotState::FusionReady:
            OnFusionStarted();
            break;
        case ESigilSlotState::Equipped:
            if (PreviousState == ESigilSlotState::Fusing)
            {
                OnFusionCompleted();
            }
            break;
        case ESigilSlotState::Empty:
            if (PreviousState == ESigilSlotState::Locked)
            {
                OnSlotUnlocked();
            }
            break;
    }
}

void USigilSlotWidget::UpdateFusionProgress(float Progress)
{
    if (FusionProgressBar)
    {
        FusionProgressBar->SetPercent(Progress);
        FusionProgressBar->SetVisibility(Progress > 0.0f ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
    }
}

void USigilSlotWidget::UpdateTimerDisplay(float RemainingTime)
{
    if (TimerText)
    {
        if (RemainingTime > 0.0f)
        {
            TimerText->SetText(FormatTimerText(RemainingTime));
            TimerText->SetVisibility(ESlateVisibility::Visible);
        }
        else
        {
            TimerText->SetVisibility(ESlateVisibility::Hidden);
        }
    }
}

bool USigilSlotWidget::IsSlotEmpty() const
{
    return EquippedSigil == nullptr;
}

bool USigilSlotWidget::IsSlotLocked() const
{
    return CurrentState == ESigilSlotState::Locked;
}

bool USigilSlotWidget::CanAcceptSigil(ASigilItem* Sigil) const
{
    if (!Sigil || IsSlotLocked())
    {
        return false;
    }

    // Verificar com o manager se pode equipar
    if (SigilManager)
    {
        return SigilManager->CanEquipSigil(Sigil, SlotIndex);
    }

    return true;
}

bool USigilSlotWidget::IsFusionReady() const
{
    return CurrentState == ESigilSlotState::FusionReady || CurrentState == ESigilSlotState::Fusing;
}

// ========================================
// DRAG & DROP IMPLEMENTATION
// ========================================

FReply USigilSlotWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
    if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton && EquippedSigil)
    {
        return FReply::Handled().DetectDrag(TakeWidget(), EKeys::LeftMouseButton);
    }

    return FReply::Unhandled();
}

void USigilSlotWidget::NativeOnDragDetected(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent, UDragDropOperation*& OutOperation)
{
    if (!EquippedSigil)
    {
        return;
    }

    // Criar operação de drag & drop
    USigilDragDropOperation* DragOperation = CreateDragDropOperation();
    if (DragOperation)
    {
        OutOperation = DragOperation;
        OnDragStarted(DragOperation);
    }
}

bool USigilSlotWidget::NativeOnDrop(const FGeometry& InGeometry, const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation)
{
    USigilDragDropOperation* SigilDragOp = Cast<USigilDragDropOperation>(InOperation);
    if (!SigilDragOp)
    {
        return false;
    }

    bool bSuccess = HandleDrop(SigilDragOp);
    OnDropReceived(SigilDragOp, bSuccess);

    // Remover highlight
    SetDragHighlight(false);

    return bSuccess;
}

void USigilSlotWidget::NativeOnDragEnter(const FGeometry& InGeometry, const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation)
{
    USigilDragDropOperation* SigilDragOp = Cast<USigilDragDropOperation>(InOperation);
    if (SigilDragOp && SigilDragOp->CanDropOnSlot(this))
    {
        SetDragHighlight(true);
        OnDragEntered(SigilDragOp);
    }
}

void USigilSlotWidget::NativeOnDragLeave(const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation)
{
    USigilDragDropOperation* SigilDragOp = Cast<USigilDragDropOperation>(InOperation);
    if (SigilDragOp)
    {
        SetDragHighlight(false);
        OnDragLeft(SigilDragOp);
    }
}

USigilDragDropOperation* USigilSlotWidget::CreateDragDropOperation()
{
    if (!EquippedSigil)
    {
        return nullptr;
    }

    USigilDragDropOperation* DragOperation = NewObject<USigilDragDropOperation>();
    DragOperation->DraggedSigil = EquippedSigil;
    DragOperation->SourceSlotIndex = SlotIndex;
    DragOperation->SourceSlotWidget = this;

    // Criar visual de drag
    if (SigilImage)
    {
        UUserWidget* DragWidget = CreateWidget<UUserWidget>(GetWorld(), GetClass());
        if (DragWidget)
        {
            DragOperation->DragVisual = DragWidget;
            DragOperation->DefaultDragVisual = DragWidget;
        }
    }

    return DragOperation;
}

bool USigilSlotWidget::HandleDrop(USigilDragDropOperation* DropOperation)
{
    if (!DropOperation || !DropOperation->CanDropOnSlot(this) || !SigilManager)
    {
        return false;
    }

    ASigilItem* DroppedSigil = DropOperation->DraggedSigil;
    int32 SourceSlot = DropOperation->SourceSlotIndex;

    // Se o slot está vazio, equipar o sigilo
    if (IsSlotEmpty())
    {
        return SigilManager->EquipSigil(DroppedSigil, SlotIndex);
    }
    // Se o slot tem sigilo, trocar
    else if (EquippedSigil)
    {
        return SigilManager->SwapSigils(SourceSlot, SlotIndex);
    }

    return false;
}

void USigilSlotWidget::SetDragHighlight(bool bHighlighted)
{
    bIsDragTarget = bHighlighted;
    
    if (bHighlighted)
    {
        UpdateSlotState(ESigilSlotState::DragTarget);
    }
    else
    {
        // Voltar ao estado anterior
        if (EquippedSigil)
        {
            UpdateSlotState(ESigilSlotState::Equipped);
        }
        else
        {
            UpdateSlotState(ESigilSlotState::Empty);
        }
    }
}

// ========================================
// VISUAL & ANIMAÇÕES
// ========================================

void USigilSlotWidget::UpdateVisualState()
{
    UpdateSlotMaterial();
    UpdateSlotColor();
    
    // Atualizar visibilidade de componentes
    if (LockIndicator)
    {
        LockIndicator->SetVisibility(IsSlotLocked() ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
    }
    
    if (FusionProgressBar)
    {
        FusionProgressBar->SetVisibility(CurrentState == ESigilSlotState::Fusing ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
    }
}

void USigilSlotWidget::UpdateRarityIndicator()
{
    if (!RarityIndicator || !EquippedSigil)
    {
        if (RarityIndicator)
        {
            RarityIndicator->SetVisibility(ESlateVisibility::Hidden);
        }
        return;
    }

    ESigilRarity Rarity = EquippedSigil->SigilData.Rarity;
    const FLinearColor* RarityColorPtr = VisualConfig.RarityColors.Find(Rarity);
    FLinearColor RarityColor = RarityColorPtr ? *RarityColorPtr : FLinearColor::White;

    RarityIndicator->SetColorAndOpacity(RarityColor);
    RarityIndicator->SetVisibility(ESlateVisibility::Visible);
}

void USigilSlotWidget::PlaySlotAnimation(ESigilSlotState FromState, ESigilSlotState ToState)
{
    bIsAnimating = true;
    AnimationTimer = 0.0f;

    // Implementar animações específicas via Blueprint
    // Aqui apenas configuramos o timer
}

void USigilSlotWidget::PlayVFXEffect(ESigilSlotState EffectState)
{
    if (!VFXComponent || !VisualConfig.StateVFX.Contains(EffectState))
    {
        return;
    }

    TSoftObjectPtr<UNiagaraSystem> VFXAsset = VisualConfig.StateVFX[EffectState];
    if (VFXAsset.IsValid())
    {
        VFXComponent->SetAsset(VFXAsset.Get());
        VFXComponent->Activate(true);
    }
}

void USigilSlotWidget::StopVFXEffect()
{
    if (VFXComponent)
    {
        VFXComponent->Deactivate();
    }
}

void USigilSlotWidget::PlaySlotSound(ESigilSlotState SoundState)
{
    USoundBase* SoundToPlay = nullptr;

    switch (SoundState)
    {
        case ESigilSlotState::Equipped:
            SoundToPlay = VisualConfig.EquipSound.Get();
            break;
        case ESigilSlotState::Empty:
            SoundToPlay = VisualConfig.UnequipSound.Get();
            break;
        case ESigilSlotState::FusionReady:
            SoundToPlay = VisualConfig.FusionSound.Get();
            break;
        case ESigilSlotState::Invalid:
            SoundToPlay = VisualConfig.InvalidSound.Get();
            break;
    }

    if (SoundToPlay)
    {
        UGameplayStatics::PlaySound2D(GetWorld(), SoundToPlay);
    }
}

// ========================================
// EVENTOS DE INTERAÇÃO
// ========================================

void USigilSlotWidget::OnInteractionButtonClicked()
{
    OnSlotClicked();

    // Lógica adicional de clique
    if (EquippedSigil && SigilManager)
    {
        // Verificar se pode forçar fusão
        if (IsFusionReady())
        {
            EquippedSigil->TriggerFusion();
        }
    }
}

void USigilSlotWidget::OnInteractionButtonHovered()
{
    bIsHovered = true;
    OnSlotHovered(true);
}

void USigilSlotWidget::OnInteractionButtonUnhovered()
{
    bIsHovered = false;
    OnSlotHovered(false);
}

// ========================================
// FUNÇÕES INTERNAS
// ========================================

void USigilSlotWidget::UpdateSlotMaterial()
{
    if (!SlotBorder || !VisualConfig.StateMaterials.Contains(CurrentState))
    {
        return;
    }

    UMaterialInterface* StateMaterial = VisualConfig.StateMaterials[CurrentState].Get();
    if (StateMaterial)
    {
        SlotBorder->SetBrushFromMaterial(StateMaterial);
    }
}

void USigilSlotWidget::UpdateSlotColor()
{
    if (!SlotBorder)
    {
        return;
    }

    FLinearColor SlotColor = FLinearColor::White;

    // Cor baseada no estado
    switch (CurrentState)
    {
        case ESigilSlotState::Locked:
            SlotColor = FLinearColor::Gray;
            break;
        case ESigilSlotState::DragTarget:
            SlotColor = FLinearColor::Green;
            break;
        case ESigilSlotState::Invalid:
            SlotColor = FLinearColor::Red;
            break;
        case ESigilSlotState::FusionReady:
            SlotColor = FLinearColor::Yellow;
            break;
    }

    // Aplicar cor de raridade se equipado
    if (EquippedSigil && VisualConfig.RarityColors.Contains(EquippedSigil->SigilData.Rarity))
    {
        if (const FLinearColor* RarityColor = VisualConfig.RarityColors.Find(EquippedSigil->SigilData.Rarity))
        {
            SlotColor = FLinearColor::LerpUsingHSV(SlotColor, *RarityColor, 0.5f);
        }
    }

    SlotBorder->SetBrushColor(SlotColor);
}

void USigilSlotWidget::SetupVFXComponent()
{
    if (!VFXComponent)
    {
        VFXComponent = NewObject<UNiagaraComponent>(this);
        if (VFXComponent)
        {
            // Para widgets UMG, o VFXComponent será gerenciado pelo sistema de UI
            VFXComponent->SetAutoActivate(false);
        }
    }
}

FText USigilSlotWidget::FormatTimerText(float TimeInSeconds) const
{
    int32 Minutes = FMath::FloorToInt(TimeInSeconds / 60.0f);
    int32 Seconds = FMath::FloorToInt(TimeInSeconds) % 60;
    
    return FText::FromString(FString::Printf(TEXT("%02d:%02d"), Minutes, Seconds));
}

// ========================================
// INVENTORY WIDGET
// ========================================

USigilInventoryWidget::USigilInventoryWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    MaxSlots = 6;
    SigilManager = nullptr;
    SlotWidgetClass = USigilSlotWidget::StaticClass();
    bIsInitialized = false;
    LastStatsUpdate = 0.0f;
}

void USigilInventoryWidget::NativeConstruct()
{
    Super::NativeConstruct();

    // Bind eventos do botão de reforge
    if (ReforgeButton)
    {
        ReforgeButton->OnClicked.AddDynamic(this, &USigilInventoryWidget::OnReforgeButtonClicked);
    }
}

void USigilInventoryWidget::NativeDestruct()
{
    // Limpar callbacks do manager
    if (SigilManager)
    {
        // Desconectar delegates se necessário
    }

    Super::NativeDestruct();
}

void USigilInventoryWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
    Super::NativeTick(MyGeometry, InDeltaTime);

    // Atualizar stats periodicamente
    LastStatsUpdate += InDeltaTime;
    if (LastStatsUpdate >= 1.0f) // Atualizar a cada segundo
    {
        UpdateStats();
        UpdateReforgeButton();
        LastStatsUpdate = 0.0f;
    }
}

void USigilInventoryWidget::InitializeInventory(USigilManagerComponent* InSigilManager)
{
    if (bIsInitialized || !InSigilManager)
    {
        return;
    }

    SigilManager = InSigilManager;

    // Criar slots
    CreateSlots();

    // Conectar callbacks do manager
    if (SigilManager)
    {
        // Bind delegates do manager (implementar quando disponível)
        // SigilManager->OnSigilEquipped.AddDynamic(this, &USigilInventoryWidget::OnSigilEquipped);
        // SigilManager->OnSigilUnequipped.AddDynamic(this, &USigilInventoryWidget::OnSigilUnequipped);
        // SigilManager->OnFusionCompleted.AddDynamic(this, &USigilInventoryWidget::OnFusionCompleted);
        // SigilManager->OnSlotUnlocked.AddDynamic(this, &USigilInventoryWidget::OnSlotUnlockedCallback);
        // SigilManager->OnStatsChanged.AddDynamic(this, &USigilInventoryWidget::OnStatsChanged);
    }

    bIsInitialized = true;
    OnInventoryInitialized();
}

void USigilInventoryWidget::CreateSlots()
{
    if (!SlotsContainer || !SlotWidgetClass)
    {
        return;
    }

    // Limpar slots existentes
    SlotsContainer->ClearChildren();
    SigilSlots.Empty();

    // Criar novos slots
    for (int32 i = 0; i < MaxSlots; ++i)
    {
        USigilSlotWidget* SlotWidget = CreateWidget<USigilSlotWidget>(GetWorld(), SlotWidgetClass);
        if (SlotWidget)
        {
            SlotWidget->InitializeSlot(i, SigilManager);
            SigilSlots.Add(SlotWidget);

            // Adicionar ao container
            UHorizontalBoxSlot* SlotContainer = SlotsContainer->AddChildToHorizontalBox(SlotWidget);
            if (SlotContainer)
            {
                SlotContainer->SetSize(FSlateChildSize(ESlateSizeRule::Fill));
                SlotContainer->SetPadding(FMargin(2.0f));
            }
        }
    }
}

void USigilInventoryWidget::UpdateAllSlots()
{
    for (USigilSlotWidget* SlotWidget : SigilSlots)
    {
        if (SlotWidget && SigilManager)
        {
            // Atualizar estado do slot baseado no manager
            if (!SigilManager->IsSlotUnlocked(SlotWidget->SlotIndex))
            {
                SlotWidget->UpdateSlotState(ESigilSlotState::Locked);
            }
            else
            {
                ASigilItem* EquippedSigil = SigilManager->GetEquippedSigil(SlotWidget->SlotIndex);
                if (EquippedSigil)
                {
                    SlotWidget->EquipSigil(EquippedSigil);
                }
                else
                {
                    SlotWidget->UpdateSlotState(ESigilSlotState::Empty);
                }
            }
        }
    }
}

void USigilInventoryWidget::UnlockSlot(int32 SlotIndex)
{
    if (SlotIndex >= 0 && SlotIndex < SigilSlots.Num())
    {
        USigilSlotWidget* SlotWidget = SigilSlots[SlotIndex];
        if (SlotWidget)
        {
            SlotWidget->UpdateSlotState(ESigilSlotState::Empty);
            OnSlotUnlocked(SlotIndex);
        }
    }
}

void USigilInventoryWidget::UpdateStats()
{
    if (!SigilManager)
    {
        return;
    }

    FSigilSystemStats Stats = SigilManager->GetSystemStatistics();

    // Atualizar texto de poder total
    if (TotalPowerText)
    {
        float TotalPower = SigilManager->CalculateTotalSigilPower();
        TotalPowerText->SetText(FText::FromString(FString::Printf(TEXT("Poder Total: %.1f"), TotalPower)));
    }

    // Atualizar texto de sígilos equipados
    if (EquippedSigilsText)
    {
        EquippedSigilsText->SetText(FText::FromString(FString::Printf(TEXT("Equipados: %d/%d"), 
            Stats.TotalEquippedSigils, MaxSlots)));
    }

    OnStatsUpdated(Stats);
}

void USigilInventoryWidget::UpdateReforgeButton()
{
    if (!ReforgeButton || !SigilManager)
    {
        return;
    }

    bool bCanReforge = SigilManager->CanReforge();
    ReforgeButton->SetIsEnabled(bCanReforge);

    // Atualizar texto de cooldown
    if (ReforgeCooldownText)
    {
        if (!bCanReforge)
        {
            float RemainingCooldown = SigilManager->GetReforgeTimeRemaining();
            ReforgeCooldownText->SetText(FText::FromString(FString::Printf(TEXT("Cooldown: %.1fs"), RemainingCooldown)));
            ReforgeCooldownText->SetVisibility(ESlateVisibility::Visible);
        }
        else
        {
            ReforgeCooldownText->SetVisibility(ESlateVisibility::Hidden);
        }
    }

    OnReforgeAvailable(bCanReforge);
}

USigilSlotWidget* USigilInventoryWidget::GetSlotWidget(int32 SlotIndex) const
{
    if (SlotIndex >= 0 && SlotIndex < SigilSlots.Num())
    {
        return SigilSlots[SlotIndex];
    }
    return nullptr;
}

TArray<USigilSlotWidget*> USigilInventoryWidget::GetAllSlotWidgets() const
{
    TArray<USigilSlotWidget*> Result;
    for (TObjectPtr<USigilSlotWidget> SlotWidget : SigilSlots)
    {
        if (SlotWidget)
        {
            Result.Add(SlotWidget);
        }
    }
    return Result;
}

// ========================================
// EVENTOS DO INVENTORY
// ========================================

void USigilInventoryWidget::OnReforgeButtonClicked()
{
    if (SigilManager && SigilManager->CanReforge())
    {
        // Implementar seleção de sigilo para reforge
        // Por enquanto, reforge o primeiro sigilo equipado
        for (int32 i = 0; i < MaxSlots; ++i)
        {
            if (SigilManager->GetEquippedSigil(i))
            {
                SigilManager->ReforgeSigil(i);
                break;
            }
        }
    }
}

void USigilInventoryWidget::OnSigilEquipped(int32 SlotIndex, ASigilItem* Sigil)
{
    UpdateAllSlots();
    UpdateStats();
}

void USigilInventoryWidget::OnSigilUnequipped(int32 SlotIndex, ASigilItem* Sigil)
{
    UpdateAllSlots();
    UpdateStats();
}

void USigilInventoryWidget::OnFusionCompleted(int32 SlotIndex, ASigilItem* FusedSigil)
{
    UpdateAllSlots();
    UpdateStats();
}

void USigilInventoryWidget::OnSlotUnlockedCallback(int32 SlotIndex)
{
    UnlockSlot(SlotIndex);
}

void USigilInventoryWidget::OnStatsChanged(const FSigilSystemStats& NewStats)
{
    UpdateStats();
}

// ========================================
// NOTIFICATION WIDGET
// ========================================

USigilNotificationWidget::USigilNotificationWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    RemainingDuration = 0.0f;
    bIsShowing = false;
}

void USigilNotificationWidget::NativeConstruct()
{
    Super::NativeConstruct();
    SetVisibility(ESlateVisibility::Hidden);
}

void USigilNotificationWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
    Super::NativeTick(MyGeometry, InDeltaTime);

    if (bIsShowing && RemainingDuration > 0.0f)
    {
        RemainingDuration -= InDeltaTime;
        
        // Atualizar barra de duração
        if (DurationBar)
        {
            float Progress = RemainingDuration / CurrentNotification.Duration;
            DurationBar->SetPercent(Progress);
        }

        // Esconder quando acabar o tempo
        if (RemainingDuration <= 0.0f)
        {
            HideNotification();
        }
    }
}

void USigilNotificationWidget::ShowNotification(const FSigilNotificationData& NotificationData)
{
    CurrentNotification = NotificationData;
    RemainingDuration = NotificationData.Duration;
    bIsShowing = true;

    // Atualizar componentes UI
    if (TitleText)
    {
        TitleText->SetText(NotificationData.Title);
    }

    if (DescriptionText)
    {
        DescriptionText->SetText(NotificationData.Description);
    }

    if (NotificationIcon && NotificationData.Icon.IsValid())
    {
        NotificationIcon->SetBrushFromTexture(NotificationData.Icon.Get());
    }

    if (NotificationBorder)
    {
        NotificationBorder->SetBrushColor(NotificationData.Color);
    }

    if (DurationBar)
    {
        DurationBar->SetPercent(1.0f);
    }

    SetVisibility(ESlateVisibility::Visible);
    OnNotificationShown(NotificationData);
}

void USigilNotificationWidget::HideNotification()
{
    bIsShowing = false;
    RemainingDuration = 0.0f;
    SetVisibility(ESlateVisibility::Hidden);
    OnNotificationHidden();
}

// ========================================
// HUD WIDGET
// ========================================

USigilHUDWidget::USigilHUDWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NotificationWidgetClass = USigilNotificationWidget::StaticClass();
    MaxNotifications = 5;
}

void USigilHUDWidget::NativeConstruct()
{
    Super::NativeConstruct();
}

void USigilHUDWidget::InitializeHUD(USigilManagerComponent* SigilManager)
{
    if (InventoryWidget)
    {
        InventoryWidget->InitializeInventory(SigilManager);
    }

    OnHUDInitialized();
}

void USigilHUDWidget::ShowNotification(const FSigilNotificationData& NotificationData)
{
    if (!NotificationWidgetClass || !NotificationsContainer)
    {
        return;
    }

    // Remover notificações antigas se necessário
    while (ActiveNotifications.Num() >= MaxNotifications)
    {
        RemoveOldestNotification();
    }

    // Criar nova notificação
    USigilNotificationWidget* NotificationWidget = CreateWidget<USigilNotificationWidget>(GetWorld(), NotificationWidgetClass);
    if (NotificationWidget)
    {
        ActiveNotifications.Add(NotificationWidget);
        
        UVerticalBoxSlot* NotificationSlot = NotificationsContainer->AddChildToVerticalBox(NotificationWidget);
        if (NotificationSlot)
        {
            NotificationSlot->SetSize(FSlateChildSize(ESlateSizeRule::Automatic));
            NotificationSlot->SetPadding(FMargin(0.0f, 2.0f));
        }

        NotificationWidget->ShowNotification(NotificationData);
    }
}

void USigilHUDWidget::ClearAllNotifications()
{
    for (USigilNotificationWidget* Notification : ActiveNotifications)
    {
        if (Notification)
        {
            Notification->HideNotification();
            Notification->RemoveFromParent();
        }
    }
    ActiveNotifications.Empty();
}

void USigilHUDWidget::RemoveOldestNotification()
{
    if (ActiveNotifications.Num() > 0)
    {
        USigilNotificationWidget* OldestNotification = ActiveNotifications[0];
        if (OldestNotification)
        {
            OldestNotification->HideNotification();
            OldestNotification->RemoveFromParent();
        }
        ActiveNotifications.RemoveAt(0);
    }
}