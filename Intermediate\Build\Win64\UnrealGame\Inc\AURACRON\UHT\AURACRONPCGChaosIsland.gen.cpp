// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGChaosIsland.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGChaosIsland() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AChaosIsland();
AURACRON_API UClass* Z_Construct_UClass_AChaosIsland_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AChaosIsland Function ApplyChaosEffect ***********************************
struct Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics
{
	struct ChaosIsland_eventApplyChaosEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplica efeito ca\xc3\xb3tico ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeito ca\xc3\xb3tico ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventApplyChaosEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "ApplyChaosEffect", Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::ChaosIsland_eventApplyChaosEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::ChaosIsland_eventApplyChaosEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execApplyChaosEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyChaosEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function ApplyChaosEffect *************************************

// ********** Begin Class AChaosIsland Function ApplyEnvironmentalHazardEffect *********************
struct Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics
{
	struct ChaosIsland_eventApplyEnvironmentalHazardEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplica efeitos de perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeitos de perigos ambientais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventApplyEnvironmentalHazardEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "ApplyEnvironmentalHazardEffect", Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::ChaosIsland_eventApplyEnvironmentalHazardEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::ChaosIsland_eventApplyEnvironmentalHazardEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execApplyEnvironmentalHazardEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyEnvironmentalHazardEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function ApplyEnvironmentalHazardEffect ***********************

// ********** Begin Class AChaosIsland Function ApplyUnstableTerrainEffect *************************
struct Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics
{
	struct ChaosIsland_eventApplyUnstableTerrainEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplica efeitos de terreno inst\xc3\xa1vel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeitos de terreno inst\xc3\xa1vel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventApplyUnstableTerrainEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "ApplyUnstableTerrainEffect", Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::ChaosIsland_eventApplyUnstableTerrainEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::ChaosIsland_eventApplyUnstableTerrainEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execApplyUnstableTerrainEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyUnstableTerrainEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function ApplyUnstableTerrainEffect ***************************

// ********** Begin Class AChaosIsland Function GrantHighRiskReward ********************************
struct Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics
{
	struct ChaosIsland_eventGrantHighRiskReward_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede recompensa de alto risco\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede recompensa de alto risco" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventGrantHighRiskReward_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "GrantHighRiskReward", Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::ChaosIsland_eventGrantHighRiskReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::ChaosIsland_eventGrantHighRiskReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execGrantHighRiskReward)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantHighRiskReward(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function GrantHighRiskReward **********************************

// ********** Begin Class AChaosIsland Function RemoveChaosEffects *********************************
struct Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics
{
	struct ChaosIsland_eventRemoveChaosEffects_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove os efeitos ca\xc3\xb3ticos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove os efeitos ca\xc3\xb3ticos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventRemoveChaosEffects_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "RemoveChaosEffects", Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::ChaosIsland_eventRemoveChaosEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::ChaosIsland_eventRemoveChaosEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execRemoveChaosEffects)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveChaosEffects(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function RemoveChaosEffects ***********************************

// ********** Begin Class AChaosIsland Function RemoveEnvironmentalHazardEffects *******************
struct Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics
{
	struct ChaosIsland_eventRemoveEnvironmentalHazardEffects_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove efeitos de perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove efeitos de perigos ambientais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventRemoveEnvironmentalHazardEffects_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "RemoveEnvironmentalHazardEffects", Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::ChaosIsland_eventRemoveEnvironmentalHazardEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::ChaosIsland_eventRemoveEnvironmentalHazardEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execRemoveEnvironmentalHazardEffects)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveEnvironmentalHazardEffects(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function RemoveEnvironmentalHazardEffects *********************

// ********** Begin Class AChaosIsland Function RemoveUnstableTerrainEffects ***********************
struct Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics
{
	struct ChaosIsland_eventRemoveUnstableTerrainEffects_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove efeitos de terreno inst\xc3\xa1vel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove efeitos de terreno inst\xc3\xa1vel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventRemoveUnstableTerrainEffects_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "RemoveUnstableTerrainEffects", Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::ChaosIsland_eventRemoveUnstableTerrainEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::ChaosIsland_eventRemoveUnstableTerrainEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execRemoveUnstableTerrainEffects)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveUnstableTerrainEffects(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function RemoveUnstableTerrainEffects *************************

// ********** Begin Class AChaosIsland Function SetActivityLevel ***********************************
struct Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics
{
	struct ChaosIsland_eventSetActivityLevel_Parms
	{
		float NewActivityLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir n\xc3\xadvel de atividade da ilha (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir n\xc3\xadvel de atividade da ilha (0.0 - 1.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewActivityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::NewProp_NewActivityLevel = { "NewActivityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventSetActivityLevel_Parms, NewActivityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::NewProp_NewActivityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "SetActivityLevel", Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::ChaosIsland_eventSetActivityLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::ChaosIsland_eventSetActivityLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_SetActivityLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_SetActivityLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execSetActivityLevel)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewActivityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityLevel(Z_Param_NewActivityLevel);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function SetActivityLevel *************************************

// ********** Begin Class AChaosIsland Function UpdateEnvironmentalHazards *************************
struct Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics
{
	struct ChaosIsland_eventUpdateEnvironmentalHazards_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualiza os perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza os perigos ambientais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventUpdateEnvironmentalHazards_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "UpdateEnvironmentalHazards", Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::ChaosIsland_eventUpdateEnvironmentalHazards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::ChaosIsland_eventUpdateEnvironmentalHazards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execUpdateEnvironmentalHazards)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateEnvironmentalHazards(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function UpdateEnvironmentalHazards ***************************

// ********** Begin Class AChaosIsland Function UpdateUnstableTerrain ******************************
struct Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics
{
	struct ChaosIsland_eventUpdateUnstableTerrain_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualiza o estado do terreno inst\xc3\xa1vel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza o estado do terreno inst\xc3\xa1vel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ChaosIsland_eventUpdateUnstableTerrain_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AChaosIsland, nullptr, "UpdateUnstableTerrain", Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::PropPointers), sizeof(Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::ChaosIsland_eventUpdateUnstableTerrain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::Function_MetaDataParams), Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::ChaosIsland_eventUpdateUnstableTerrain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AChaosIsland::execUpdateUnstableTerrain)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateUnstableTerrain(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AChaosIsland Function UpdateUnstableTerrain ********************************

// ********** Begin Class AChaosIsland *************************************************************
void AChaosIsland::StaticRegisterNativesAChaosIsland()
{
	UClass* Class = AChaosIsland::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyChaosEffect", &AChaosIsland::execApplyChaosEffect },
		{ "ApplyEnvironmentalHazardEffect", &AChaosIsland::execApplyEnvironmentalHazardEffect },
		{ "ApplyUnstableTerrainEffect", &AChaosIsland::execApplyUnstableTerrainEffect },
		{ "GrantHighRiskReward", &AChaosIsland::execGrantHighRiskReward },
		{ "RemoveChaosEffects", &AChaosIsland::execRemoveChaosEffects },
		{ "RemoveEnvironmentalHazardEffects", &AChaosIsland::execRemoveEnvironmentalHazardEffects },
		{ "RemoveUnstableTerrainEffects", &AChaosIsland::execRemoveUnstableTerrainEffects },
		{ "SetActivityLevel", &AChaosIsland::execSetActivityLevel },
		{ "UpdateEnvironmentalHazards", &AChaosIsland::execUpdateEnvironmentalHazards },
		{ "UpdateUnstableTerrain", &AChaosIsland::execUpdateUnstableTerrain },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AChaosIsland;
UClass* AChaosIsland::GetPrivateStaticClass()
{
	using TClass = AChaosIsland;
	if (!Z_Registration_Info_UClass_AChaosIsland.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("ChaosIsland"),
			Z_Registration_Info_UClass_AChaosIsland.InnerSingleton,
			StaticRegisterNativesAChaosIsland,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AChaosIsland.InnerSingleton;
}
UClass* Z_Construct_UClass_AChaosIsland_NoRegister()
{
	return AChaosIsland::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AChaosIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Chaos Island\n * Ilha com elementos ca\xc3\xb3ticos, v\xc3\xb3rtices de energia e runas antigas\n * \n * Caracter\xc3\xadsticas conforme GDD:\n * - Localiza\xc3\xa7\xc3\xa3o: Em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n * - Caracter\xc3\xadsticas: Perigos ambientais, recompensas de alto risco, terreno inst\xc3\xa1vel\n * - Valor Estrat\xc3\xa9gico: Itens que mudam o jogo com risco significativo\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGChaosIsland.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Chaos Island\nIlha com elementos ca\xc3\xb3ticos, v\xc3\xb3rtices de energia e runas antigas\n\nCaracter\xc3\xadsticas conforme GDD:\n- Localiza\xc3\xa7\xc3\xa3o: Em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n- Caracter\xc3\xadsticas: Perigos ambientais, recompensas de alto risco, terreno inst\xc3\xa1vel\n- Valor Estrat\xc3\xa9gico: Itens que mudam o jogo com risco significativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazardIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade dos perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade dos perigos ambientais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazardDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o dos perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o dos perigos ambientais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardMultiplier_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multiplicador de recompensas de alto risco\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o das recompensas de alto risco\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o das recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainInstabilityIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade da instabilidade do terreno\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da instabilidade do terreno" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainInstabilityDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o da instabilidade do terreno\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o da instabilidade do terreno" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VortexRotationSpeed_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Velocidade de rota\xc3\xa7\xc3\xa3o dos v\xc3\xb3rtices\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de rota\xc3\xa7\xc3\xa3o dos v\xc3\xb3rtices" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RunePulseIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade de pulsa\xc3\xa7\xc3\xa3o das runas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade de pulsa\xc3\xa7\xc3\xa3o das runas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionEnvironments_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ambientes de transi\xc3\xa7\xc3\xa3o para efeitos visuais adaptativos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambientes de transi\xc3\xa7\xc3\xa3o para efeitos visuais adaptativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosSpire_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Espiral central do caos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Espiral central do caos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyVortexes_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// V\xc3\xb3rtices de energia\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "V\xc3\xb3rtices de energia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AncientRunes_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Runas antigas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Runas antigas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HazardZones_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Zonas de perigo ambiental\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Zonas de perigo ambiental" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazards_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Perigos ambientais (alias para compatibilidade)\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perigos ambientais (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnstableTerrainZones_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes de terreno inst\xc3\xa1vel\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de terreno inst\xc3\xa1vel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardZones_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes de recompensas de alto risco\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do efeito ca\xc3\xb3tico\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito ca\xc3\xb3tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do efeito ca\xc3\xb3tico\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito ca\xc3\xb3tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosVisualEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual do caos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual do caos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosGameplayEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para caos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para caos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazardEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para perigos ambientais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnstableTerrainEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para terreno inst\xc3\xa1vel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para terreno inst\xc3\xa1vel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardsTable_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tabela de recompensas de alto risco\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tabela de recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo acumulado para efeitos visuais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado para efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityLevel_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de atividade atual da ilha (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de atividade atual da ilha (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosEnergyEffect_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de efeito de energia ca\xc3\xb3tica */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de efeito de energia ca\xc3\xb3tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosLight_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de luz ca\xc3\xb3tica */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de luz ca\xc3\xb3tica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentalHazardIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentalHazardDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HighRiskRewardMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HighRiskRewardDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerrainInstabilityIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerrainInstabilityDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VortexRotationSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RunePulseIntensity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionEnvironments_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionEnvironments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TransitionEnvironments;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosSpire;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnergyVortexes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnergyVortexes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AncientRunes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AncientRunes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HazardZones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HazardZones;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentalHazards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnvironmentalHazards;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UnstableTerrainZones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnstableTerrainZones;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HighRiskRewardZones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HighRiskRewardZones;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChaosIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChaosDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosVisualEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ChaosGameplayEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_EnvironmentalHazardEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_UnstableTerrainEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HighRiskRewardsTable;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityLevel;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosEnergyEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosLight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AChaosIsland_ApplyChaosEffect, "ApplyChaosEffect" }, // 3622425373
		{ &Z_Construct_UFunction_AChaosIsland_ApplyEnvironmentalHazardEffect, "ApplyEnvironmentalHazardEffect" }, // 3295061864
		{ &Z_Construct_UFunction_AChaosIsland_ApplyUnstableTerrainEffect, "ApplyUnstableTerrainEffect" }, // 2623514897
		{ &Z_Construct_UFunction_AChaosIsland_GrantHighRiskReward, "GrantHighRiskReward" }, // 2735136817
		{ &Z_Construct_UFunction_AChaosIsland_RemoveChaosEffects, "RemoveChaosEffects" }, // 2397445242
		{ &Z_Construct_UFunction_AChaosIsland_RemoveEnvironmentalHazardEffects, "RemoveEnvironmentalHazardEffects" }, // 2161350372
		{ &Z_Construct_UFunction_AChaosIsland_RemoveUnstableTerrainEffects, "RemoveUnstableTerrainEffects" }, // 1660532963
		{ &Z_Construct_UFunction_AChaosIsland_SetActivityLevel, "SetActivityLevel" }, // 3399404498
		{ &Z_Construct_UFunction_AChaosIsland_UpdateEnvironmentalHazards, "UpdateEnvironmentalHazards" }, // 778107941
		{ &Z_Construct_UFunction_AChaosIsland_UpdateUnstableTerrain, "UpdateUnstableTerrain" }, // 923582150
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AChaosIsland>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazardIntensity = { "EnvironmentalHazardIntensity", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, EnvironmentalHazardIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazardIntensity_MetaData), NewProp_EnvironmentalHazardIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazardDuration = { "EnvironmentalHazardDuration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, EnvironmentalHazardDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazardDuration_MetaData), NewProp_EnvironmentalHazardDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardMultiplier = { "HighRiskRewardMultiplier", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, HighRiskRewardMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardMultiplier_MetaData), NewProp_HighRiskRewardMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardDuration = { "HighRiskRewardDuration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, HighRiskRewardDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardDuration_MetaData), NewProp_HighRiskRewardDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_TerrainInstabilityIntensity = { "TerrainInstabilityIntensity", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, TerrainInstabilityIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainInstabilityIntensity_MetaData), NewProp_TerrainInstabilityIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_TerrainInstabilityDuration = { "TerrainInstabilityDuration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, TerrainInstabilityDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainInstabilityDuration_MetaData), NewProp_TerrainInstabilityDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_VortexRotationSpeed = { "VortexRotationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, VortexRotationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VortexRotationSpeed_MetaData), NewProp_VortexRotationSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_RunePulseIntensity = { "RunePulseIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, RunePulseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RunePulseIntensity_MetaData), NewProp_RunePulseIntensity_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_TransitionEnvironments_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_TransitionEnvironments_Inner = { "TransitionEnvironments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_TransitionEnvironments = { "TransitionEnvironments", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, TransitionEnvironments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionEnvironments_MetaData), NewProp_TransitionEnvironments_MetaData) }; // 2509470107
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosSpire = { "ChaosSpire", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, ChaosSpire), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosSpire_MetaData), NewProp_ChaosSpire_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnergyVortexes_Inner = { "EnergyVortexes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnergyVortexes = { "EnergyVortexes", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, EnergyVortexes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyVortexes_MetaData), NewProp_EnergyVortexes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_AncientRunes_Inner = { "AncientRunes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_AncientRunes = { "AncientRunes", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, AncientRunes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AncientRunes_MetaData), NewProp_AncientRunes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_HazardZones_Inner = { "HazardZones", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_HazardZones = { "HazardZones", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, HazardZones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HazardZones_MetaData), NewProp_HazardZones_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazards_Inner = { "EnvironmentalHazards", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazards = { "EnvironmentalHazards", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, EnvironmentalHazards), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazards_MetaData), NewProp_EnvironmentalHazards_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_UnstableTerrainZones_Inner = { "UnstableTerrainZones", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_UnstableTerrainZones = { "UnstableTerrainZones", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, UnstableTerrainZones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnstableTerrainZones_MetaData), NewProp_UnstableTerrainZones_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardZones_Inner = { "HighRiskRewardZones", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardZones = { "HighRiskRewardZones", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, HighRiskRewardZones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardZones_MetaData), NewProp_HighRiskRewardZones_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosIntensity = { "ChaosIntensity", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, ChaosIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosIntensity_MetaData), NewProp_ChaosIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosDuration = { "ChaosDuration", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, ChaosDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosDuration_MetaData), NewProp_ChaosDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosVisualEffect = { "ChaosVisualEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, ChaosVisualEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosVisualEffect_MetaData), NewProp_ChaosVisualEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosGameplayEffect = { "ChaosGameplayEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, ChaosGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosGameplayEffect_MetaData), NewProp_ChaosGameplayEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazardEffect = { "EnvironmentalHazardEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, EnvironmentalHazardEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazardEffect_MetaData), NewProp_EnvironmentalHazardEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_UnstableTerrainEffect = { "UnstableTerrainEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, UnstableTerrainEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnstableTerrainEffect_MetaData), NewProp_UnstableTerrainEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardsTable = { "HighRiskRewardsTable", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, HighRiskRewardsTable), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardsTable_MetaData), NewProp_HighRiskRewardsTable_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_ActivityLevel = { "ActivityLevel", nullptr, (EPropertyFlags)0x0040000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, ActivityLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityLevel_MetaData), NewProp_ActivityLevel_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosEnergyEffect = { "ChaosEnergyEffect", nullptr, (EPropertyFlags)0x00400000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, ChaosEnergyEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosEnergyEffect_MetaData), NewProp_ChaosEnergyEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosLight = { "ChaosLight", nullptr, (EPropertyFlags)0x00400000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChaosIsland, ChaosLight), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosLight_MetaData), NewProp_ChaosLight_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AChaosIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazardIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazardDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_TerrainInstabilityIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_TerrainInstabilityDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_VortexRotationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_RunePulseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_TransitionEnvironments_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_TransitionEnvironments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_TransitionEnvironments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosSpire,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnergyVortexes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnergyVortexes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_AncientRunes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_AncientRunes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_HazardZones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_HazardZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_UnstableTerrainZones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_UnstableTerrainZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardZones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosVisualEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_EnvironmentalHazardEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_UnstableTerrainEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_HighRiskRewardsTable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_AccumulatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_ActivityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosEnergyEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChaosIsland_Statics::NewProp_ChaosLight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AChaosIsland_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AChaosIsland_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APrismalFlowIsland,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AChaosIsland_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AChaosIsland_Statics::ClassParams = {
	&AChaosIsland::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AChaosIsland_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AChaosIsland_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AChaosIsland_Statics::Class_MetaDataParams), Z_Construct_UClass_AChaosIsland_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AChaosIsland()
{
	if (!Z_Registration_Info_UClass_AChaosIsland.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AChaosIsland.OuterSingleton, Z_Construct_UClass_AChaosIsland_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AChaosIsland.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AChaosIsland::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_EnvironmentalHazardIntensity(TEXT("EnvironmentalHazardIntensity"));
	static FName Name_EnvironmentalHazardDuration(TEXT("EnvironmentalHazardDuration"));
	static FName Name_HighRiskRewardMultiplier(TEXT("HighRiskRewardMultiplier"));
	static FName Name_HighRiskRewardDuration(TEXT("HighRiskRewardDuration"));
	static FName Name_TerrainInstabilityIntensity(TEXT("TerrainInstabilityIntensity"));
	static FName Name_TerrainInstabilityDuration(TEXT("TerrainInstabilityDuration"));
	static FName Name_TransitionEnvironments(TEXT("TransitionEnvironments"));
	static FName Name_ChaosIntensity(TEXT("ChaosIntensity"));
	static FName Name_ChaosDuration(TEXT("ChaosDuration"));
	static FName Name_ChaosGameplayEffect(TEXT("ChaosGameplayEffect"));
	static FName Name_EnvironmentalHazardEffect(TEXT("EnvironmentalHazardEffect"));
	static FName Name_UnstableTerrainEffect(TEXT("UnstableTerrainEffect"));
	const bool bIsValid = true
		&& Name_EnvironmentalHazardIntensity == ClassReps[(int32)ENetFields_Private::EnvironmentalHazardIntensity].Property->GetFName()
		&& Name_EnvironmentalHazardDuration == ClassReps[(int32)ENetFields_Private::EnvironmentalHazardDuration].Property->GetFName()
		&& Name_HighRiskRewardMultiplier == ClassReps[(int32)ENetFields_Private::HighRiskRewardMultiplier].Property->GetFName()
		&& Name_HighRiskRewardDuration == ClassReps[(int32)ENetFields_Private::HighRiskRewardDuration].Property->GetFName()
		&& Name_TerrainInstabilityIntensity == ClassReps[(int32)ENetFields_Private::TerrainInstabilityIntensity].Property->GetFName()
		&& Name_TerrainInstabilityDuration == ClassReps[(int32)ENetFields_Private::TerrainInstabilityDuration].Property->GetFName()
		&& Name_TransitionEnvironments == ClassReps[(int32)ENetFields_Private::TransitionEnvironments].Property->GetFName()
		&& Name_ChaosIntensity == ClassReps[(int32)ENetFields_Private::ChaosIntensity].Property->GetFName()
		&& Name_ChaosDuration == ClassReps[(int32)ENetFields_Private::ChaosDuration].Property->GetFName()
		&& Name_ChaosGameplayEffect == ClassReps[(int32)ENetFields_Private::ChaosGameplayEffect].Property->GetFName()
		&& Name_EnvironmentalHazardEffect == ClassReps[(int32)ENetFields_Private::EnvironmentalHazardEffect].Property->GetFName()
		&& Name_UnstableTerrainEffect == ClassReps[(int32)ENetFields_Private::UnstableTerrainEffect].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AChaosIsland"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AChaosIsland);
AChaosIsland::~AChaosIsland() {}
// ********** End Class AChaosIsland ***************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AChaosIsland, AChaosIsland::StaticClass, TEXT("AChaosIsland"), &Z_Registration_Info_UClass_AChaosIsland, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AChaosIsland), 3834643406U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h__Script_AURACRON_345400860(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
