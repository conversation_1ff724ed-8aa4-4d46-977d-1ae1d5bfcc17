// SigilItem.cpp
// Sistema de Sígilos AURACRON - Implementação da Classe Base de Sígilos UE 5.6
// Implementação completa com suporte para GAS, Iris Replication e Niagara VFX

#include "Sigils/SigilItem.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayAbilitySpec.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "GameFramework/Actor.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Sigils/SigilManagerComponent.h"

ASigilItem::ASigilItem()
{
    // Configurar replicação
    PrimaryActorTick.bCanEverTick = false;
    bReplicates = true;
    bAlwaysRelevant = true;
    SetNetUpdateFrequency(10.0f);
    SetMinNetUpdateFrequency(2.0f);
    
    // Configurar Iris Push Model (UE 5.6)
    SetReplicateMovement(false);

    // Criar componentes
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
    MeshComponent->SetupAttachment(RootComponent);
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    MeshComponent->SetGenerateOverlapEvents(false);

    CollisionComponent = CreateDefaultSubobject<USphereComponent>(TEXT("CollisionComponent"));
    CollisionComponent->SetupAttachment(RootComponent);
    CollisionComponent->SetSphereRadius(50.0f);
    CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    CollisionComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
    CollisionComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
    
    VFXComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("VFXComponent"));
    VFXComponent->SetupAttachment(RootComponent);
    VFXComponent->SetAutoActivate(false);
    
    // Criar Ability System Component
    AbilitySystemComponent = CreateDefaultSubobject<UAbilitySystemComponent>(TEXT("AbilitySystemComponent"));
    AbilitySystemComponent->SetIsReplicated(true);
    AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Mixed);
    
    // Inicializar dados do sigilo
    SigilData = FSigilData();
    EquippedOwner = nullptr;
    OwningActor = nullptr;
    SlotIndex = -1;
    SigilLevel = 1;
    ExperiencePoints = 0.0f;
    bIsEquipped = false;
    bIsFused = false;
    LastReforgeTime = 0.0f;

    // Configurar tags padrão
    SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Offensive"));
    SigilData.Rarity = ESigilRarity::Common;
    SigilData.SigilRarity = ESigilRarity::Common;
    SigilData.State = ESigilState::Unequipped;
    SigilData.SigilState = ESigilState::Unequipped;
}

void ASigilItem::BeginPlay()
{
    Super::BeginPlay();
    
    if (HasAuthority())
    {
        // Inicializar Ability System Component
        if (AbilitySystemComponent)
        {
            AbilitySystemComponent->InitAbilityActorInfo(this, this);

            // Aplicar efeitos passivos se equipado
            if (IsEquipped())
            {
                ApplyPassiveEffects();
            }
        }

        // Inicializar VFX
        InitializeVFXComponent();

        // Configurar validação periódica
        GetWorld()->GetTimerManager().SetTimer(
            ValidationTimerHandle,
            [this]()
            {
                ValidateSigilState();
            },
            5.0f,
            true
        );
    }
    
    // Atualizar visual baseado no estado atual
    UpdateVFXBasedOnState();
}

void ASigilItem::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    if (HasAuthority())
    {
        // Limpar timers
        GetWorld()->GetTimerManager().ClearTimer(ValidationTimerHandle);

        // Remover efeitos se equipado
        if (bIsEquipped && AbilitySystemComponent)
        {
            RemovePassiveEffects();
        }
    }
    
    Super::EndPlay(EndPlayReason);
}

void ASigilItem::PostInitializeComponents()
{
    Super::PostInitializeComponents();

    if (HasAuthority() && AbilitySystemComponent)
    {
        // Configurar delegates do Ability System (usando delegates corretos do UE 5.6)
        AbilitySystemComponent->OnGameplayEffectAppliedDelegateToSelf.AddUObject(this, &ASigilItem::OnGameplayEffectApplied);
        AbilitySystemComponent->OnAnyGameplayEffectRemovedDelegate().AddUObject(this, &ASigilItem::OnGameplayEffectRemoved);
    }
}

void ASigilItem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar dados principais
    DOREPLIFETIME(ASigilItem, SigilData);
    DOREPLIFETIME(ASigilItem, EquippedOwner);
    DOREPLIFETIME(ASigilItem, SlotIndex);
    DOREPLIFETIME(ASigilItem, SigilLevel);
    DOREPLIFETIME(ASigilItem, ExperiencePoints);
    DOREPLIFETIME(ASigilItem, bIsEquipped);
    DOREPLIFETIME(ASigilItem, bIsFused);
    DOREPLIFETIME(ASigilItem, LastReforgeTime);

    // Configurar condições de replicação para otimização
    DOREPLIFETIME_CONDITION(ASigilItem, SigilData, COND_None);
}

UAbilitySystemComponent* ASigilItem::GetAbilitySystemComponent() const
{
    return AbilitySystemComponent;
}

// ========================================
// SISTEMA DE EQUIPAMENTO
// ========================================

bool ASigilItem::EquipToActor(AActor* TargetActor, int32 TargetSlotIndex)
{
    if (!HasAuthority())
    {
        ServerEquipToActor(TargetActor, TargetSlotIndex);
        return false;
    }
    
    if (!CanEquipToActor(TargetActor))
    {
        return false;
    }
    
    // Verificar se há um SigilManagerComponent
    if (USigilManagerComponent* SigilManager = TargetActor->FindComponentByClass<USigilManagerComponent>())
    {
        // Desequipar de ator anterior se necessário
        if (IsEquipped() && EquippedOwner)
        {
            UnequipFromActor();
        }

        // Equipar no novo ator
        EquippedOwner = TargetActor;
        SlotIndex = TargetSlotIndex;
        SigilData.State = ESigilState::Equipped;

        // Aplicar efeitos passivos
        ApplyPassiveEffects();

        // Efeitos visuais
        MulticastPlayEquipVFX();

        UE_LOG(LogTemp, Log, TEXT("Sigil %s equipped to %s in slot %d"),
               *SigilData.SigilName.ToString(),
               *TargetActor->GetName(),
               TargetSlotIndex);

        return true;
    }
    
    return false;
}

bool ASigilItem::UnequipFromActor()
{
    if (!HasAuthority())
    {
        ServerUnequipFromActor();
        return false;
    }
    
    if (!CanUnequip())
    {
        return false;
    }
    
    // Remover efeitos
    RemovePassiveEffects();
    RemoveFusionEffects();

    // Resetar estado
    EquippedOwner = nullptr;
    SlotIndex = -1;
    SigilData.State = ESigilState::Unequipped;
    
    // Atualizar VFX
    UpdateVFXBasedOnState();
    
    UE_LOG(LogTemp, Log, TEXT("Sigil %s unequipped"), *SigilData.SigilName.ToString());
    
    return true;
}

bool ASigilItem::CanEquipToActor(AActor* TargetActor) const
{
    if (!TargetActor)
    {
        return false;
    }
    
    // Verificar se o ator tem SigilManagerComponent
    USigilManagerComponent* SigilManager = TargetActor->FindComponentByClass<USigilManagerComponent>();
    if (!SigilManager)
    {
        return false;
    }

    // Verificar se o sigilo pode ser equipado (validações básicas)
    if (SigilData.State == ESigilState::Fused)
    {
        return false; // Não pode equipar sigilo já fundido
    }
    
    return true;
}

bool ASigilItem::CanUnequip() const
{
    // Não pode desequipar se estiver em fusão ativa
    if (IsFused())
    {
        return false;
    }

    return IsEquipped();
}

// ========================================
// SISTEMA DE FUSÃO
// ========================================

bool ASigilItem::TriggerFusion()
{
    if (!HasAuthority())
    {
        ServerTriggerFusion();
        return false;
    }

    if (!CanFuse())
    {
        return false;
    }

    // Verificar se há outros sígilos compatíveis equipados
    if (!OwningActor)
    {
        return false;
    }

    USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>();
    if (!SigilManager)
    {
        return false;
    }

    // Tentar fusão automática
    if (SigilManager->TriggerFusionForSigil(this))
    {
        bIsFused = true;
        SigilData.SigilState = ESigilState::Fused;

        // Aplicar efeitos de fusão
        ApplyFusionEffects();

        // Efeitos visuais
        MulticastPlayFusionVFX();

        UE_LOG(LogTemp, Log, TEXT("Sigil %s fusion triggered"), *SigilData.SigilName.ToString());
        return true;
    }

    return false;
}

bool ASigilItem::CanFuse() const
{
    if (!bIsEquipped || bIsFused)
    {
        return false;
    }

    // Verificar nível mínimo para fusão
    if (SigilData.CurrentLevel < 2)
    {
        return false;
    }

    // Verificar se há sígilos compatíveis
    if (OwningActor)
    {
        if (USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>())
        {
            return SigilManager->HasCompatibleSigilsForFusion(const_cast<ASigilItem*>(this));
        }
    }

    return false;
}

void ASigilItem::ApplyFusionEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Aplicar efeitos de fusão baseados no tipo e raridade
    for (const TSubclassOf<UGameplayEffect>& FusionEffect : SigilData.FusionEffects)
    {
        if (FusionEffect)
        {
            FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            FGameplayEffectSpecHandle EffectSpec = AbilitySystemComponent->MakeOutgoingSpec(
                FusionEffect,
                SigilData.CurrentLevel,
                EffectContext
            );

            if (EffectSpec.IsValid())
            {
                FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
                ActiveFusionEffects.Add(EffectHandle);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Applied fusion effects for sigil %s"), *SigilData.SigilName.ToString());
}

void ASigilItem::RemoveFusionEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Remover todos os efeitos de fusão ativos
    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveFusionEffects)
    {
        if (EffectHandle.IsValid())
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
        }
    }

    ActiveFusionEffects.Empty();
    bIsFused = false;

    if (bIsEquipped)
    {
        SigilData.SigilState = ESigilState::Equipped;
    }
    else
    {
        SigilData.SigilState = ESigilState::Unequipped;
    }

    UE_LOG(LogTemp, Log, TEXT("Removed fusion effects for sigil %s"), *SigilData.SigilName.ToString());
}

// ========================================
// SISTEMA DE REFORGE
// ========================================

bool ASigilItem::ReforgeProperties()
{
    if (!HasAuthority())
    {
        ServerReforgeProperties();
        return false;
    }

    if (!CanReforge())
    {
        return false;
    }

    // Verificar recursos necessários
    if (OwningActor)
    {
        if (USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>())
        {
            if (!SigilManager->CanAffordReforge(this))
            {
                return false;
            }

            // Consumir recursos
            SigilManager->ConsumeReforgeResources(this);
        }
    }

    // Remover efeitos atuais temporariamente
    if (bIsEquipped)
    {
        RemovePassiveEffects();
        RemoveFusionEffects();
    }

    // Regenerar propriedades do sigilo
    RegenerateSigilProperties();

    // Reaplicar efeitos com novas propriedades
    if (bIsEquipped)
    {
        ApplyPassiveEffects();
        if (bIsFused)
        {
            ApplyFusionEffects();
        }
    }

    // Atualizar timestamp
    LastReforgeTime = GetWorld()->GetTimeSeconds();

    // Efeitos visuais
    UpdateRarityGlow();

    UE_LOG(LogTemp, Log, TEXT("Sigil %s reforged successfully"), *SigilData.SigilName.ToString());
    return true;
}

bool ASigilItem::CanReforge() const
{
    if (!bIsEquipped)
    {
        return false;
    }

    // Verificar cooldown
    const float CurrentTime = GetWorld()->GetTimeSeconds();
    const float TimeSinceLastReforge = CurrentTime - LastReforgeTime;
    const float ReforgeCooldown = 120.0f; // 2 minutos

    if (TimeSinceLastReforge < ReforgeCooldown)
    {
        return false;
    }

    // Verificar se não está em fusão
    if (bIsFused)
    {
        return false;
    }

    // Verificar recursos disponíveis
    if (OwningActor)
    {
        if (USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>())
        {
            return SigilManager->CanAffordReforge(const_cast<ASigilItem*>(this));
        }
    }

    return true;
}

// ========================================
// SISTEMA DE EXPERIÊNCIA E LEVEL
// ========================================

void ASigilItem::AddExperience(float ExperienceAmount)
{
    if (!HasAuthority() || ExperienceAmount <= 0.0f)
    {
        return;
    }

    SigilData.CurrentExperience += ExperienceAmount;

    // Verificar se pode subir de nível
    while (CanLevelUp())
    {
        LevelUp();
    }

    UE_LOG(LogTemp, Log, TEXT("Added %f experience to sigil %s (Total: %f)"),
           ExperienceAmount,
           *SigilData.SigilName.ToString(),
           SigilData.CurrentExperience);
}

bool ASigilItem::CanLevelUp() const
{
    if (SigilData.CurrentLevel >= SigilData.MaxLevel)
    {
        return false;
    }

    const float RequiredExp = GetExperienceToNextLevel();
    return SigilData.CurrentExperience >= RequiredExp;
}

void ASigilItem::LevelUp()
{
    if (!HasAuthority() || !CanLevelUp())
    {
        return;
    }

    // Remover efeitos atuais
    if (bIsEquipped)
    {
        RemovePassiveEffects();
        if (bIsFused)
        {
            RemoveFusionEffects();
        }
    }

    // Aumentar nível
    SigilData.CurrentLevel++;

    // Consumir experiência necessária
    const float ExpUsed = GetExperienceToNextLevel();
    SigilData.CurrentExperience -= ExpUsed;

    // Melhorar propriedades baseado no nível
    ImprovePropertiesOnLevelUp();

    // Reaplicar efeitos com propriedades melhoradas
    if (bIsEquipped)
    {
        ApplyPassiveEffects();
        if (bIsFused)
        {
            ApplyFusionEffects();
        }
    }

    // Efeitos visuais
    PlayLevelUpVFX();

    UE_LOG(LogTemp, Warning, TEXT("Sigil %s leveled up to level %d!"),
           *SigilData.SigilName.ToString(),
           SigilData.CurrentLevel);
}

float ASigilItem::GetExperienceToNextLevel() const
{
    if (SigilData.CurrentLevel >= SigilData.MaxLevel)
    {
        return 0.0f;
    }

    // Fórmula exponencial para experiência necessária
    const float BaseExp = 100.0f;
    const float Multiplier = 1.5f;
    return BaseExp * FMath::Pow(Multiplier, SigilData.CurrentLevel - 1);
}

// ========================================
// SISTEMA DE PODER E BÔNUS
// ========================================

float ASigilItem::GetTotalSpectralPower() const
{
    float TotalPower = SigilData.BasePower;

    // Multiplicador por nível
    TotalPower *= (1.0f + (SigilData.CurrentLevel - 1) * 0.1f);

    // Multiplicador por raridade
    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            TotalPower *= 1.0f;
            break;
        case ESigilRarity::Rare:
            TotalPower *= 1.5f;
            break;
        case ESigilRarity::Epic:
            TotalPower *= 2.0f;
            break;
        case ESigilRarity::Legendary:
            TotalPower *= 3.0f;
            break;
        default:
            TotalPower *= 1.0f;
            break;
    }

    // Bônus de fusão
    if (bIsFused)
    {
        TotalPower *= 1.25f;
    }

    return TotalPower;
}

float ASigilItem::GetEffectiveBonus(const FString& BonusType) const
{
    float BaseBonus = 0.0f;

    // Buscar bônus nas propriedades
    for (const FSigilProperty& Property : SigilData.Properties)
    {
        if (Property.PropertyName.ToString().Contains(BonusType))
        {
            BaseBonus += Property.Value;
        }
    }

    // Aplicar multiplicadores
    float EffectiveBonus = BaseBonus;

    // Multiplicador por nível
    EffectiveBonus *= (1.0f + (SigilData.CurrentLevel - 1) * 0.05f);

    // Multiplicador por fusão
    if (bIsFused)
    {
        EffectiveBonus *= 1.15f;
    }

    return EffectiveBonus;
}

// ========================================
// SISTEMA VFX
// ========================================

void ASigilItem::PlayEquipVFX()
{
    if (HasAuthority())
    {
        MulticastPlayEquipVFX();
    }
}

void ASigilItem::PlayFusionVFX()
{
    if (HasAuthority())
    {
        MulticastPlayFusionVFX();
    }
}

void ASigilItem::PlayAuraVFX(bool bActivate)
{
    if (!VFXComponent)
    {
        return;
    }

    if (bActivate && SigilData.AuraVFX.IsValid())
    {
        if (UNiagaraSystem* AuraSystem = SigilData.AuraVFX.LoadSynchronous())
        {
            VFXComponent->SetAsset(AuraSystem);
            VFXComponent->Activate(true);
        }
    }
    else
    {
        VFXComponent->Deactivate();
    }
}

void ASigilItem::UpdateRarityGlow()
{
    if (HasAuthority())
    {
        MulticastUpdateRarityGlow();
    }
}

void ASigilItem::PlayLevelUpVFX()
{
    if (SigilData.LevelUpVFX.IsValid())
    {
        if (UNiagaraSystem* LevelUpSystem = SigilData.LevelUpVFX.LoadSynchronous())
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                LevelUpSystem,
                GetActorLocation(),
                GetActorRotation()
            );
        }
    }
}

// ========================================
// FUNÇÕES DE REDE (SERVER)
// ========================================

void ASigilItem::ServerEquipToActor_Implementation(AActor* TargetActor, int32 TargetSlotIndex)
{
    EquipToActor(TargetActor, TargetSlotIndex);
}

void ASigilItem::ServerUnequipFromActor_Implementation()
{
    UnequipFromActor();
}

void ASigilItem::ServerTriggerFusion_Implementation()
{
    TriggerFusion();
}

void ASigilItem::ServerReforgeProperties_Implementation()
{
    ReforgeProperties();
}

// ========================================
// FUNÇÕES DE REDE (MULTICAST)
// ========================================

void ASigilItem::MulticastPlayEquipVFX_Implementation()
{
    if (SigilData.EquipVFX.IsValid())
    {
        if (UNiagaraSystem* EquipSystem = SigilData.EquipVFX.LoadSynchronous())
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                EquipSystem,
                GetActorLocation(),
                GetActorRotation()
            );
        }
    }

    // Som de equipar
    if (SigilData.EquipSound.IsValid())
    {
        if (USoundBase* EquipSoundAsset = SigilData.EquipSound.LoadSynchronous())
        {
            UGameplayStatics::PlaySoundAtLocation(
                GetWorld(),
                EquipSoundAsset,
                GetActorLocation()
            );
        }
    }
}

void ASigilItem::MulticastPlayFusionVFX_Implementation()
{
    if (SigilData.FusionVFX.IsValid())
    {
        if (UNiagaraSystem* FusionSystem = SigilData.FusionVFX.LoadSynchronous())
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                FusionSystem,
                GetActorLocation(),
                GetActorRotation()
            );
        }
    }

    // Som de fusão
    if (SigilData.FusionSound.IsValid())
    {
        if (USoundBase* FusionSoundAsset = SigilData.FusionSound.LoadSynchronous())
        {
            UGameplayStatics::PlaySoundAtLocation(
                GetWorld(),
                FusionSoundAsset,
                GetActorLocation()
            );
        }
    }
}

void ASigilItem::MulticastUpdateRarityGlow_Implementation()
{
    UpdateVFXBasedOnState();
}

// ========================================
// REPLICAÇÃO
// ========================================

void ASigilItem::OnRep_SigilData()
{
    // Atualizar visual quando dados replicarem
    UpdateVFXBasedOnState();

    // Atualizar mesh se necessário
    if (SigilData.SigilMesh.IsValid() && MeshComponent)
    {
        if (UStaticMesh* MeshAsset = SigilData.SigilMesh.LoadSynchronous())
        {
            MeshComponent->SetStaticMesh(MeshAsset);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("SigilData replicated for %s"), *SigilData.SigilName.ToString());
}

// ========================================
// MÉTODOS INTERNOS
// ========================================

void ASigilItem::ApplyPassiveEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Aplicar efeitos passivos
    for (const TSubclassOf<UGameplayEffect>& PassiveEffect : SigilData.PassiveEffects)
    {
        if (PassiveEffect)
        {
            FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            FGameplayEffectSpecHandle EffectSpec = AbilitySystemComponent->MakeOutgoingSpec(
                PassiveEffect,
                SigilData.CurrentLevel,
                EffectContext
            );

            if (EffectSpec.IsValid())
            {
                FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
                ActivePassiveEffects.Add(EffectHandle);
            }
        }
    }

    // Aplicar efeito único se existir
    ApplyUniqueEffect();
}

void ASigilItem::RemovePassiveEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Remover efeitos passivos
    for (const FActiveGameplayEffectHandle& EffectHandle : ActivePassiveEffects)
    {
        if (EffectHandle.IsValid())
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
        }
    }

    ActivePassiveEffects.Empty();

    // Remover efeito único
    RemoveUniqueEffect();
}

void ASigilItem::ApplyUniqueEffect()
{
    if (!HasAuthority() || !AbilitySystemComponent || !SigilData.UniqueEffect)
    {
        return;
    }

    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    FGameplayEffectSpecHandle EffectSpec = AbilitySystemComponent->MakeOutgoingSpec(
        SigilData.UniqueEffect,
        SigilData.CurrentLevel,
        EffectContext
    );

    if (EffectSpec.IsValid())
    {
        ActiveUniqueEffect = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
    }
}

void ASigilItem::RemoveUniqueEffect()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    if (ActiveUniqueEffect.IsValid())
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(ActiveUniqueEffect);
        ActiveUniqueEffect = FActiveGameplayEffectHandle();
    }
}

void ASigilItem::InitializeVFXComponent()
{
    if (!VFXComponent)
    {
        return;
    }

    // Configurar VFX baseado na raridade
    UpdateVFXBasedOnState();
}

void ASigilItem::UpdateVFXBasedOnState()
{
    if (!VFXComponent)
    {
        return;
    }

    // Escolher VFX baseado no estado atual
    UNiagaraSystem* TargetVFX = nullptr;

    if (bIsFused && SigilData.FusionAuraVFX.IsValid())
    {
        TargetVFX = SigilData.FusionAuraVFX.LoadSynchronous();
    }
    else if (bIsEquipped && SigilData.AuraVFX.IsValid())
    {
        TargetVFX = SigilData.AuraVFX.LoadSynchronous();
    }
    else if (SigilData.IdleVFX.IsValid())
    {
        TargetVFX = SigilData.IdleVFX.LoadSynchronous();
    }

    if (TargetVFX)
    {
        VFXComponent->SetAsset(TargetVFX);
        VFXComponent->Activate(true);

        // Configurar cor baseada na raridade
        SetVFXRarityColor();
    }
    else
    {
        VFXComponent->Deactivate();
    }
}

void ASigilItem::SetVFXRarityColor()
{
    if (!VFXComponent)
    {
        return;
    }

    FLinearColor RarityColor = FLinearColor::White;

    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            RarityColor = FLinearColor::Gray;
            break;
        case ESigilRarity::Rare:
            RarityColor = FLinearColor::Blue;
            break;
        case ESigilRarity::Epic:
            RarityColor = FLinearColor(0.5f, 0.0f, 1.0f); // Roxo
            break;
        case ESigilRarity::Legendary:
            RarityColor = FLinearColor(1.0f, 0.5f, 0.0f); // Laranja
            break;
        default:
            RarityColor = FLinearColor::White;
            break;
    }

    VFXComponent->SetColorParameter(FName("RarityColor"), RarityColor);
}

void ASigilItem::ValidateSigilState()
{
    if (!HasAuthority())
    {
        return;
    }

    // Validar consistência do estado
    if (bIsEquipped && !OwningActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s marked as equipped but has no owner - fixing"), *SigilData.SigilName.ToString());
        bIsEquipped = false;
        SigilData.SigilState = ESigilState::Unequipped;
    }

    if (!bIsEquipped && OwningActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s has owner but not marked as equipped - fixing"), *SigilData.SigilName.ToString());
        OwningActor = nullptr;
        SlotIndex = -1;
    }

    // Validar fusão
    if (bIsFused && !bIsEquipped)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s marked as fused but not equipped - removing fusion"), *SigilData.SigilName.ToString());
        RemoveFusionEffects();
    }
}

void ASigilItem::RegenerateSigilProperties()
{
    // Regenerar propriedades baseado na raridade e nível
    SigilData.Properties.Empty();

    const int32 NumProperties = GetNumPropertiesForRarity(SigilData.SigilRarity);

    for (int32 i = 0; i < NumProperties; i++)
    {
        FSigilProperty NewProperty = GenerateRandomProperty();
        SigilData.Properties.Add(NewProperty);
    }

    // Recalcular poder base
    SigilData.BasePower = CalculateBasePowerFromProperties();
}

void ASigilItem::RegenerateSigilData()
{
    if (!HasAuthority())
    {
        return;
    }
    
    // Regenerar todas as propriedades do sigilo
    RegenerateSigilProperties();
    
    // Atualizar tags baseado no tipo e raridade
    UpdateGameplayTags();
    
    // Recalcular bônus espectrais
    RecalculateSpectralBonuses();
    
    // Invalidar cache de poder
    bSpectralPowerCacheDirty = true;
    
    // Atualizar VFX baseado no novo estado
    UpdateVFXBasedOnState();
    
    // Forçar replicação dos dados atualizados
    ForceNetUpdate();
    
    UE_LOG(LogTemp, Log, TEXT("Sigil data regenerated for %s"), *SigilData.SigilName.ToString());
}

void ASigilItem::UpdateGameplayTags()
{
    // Atualizar tags baseado no tipo e raridade do sigilo
    FString TagPrefix = "Sigil.";
    
    // Adicionar tag de tipo
    switch (SigilData.SigilType)
    {
        case ESigilType::Damage:
            SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "Type.Offensive")));
            break;
        case ESigilType::Tank:
            SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "Type.Defensive")));
            break;
        case ESigilType::Utility:
            SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "Type.Utility")));
            break;
        case ESigilType::None:
        default:
            SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "Type.None")));
            break;
    }
    
    // Adicionar tag de raridade
    FString RarityTagStr = TagPrefix + "Rarity.";
    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            RarityTagStr += "Common";
            break;
        case ESigilRarity::Rare:
            RarityTagStr += "Rare";
            break;
        case ESigilRarity::Epic:
            RarityTagStr += "Epic";
            break;
        case ESigilRarity::Legendary:
            RarityTagStr += "Legendary";
            break;
        default:
            RarityTagStr += "Common";
            break;
    }
    
    SigilData.RarityTag = FGameplayTag::RequestGameplayTag(FName(*RarityTagStr));
    
    UE_LOG(LogTemp, Verbose, TEXT("Updated gameplay tags for sigil %s: Type=%s, Rarity=%s"), 
           *SigilData.SigilName.ToString(),
           *SigilData.SigilTag.ToString(),
           *SigilData.RarityTag.ToString());
}

void ASigilItem::RecalculateSpectralBonuses()
{
    // Recalcular todos os bônus espectrais baseados nas propriedades atuais
    SigilData.SpectralBonuses.Empty();
    
    // Agrupar propriedades por tipo
    TMap<FName, float> BonusesByType;
    
    for (const FSigilProperty& Property : SigilData.Properties)
    {
        float CurrentValue = 0.0f;
        if (BonusesByType.Contains(Property.PropertyName))
        {
            CurrentValue = BonusesByType[Property.PropertyName];
        }
        
        // Adicionar valor baseado no tipo de propriedade
        if (Property.PropertyType == ESigilPropertyType::Additive)
        {
            BonusesByType.Add(Property.PropertyName, CurrentValue + Property.Value);
        }
        else if (Property.PropertyType == ESigilPropertyType::Multiplicative)
        {
            BonusesByType.Add(Property.PropertyName, CurrentValue * (1.0f + Property.Value));
        }
    }
    
    // Converter para array de bônus espectrais
    for (const TPair<FName, float>& Bonus : BonusesByType)
    {
        FSigilSpectralBonus SpectralBonus;
        SpectralBonus.BonusName = Bonus.Key;
        SpectralBonus.BonusValue = Bonus.Value;
        
        // Aplicar modificador baseado no nível
        SpectralBonus.BonusValue *= (1.0f + (SigilData.CurrentLevel - 1) * 0.05f);
        
        SigilData.SpectralBonuses.Add(SpectralBonus);
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("Recalculated %d spectral bonuses for sigil %s"),
           SigilData.SpectralBonuses.Num(),
           *SigilData.SigilName.ToString());
}

int32 ASigilItem::GetNumPropertiesForRarity(ESigilRarity Rarity) const
{
    switch (Rarity)
    {
        case ESigilRarity::Common:
            return FMath::RandRange(1, 2);
        case ESigilRarity::Rare:
            return FMath::RandRange(3, 4);
        case ESigilRarity::Epic:
            return FMath::RandRange(4, 5);
        case ESigilRarity::Legendary:
            return FMath::RandRange(5, 6);
        default:
            return 1;
    }
}

FSigilProperty ASigilItem::GenerateRandomProperty() const
{
    FSigilProperty Property;

    // Lista de propriedades possíveis
    const TArray<FString> PropertyNames = {
        TEXT("Damage"),
        TEXT("CriticalChance"),
        TEXT("CriticalDamage"),
        TEXT("AttackSpeed"),
        TEXT("Health"),
        TEXT("Mana"),
        TEXT("Resistance"),
        TEXT("Penetration"),
        TEXT("LifeSteal"),
        TEXT("SpellVamp")
    };

    // Escolher propriedade aleatória
    const FString& PropertyName = PropertyNames[FMath::RandRange(0, PropertyNames.Num() - 1)];
    Property.PropertyName = FName(*PropertyName);

    // Gerar valor baseado na raridade
    float BaseValue = FMath::RandRange(1.0f, 10.0f);
    float RarityMultiplier = 1.0f;

    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            RarityMultiplier = 1.0f;
            break;
        case ESigilRarity::Rare:
            RarityMultiplier = 1.5f;
            break;
        case ESigilRarity::Epic:
            RarityMultiplier = 2.0f;
            break;
        case ESigilRarity::Legendary:
            RarityMultiplier = 3.0f;
            break;
        default:
            RarityMultiplier = 1.0f;
            break;
    }

    Property.Value = BaseValue * RarityMultiplier;
    Property.PropertyType = ESigilPropertyType::Additive;

    return Property;
}

float ASigilItem::CalculateBasePowerFromProperties() const
{
    float TotalPower = 0.0f;

    for (const FSigilProperty& Property : SigilData.Properties)
    {
        TotalPower += Property.Value * 0.5f; // Cada propriedade contribui com metade do seu valor
    }

    return FMath::Max(TotalPower, 10.0f); // Mínimo de 10 de poder
}

void ASigilItem::ImprovePropertiesOnLevelUp()
{
    // Melhorar todas as propriedades em 5% por nível
    const float ImprovementRate = 0.05f;

    for (FSigilProperty& Property : SigilData.Properties)
    {
        Property.Value *= (1.0f + ImprovementRate);
    }

    // Recalcular poder base
    SigilData.BasePower = CalculateBasePowerFromProperties();
}

void ASigilItem::OnGameplayEffectApplied(UAbilitySystemComponent* ASC, const FGameplayEffectSpec& Spec, FActiveGameplayEffectHandle Handle)
{
    // Callback quando um efeito é aplicado
    UE_LOG(LogTemp, Verbose, TEXT("GameplayEffect applied to sigil %s"), *SigilData.SigilName.ToString());
}

void ASigilItem::OnGameplayEffectRemoved(const FActiveGameplayEffect& Effect)
{
    // Callback quando um efeito é removido
    UE_LOG(LogTemp, Verbose, TEXT("GameplayEffect removed from sigil %s"), *SigilData.SigilName.ToString());
}

// ========================================
// MÉTODOS BLUEPRINT EXPOSTOS (Implementações não inline)
// ========================================

// Métodos inline estão implementados no header file
// Implementações complexas que não podem ser inline ficam aqui
