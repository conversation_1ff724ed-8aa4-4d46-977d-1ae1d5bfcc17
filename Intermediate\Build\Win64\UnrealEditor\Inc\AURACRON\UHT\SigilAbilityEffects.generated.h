// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Sigils/SigilAbilityEffects.h"

#ifdef AURACRON_SigilAbilityEffects_generated_h
#error "SigilAbilityEffects.generated.h already included, missing '#pragma once' in SigilAbilityEffects.h"
#endif
#define AURACRON_SigilAbilityEffects_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"
#include "Net/Core/PushModel/PushModelMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UGameplayEffect;
struct FGameplayAttributeData;

// ********** Begin Class USigilAbilityAttributeSet ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_60_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_OutgoingDamage); \
	DECLARE_FUNCTION(execOnRep_IncomingDamage); \
	DECLARE_FUNCTION(execOnRep_MaxShieldAmount); \
	DECLARE_FUNCTION(execOnRep_ShieldAmount); \
	DECLARE_FUNCTION(execOnRep_DamageMultiplier); \
	DECLARE_FUNCTION(execOnRep_BarrierProtection);


AURACRON_API UClass* Z_Construct_UClass_USigilAbilityAttributeSet_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_60_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAbilityAttributeSet(); \
	friend struct Z_Construct_UClass_USigilAbilityAttributeSet_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAbilityAttributeSet_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAbilityAttributeSet, UAttributeSet, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAbilityAttributeSet_NoRegister) \
	DECLARE_SERIALIZER(USigilAbilityAttributeSet) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		BarrierProtection=NETFIELD_REP_START, \
		DamageMultiplier, \
		ShieldAmount, \
		MaxShieldAmount, \
		IncomingDamage, \
		OutgoingDamage, \
		NETFIELD_REP_END=OutgoingDamage	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API) \
private: \
	REPLICATED_BASE_CLASS(USigilAbilityAttributeSet) \
public:


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_60_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAbilityAttributeSet(USigilAbilityAttributeSet&&) = delete; \
	USigilAbilityAttributeSet(const USigilAbilityAttributeSet&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAbilityAttributeSet); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAbilityAttributeSet); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilAbilityAttributeSet) \
	NO_API virtual ~USigilAbilityAttributeSet();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_57_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_60_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_60_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_60_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_60_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAbilityAttributeSet;

// ********** End Class USigilAbilityAttributeSet **************************************************

// ********** Begin Class USigilBarrierProtectionCalculation ***************************************
AURACRON_API UClass* Z_Construct_UClass_USigilBarrierProtectionCalculation_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_127_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilBarrierProtectionCalculation(); \
	friend struct Z_Construct_UClass_USigilBarrierProtectionCalculation_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilBarrierProtectionCalculation_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilBarrierProtectionCalculation, UGameplayEffectExecutionCalculation, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilBarrierProtectionCalculation_NoRegister) \
	DECLARE_SERIALIZER(USigilBarrierProtectionCalculation)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_127_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilBarrierProtectionCalculation(USigilBarrierProtectionCalculation&&) = delete; \
	USigilBarrierProtectionCalculation(const USigilBarrierProtectionCalculation&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilBarrierProtectionCalculation); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilBarrierProtectionCalculation); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilBarrierProtectionCalculation) \
	NO_API virtual ~USigilBarrierProtectionCalculation();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_124_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_127_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_127_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_127_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilBarrierProtectionCalculation;

// ********** End Class USigilBarrierProtectionCalculation *****************************************

// ********** Begin Class USigilDamageBuffCalculation **********************************************
AURACRON_API UClass* Z_Construct_UClass_USigilDamageBuffCalculation_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_146_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilDamageBuffCalculation(); \
	friend struct Z_Construct_UClass_USigilDamageBuffCalculation_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilDamageBuffCalculation_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilDamageBuffCalculation, UGameplayEffectExecutionCalculation, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilDamageBuffCalculation_NoRegister) \
	DECLARE_SERIALIZER(USigilDamageBuffCalculation)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_146_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilDamageBuffCalculation(USigilDamageBuffCalculation&&) = delete; \
	USigilDamageBuffCalculation(const USigilDamageBuffCalculation&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilDamageBuffCalculation); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilDamageBuffCalculation); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilDamageBuffCalculation) \
	NO_API virtual ~USigilDamageBuffCalculation();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_143_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_146_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_146_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_146_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilDamageBuffCalculation;

// ********** End Class USigilDamageBuffCalculation ************************************************

// ********** Begin Class USigilShieldCalculation **************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilShieldCalculation_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_165_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilShieldCalculation(); \
	friend struct Z_Construct_UClass_USigilShieldCalculation_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilShieldCalculation_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilShieldCalculation, UGameplayEffectExecutionCalculation, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilShieldCalculation_NoRegister) \
	DECLARE_SERIALIZER(USigilShieldCalculation)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_165_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilShieldCalculation(USigilShieldCalculation&&) = delete; \
	USigilShieldCalculation(const USigilShieldCalculation&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilShieldCalculation); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilShieldCalculation); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilShieldCalculation) \
	NO_API virtual ~USigilShieldCalculation();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_162_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_165_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_165_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_165_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilShieldCalculation;

// ********** End Class USigilShieldCalculation ****************************************************

// ********** Begin Class USigilAbilityEffectBase **************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityEffectBase_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_188_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAbilityEffectBase(); \
	friend struct Z_Construct_UClass_USigilAbilityEffectBase_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAbilityEffectBase_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAbilityEffectBase, UGameplayEffect, COMPILED_IN_FLAGS(CLASS_Abstract), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAbilityEffectBase_NoRegister) \
	DECLARE_SERIALIZER(USigilAbilityEffectBase)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_188_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAbilityEffectBase(USigilAbilityEffectBase&&) = delete; \
	USigilAbilityEffectBase(const USigilAbilityEffectBase&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAbilityEffectBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAbilityEffectBase); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(USigilAbilityEffectBase) \
	NO_API virtual ~USigilAbilityEffectBase();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_185_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_188_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_188_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_188_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAbilityEffectBase;

// ********** End Class USigilAbilityEffectBase ****************************************************

// ********** Begin Class UGE_Murallion_BarrierProtection ******************************************
AURACRON_API UClass* Z_Construct_UClass_UGE_Murallion_BarrierProtection_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_215_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUGE_Murallion_BarrierProtection(); \
	friend struct Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UGE_Murallion_BarrierProtection_NoRegister(); \
public: \
	DECLARE_CLASS2(UGE_Murallion_BarrierProtection, USigilAbilityEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UGE_Murallion_BarrierProtection_NoRegister) \
	DECLARE_SERIALIZER(UGE_Murallion_BarrierProtection)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_215_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UGE_Murallion_BarrierProtection(UGE_Murallion_BarrierProtection&&) = delete; \
	UGE_Murallion_BarrierProtection(const UGE_Murallion_BarrierProtection&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UGE_Murallion_BarrierProtection); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UGE_Murallion_BarrierProtection); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UGE_Murallion_BarrierProtection) \
	NO_API virtual ~UGE_Murallion_BarrierProtection();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_212_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_215_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_215_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_215_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UGE_Murallion_BarrierProtection;

// ********** End Class UGE_Murallion_BarrierProtection ********************************************

// ********** Begin Class UGE_Murallion_BarrierRegeneration ****************************************
AURACRON_API UClass* Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_238_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUGE_Murallion_BarrierRegeneration(); \
	friend struct Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_NoRegister(); \
public: \
	DECLARE_CLASS2(UGE_Murallion_BarrierRegeneration, USigilAbilityEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_NoRegister) \
	DECLARE_SERIALIZER(UGE_Murallion_BarrierRegeneration)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_238_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UGE_Murallion_BarrierRegeneration(UGE_Murallion_BarrierRegeneration&&) = delete; \
	UGE_Murallion_BarrierRegeneration(const UGE_Murallion_BarrierRegeneration&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UGE_Murallion_BarrierRegeneration); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UGE_Murallion_BarrierRegeneration); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UGE_Murallion_BarrierRegeneration) \
	NO_API virtual ~UGE_Murallion_BarrierRegeneration();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_235_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_238_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_238_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_238_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UGE_Murallion_BarrierRegeneration;

// ********** End Class UGE_Murallion_BarrierRegeneration ******************************************

// ********** Begin Class UGE_FracassoPrismal_DamageBuff *******************************************
AURACRON_API UClass* Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_262_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUGE_FracassoPrismal_DamageBuff(); \
	friend struct Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_NoRegister(); \
public: \
	DECLARE_CLASS2(UGE_FracassoPrismal_DamageBuff, USigilAbilityEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_NoRegister) \
	DECLARE_SERIALIZER(UGE_FracassoPrismal_DamageBuff)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_262_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UGE_FracassoPrismal_DamageBuff(UGE_FracassoPrismal_DamageBuff&&) = delete; \
	UGE_FracassoPrismal_DamageBuff(const UGE_FracassoPrismal_DamageBuff&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UGE_FracassoPrismal_DamageBuff); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UGE_FracassoPrismal_DamageBuff); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UGE_FracassoPrismal_DamageBuff) \
	NO_API virtual ~UGE_FracassoPrismal_DamageBuff();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_259_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_262_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_262_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_262_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UGE_FracassoPrismal_DamageBuff;

// ********** End Class UGE_FracassoPrismal_DamageBuff *********************************************

// ********** Begin Class UGE_FracassoPrismal_CooldownReduction ************************************
AURACRON_API UClass* Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_288_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUGE_FracassoPrismal_CooldownReduction(); \
	friend struct Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_NoRegister(); \
public: \
	DECLARE_CLASS2(UGE_FracassoPrismal_CooldownReduction, USigilAbilityEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_NoRegister) \
	DECLARE_SERIALIZER(UGE_FracassoPrismal_CooldownReduction)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_288_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UGE_FracassoPrismal_CooldownReduction(UGE_FracassoPrismal_CooldownReduction&&) = delete; \
	UGE_FracassoPrismal_CooldownReduction(const UGE_FracassoPrismal_CooldownReduction&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UGE_FracassoPrismal_CooldownReduction); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UGE_FracassoPrismal_CooldownReduction); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UGE_FracassoPrismal_CooldownReduction) \
	NO_API virtual ~UGE_FracassoPrismal_CooldownReduction();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_285_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_288_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_288_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_288_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UGE_FracassoPrismal_CooldownReduction;

// ********** End Class UGE_FracassoPrismal_CooldownReduction **************************************

// ********** Begin Class UGE_SoproDeFluxo_Shield **************************************************
AURACRON_API UClass* Z_Construct_UClass_UGE_SoproDeFluxo_Shield_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_315_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUGE_SoproDeFluxo_Shield(); \
	friend struct Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UGE_SoproDeFluxo_Shield_NoRegister(); \
public: \
	DECLARE_CLASS2(UGE_SoproDeFluxo_Shield, USigilAbilityEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UGE_SoproDeFluxo_Shield_NoRegister) \
	DECLARE_SERIALIZER(UGE_SoproDeFluxo_Shield)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_315_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UGE_SoproDeFluxo_Shield(UGE_SoproDeFluxo_Shield&&) = delete; \
	UGE_SoproDeFluxo_Shield(const UGE_SoproDeFluxo_Shield&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UGE_SoproDeFluxo_Shield); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UGE_SoproDeFluxo_Shield); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UGE_SoproDeFluxo_Shield) \
	NO_API virtual ~UGE_SoproDeFluxo_Shield();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_312_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_315_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_315_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_315_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UGE_SoproDeFluxo_Shield;

// ********** End Class UGE_SoproDeFluxo_Shield ****************************************************

// ********** Begin Class UGE_SoproDeFluxo_DashMobility ********************************************
AURACRON_API UClass* Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_341_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUGE_SoproDeFluxo_DashMobility(); \
	friend struct Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_NoRegister(); \
public: \
	DECLARE_CLASS2(UGE_SoproDeFluxo_DashMobility, USigilAbilityEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_NoRegister) \
	DECLARE_SERIALIZER(UGE_SoproDeFluxo_DashMobility)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_341_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UGE_SoproDeFluxo_DashMobility(UGE_SoproDeFluxo_DashMobility&&) = delete; \
	UGE_SoproDeFluxo_DashMobility(const UGE_SoproDeFluxo_DashMobility&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UGE_SoproDeFluxo_DashMobility); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UGE_SoproDeFluxo_DashMobility); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UGE_SoproDeFluxo_DashMobility) \
	NO_API virtual ~UGE_SoproDeFluxo_DashMobility();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_338_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_341_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_341_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_341_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UGE_SoproDeFluxo_DashMobility;

// ********** End Class UGE_SoproDeFluxo_DashMobility **********************************************

// ********** Begin Class USigilAbilityEffectFactory ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_371_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateSoproDeFluxoDashMobility); \
	DECLARE_FUNCTION(execCreateSoproDeFluxoShield); \
	DECLARE_FUNCTION(execCreateFracassoPrismalCooldownReduction); \
	DECLARE_FUNCTION(execCreateFracassoPrismalDamageBuff); \
	DECLARE_FUNCTION(execCreateMurallionBarrierRegeneration); \
	DECLARE_FUNCTION(execCreateMurallionBarrierProtection);


AURACRON_API UClass* Z_Construct_UClass_USigilAbilityEffectFactory_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_371_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAbilityEffectFactory(); \
	friend struct Z_Construct_UClass_USigilAbilityEffectFactory_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAbilityEffectFactory_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAbilityEffectFactory, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAbilityEffectFactory_NoRegister) \
	DECLARE_SERIALIZER(USigilAbilityEffectFactory)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_371_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAbilityEffectFactory(USigilAbilityEffectFactory&&) = delete; \
	USigilAbilityEffectFactory(const USigilAbilityEffectFactory&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAbilityEffectFactory); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAbilityEffectFactory); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilAbilityEffectFactory) \
	NO_API virtual ~USigilAbilityEffectFactory();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_368_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_371_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_371_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_371_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h_371_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAbilityEffectFactory;

// ********** End Class USigilAbilityEffectFactory *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
