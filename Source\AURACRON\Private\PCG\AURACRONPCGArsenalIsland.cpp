// AURACRONPCGArsenalIsland.cpp
// Implementação da classe AArsenalIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGArsenalIsland.h"
#include "GAS/AURACRONAttributeSet.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"

AArsenalIsland::AArsenalIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    
    // Inicializar propriedades
    WeaponBonusDuration = 30.0f;
    AbilityBoostMultiplier = 1.5f;
    
    // Configurar componentes específicos da Arsenal Island
    
    // Plataforma de armas central
    WeaponPlatform = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("WeaponPlatform"));
    WeaponPlatform->SetupAttachment(RootComponent);
    WeaponPlatform->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
    WeaponPlatform->SetRelativeScale3D(FVector(2.0f, 2.0f, 0.5f));
    WeaponPlatform->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito de energia da plataforma
    PlatformEnergyEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("PlatformEnergyEffect"));
    PlatformEnergyEffect->SetupAttachment(WeaponPlatform);
    PlatformEnergyEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
    
    // Depósitos de munição
    for (int32 i = 0; i < 4; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("AmmoDeposit_%d"), i);
        UStaticMeshComponent* AmmoDeposit = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
        AmmoDeposit->SetupAttachment(RootComponent);
        
        // Posicionar em torno da plataforma central
        float Angle = (float)i / 4.0f * 2.0f * PI;
        float Distance = 200.0f;
        FVector Position;
        Position.X = Distance * FMath::Cos(Angle);
        Position.Y = Distance * FMath::Sin(Angle);
        Position.Z = 100.0f;
        
        AmmoDeposit->SetRelativeLocation(Position);
        AmmoDeposit->SetRelativeScale3D(FVector(0.8f, 0.8f, 1.2f));
        AmmoDeposit->SetCollisionProfileName(TEXT("BlockAll"));
        
        AmmoDeposits.Add(AmmoDeposit);
    }
    
    // Rampas táticas
    for (int32 i = 0; i < 2; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("TacticalRamp_%d"), i);
        UStaticMeshComponent* TacticalRamp = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
        TacticalRamp->SetupAttachment(RootComponent);
        
        // Posicionar em lados opostos
        float Angle = (float)i / 2.0f * PI;
        float Distance = 300.0f;
        FVector Position;
        Position.X = Distance * FMath::Cos(Angle);
        Position.Y = Distance * FMath::Sin(Angle);
        Position.Z = 50.0f;
        
        TacticalRamp->SetRelativeLocation(Position);
        TacticalRamp->SetRelativeScale3D(FVector(3.0f, 1.0f, 0.5f));
        TacticalRamp->SetRelativeRotation(FRotator(30.0f, Angle * 180.0f / PI, 0.0f));
        TacticalRamp->SetCollisionProfileName(TEXT("BlockAll"));
        
        TacticalRamps.Add(TacticalRamp);
    }
    
    // Definir o tipo de ilha como Arsenal
    IslandType = EPrismalFlowIslandType::Arsenal;
}

void AArsenalIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Implementar rotação da plataforma de armas
    if (WeaponPlatform && bIsActive)
    {
        // Rotação lenta da plataforma
        FRotator CurrentRotation = WeaponPlatform->GetRelativeRotation();
        CurrentRotation.Yaw += DeltaTime * 10.0f; // 10 graus por segundo
        WeaponPlatform->SetRelativeRotation(CurrentRotation);
        
        // Pulsar efeitos de energia
        float Time = GetGameTimeSinceCreation();
        float PulseValue = 0.5f + 0.5f * FMath::Sin(Time * 2.0f);
        
        if (PlatformEnergyEffect)
        {
            PlatformEnergyEffect->SetFloatParameter(FName("Intensity"), PulseValue * 2.0f);
        }
    }
}

void AArsenalIsland::ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OtherActor)
    {
        return;
    }
    
    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito visual de feedback
    if (PlatformEnergyEffect)
    {
        PlatformEnergyEffect->SetFloatParameter(FName("EffectIntensity"), 3.0f); // Intensificar efeito
        
        // Retornar à intensidade normal após um curto período
        FTimerHandle TimerHandle;
        GetWorldTimerManager().SetTimer(TimerHandle, [this]()
        {
            if (PlatformEnergyEffect)
            {
                PlatformEnergyEffect->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }, 0.5f, false);
    }
    
    // Conceder bônus de armas ao jogador
    GrantWeaponBonus(OtherActor);
}

void AArsenalIsland::GrantWeaponBonus(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de bônus de armas
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para bônus de armas do Arsenal Island
    UGameplayEffect* LocalWeaponBonusEffect = NewObject<UGameplayEffect>(this, FName(TEXT("GE_ArsenalIslandWeaponBonus")));
    if (LocalWeaponBonusEffect)
    {
        // Configurar duração do efeito (45 segundos)
        LocalWeaponBonusEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
        LocalWeaponBonusEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(45.0f));
        
        // Configurar modificadores específicos do Arsenal Island
        FGameplayModifierInfo WeaponDamageModifier;
        WeaponDamageModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
        WeaponDamageModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.75f)); // +75% dano
        
        FGameplayModifierInfo WeaponRangeModifier;
        WeaponRangeModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
        WeaponRangeModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.5f)); // +50% alcance
        
        FGameplayModifierInfo WeaponSpeedModifier;
         WeaponSpeedModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
         WeaponSpeedModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.3f)); // +30% velocidade de ataque
         
         // Adicionar todos os modificadores ao efeito
         LocalWeaponBonusEffect->Modifiers.Add(WeaponDamageModifier);
         LocalWeaponBonusEffect->Modifiers.Add(WeaponRangeModifier);
         LocalWeaponBonusEffect->Modifiers.Add(WeaponSpeedModifier);

         // Aplicar o efeito
         FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(LocalWeaponBonusEffect->GetClass(), 1.0f, EffectContext);
         if (SpecHandle.IsValid())
         {
             AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
             UE_LOG(LogTemp, Display, TEXT("Arsenal Island: Aplicado bônus de armas específico (+75%% dano, +50%% alcance, +30%% velocidade) para %s"), *TargetActor->GetName());
         }
    }
    
    if (LocalWeaponBonusEffect)
    {
        // Aplicar bônus de armas
        FGameplayEffectSpecHandle EffectSpec = AbilityComponent->MakeOutgoingSpec(LocalWeaponBonusEffect->GetClass(), 1.0f, EffectContext);
        if (EffectSpec.IsValid())
        {
            FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
            
            if (ActiveEffect.IsValid())
            {
                UE_LOG(LogTemp, Log, TEXT("Arsenal Island: Bônus de armas concedido para %s"), *TargetActor->GetName());
            }
        }
    }
    
    // Criar feedback visual de bônus de armas
    UNiagaraSystem* WeaponBonusVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ArsenalIslandWeaponBonus"));
    if (WeaponBonusVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            WeaponBonusVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void AArsenalIsland::GrantSpecialAmmo(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        UE_LOG(LogTemp, Warning, TEXT("Arsenal Island: Ator %s não implementa AbilitySystemInterface para munição especial"), *TargetActor->GetName());
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("Arsenal Island: Ator %s não possui AbilitySystemComponent para munição especial"), *TargetActor->GetName());
        return;
    }
    
    // Aplicar efeito de munição especial
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para munição especial do Arsenal Island
    UGameplayEffect* LocalSpecialAmmoEffect = NewObject<UGameplayEffect>(this, FName(TEXT("GE_ArsenalIslandSpecialAmmo")));
    if (LocalSpecialAmmoEffect)
    {
        // Configurar duração do efeito (60 segundos)
        LocalSpecialAmmoEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
        LocalSpecialAmmoEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(60.0f));
        
        // Configurar modificadores específicos para munição especial
        FGameplayModifierInfo AmmoCapacityModifier;
        AmmoCapacityModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
        AmmoCapacityModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(2.0f)); // +100% capacidade
        
        FGameplayModifierInfo AmmoPenetrationModifier;
        AmmoPenetrationModifier.ModifierOp = EGameplayModOp::Additive;
        AmmoPenetrationModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(50.0f)); // +50 penetração
        
        FGameplayModifierInfo AmmoExplosiveModifier;
        AmmoExplosiveModifier.ModifierOp = EGameplayModOp::Additive;
        AmmoExplosiveModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(25.0f)); // +25 dano explosivo
        
        // Criar instância do GameplayEffect usando API moderna do UE 5.6
        if (LocalSpecialAmmoEffect)
        {
            // Usar API moderna do UE 5.6 - criar nova instância do GameplayEffect
            UGameplayEffect* NewGameplayEffect = NewObject<UGameplayEffect>(this, NAME_None, RF_NoFlags, LocalSpecialAmmoEffect);
            if (NewGameplayEffect)
            {
                // Adicionar modificadores ao efeito usando API moderna
                NewGameplayEffect->Modifiers.Add(AmmoCapacityModifier);
                NewGameplayEffect->Modifiers.Add(AmmoPenetrationModifier);
                NewGameplayEffect->Modifiers.Add(AmmoExplosiveModifier);

                // Marcar como modificado para replicação usando API moderna
                NewGameplayEffect->MarkPackageDirty();
            }
        }
        
        // Aplicar o efeito
        FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(SpecialAmmoEffect->GetClass(), 1.0f, EffectContext);
        if (SpecHandle.IsValid())
        {
            AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
            UE_LOG(LogTemp, Display, TEXT("Arsenal Island: Aplicada munição especial (+100%% capacidade, +50 penetração, +25 explosivo) para %s"), *TargetActor->GetName());
            LocalSpecialAmmoEffect->Modifiers.Add(AmmoCapacityModifier);
            LocalSpecialAmmoEffect->Modifiers.Add(AmmoPenetrationModifier);
            LocalSpecialAmmoEffect->Modifiers.Add(AmmoExplosiveModifier);
        }
    }
    
    // Aplicar o efeito de munição especial
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(LocalSpecialAmmoEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Arsenal Island: Munição especial concedida para %s"), *TargetActor->GetName());
        }
    }
    
    // Criar feedback visual de munição especial
    UNiagaraSystem* SpecialAmmoVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ArsenalIslandSpecialAmmo"));
    if (SpecialAmmoVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            SpecialAmmoVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(0.8f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void AArsenalIsland::GrantAbilityBoost(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de potencialização de habilidades
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para potencialização de habilidades
    UGameplayEffect* LocalAbilityBoostEffect = NewObject<UGameplayEffect>(this, FName("GE_ArsenalIslandAbilityBoost"));
    LocalAbilityBoostEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
    LocalAbilityBoostEffect->DurationMagnitude = FScalableFloat(60.0f); // 60 segundos
    
    // Modificador para redução de cooldown (-50%)
    FGameplayModifierInfo CooldownModifier;
    CooldownModifier.ModifierMagnitude = FScalableFloat(0.5f);
    CooldownModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
    // Usar atributo de redução de cooldown
    CooldownModifier.Attribute = UAURACRONAttributeSet::GetCooldownReductionAttribute();
    LocalAbilityBoostEffect->Modifiers.Add(CooldownModifier);

    // Modificador para aumento de poder de habilidades (+25%)
    FGameplayModifierInfo PowerModifier;
    PowerModifier.ModifierMagnitude = FScalableFloat(1.25f);
    PowerModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
    // Usar atributo de poder de habilidade
    PowerModifier.Attribute = UAURACRONAttributeSet::GetAbilityPowerAttribute();
    LocalAbilityBoostEffect->Modifiers.Add(PowerModifier);

    // Aplicar o efeito de potencialização
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(LocalAbilityBoostEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Arsenal Island: Potencialização de habilidades concedida para %s (Cooldown -50%%, Poder +25%%)"), *TargetActor->GetName());
        }
    }
    
    // Criar feedback visual de potencialização
    UNiagaraSystem* AbilityBoostVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ArsenalIslandAbilityBoost"));
    if (AbilityBoostVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            AbilityBoostVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.1f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

// Implementação das funções ausentes usando APIs modernas do UE 5.6

bool AArsenalIsland::IsNearEnvironmentTransition() const
{
    return bIsNearEnvironmentTransition;
}

void AArsenalIsland::SetNearEnvironmentTransition(bool bIsNearTransition)
{
    bIsNearEnvironmentTransition = bIsNearTransition;

    // Atualizar efeitos visuais baseado na proximidade de transição
    if (PlatformEnergyEffect)
    {
        if (bIsNearEnvironmentTransition)
        {
            // Intensificar efeitos quando próximo a transições
            PlatformEnergyEffect->SetFloatParameter(FName("IntensityMultiplier"), 1.5f);
            PlatformEnergyEffect->SetVectorParameter(FName("TransitionGlow"), FVector(1.0f, 0.8f, 0.2f));
        }
        else
        {
            // Efeitos normais
            PlatformEnergyEffect->SetFloatParameter(FName("IntensityMultiplier"), 1.0f);
            PlatformEnergyEffect->SetVectorParameter(FName("TransitionGlow"), FVector(0.5f, 0.5f, 1.0f));
        }
    }

    // Replicar mudança para clientes
    if (HasAuthority())
    {
        ForceNetUpdate();
    }
}

void AArsenalIsland::RemoveArsenalEffects(AActor* TargetActor)
{
    if (!TargetActor)
    {
        return;
    }

    // Buscar componente de sistema de habilidades usando APIs modernas do UE 5.6
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
    {
        if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
        {
            // Remover todos os efeitos de gameplay aplicados por esta ilha
            FGameplayTagContainer TagsToRemove;
            TagsToRemove.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.WeaponBonus")));
            TagsToRemove.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.AbilityBoost")));
            TagsToRemove.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.SpecialAmmo")));

            // Usar API moderna do UE 5.6 para remoção de efeitos
            ASC->RemoveActiveEffectsWithTags(TagsToRemove);

            UE_LOG(LogTemp, Log, TEXT("AArsenalIsland: Removidos efeitos de arsenal do ator %s"), *TargetActor->GetName());
        }
    }

    // Remover da lista de atores afetados
    TWeakObjectPtr<AActor> ActorPtr(TargetActor);
    ActiveArsenalEffects.Remove(ActorPtr);
    AffectedActors.Remove(ActorPtr);
}

void AArsenalIsland::UpdateIslandVisuals()
{
    // Implementação robusta da atualização visual usando APIs modernas do UE 5.6
    Super::UpdateIslandVisuals();

    if (!IsValid(this))
    {
        return;
    }

    // Atualizar efeitos baseado no estado da ilha
    // Configurar cor baseada no tipo de ambiente
    FLinearColor PlatformColor = FLinearColor::Blue;

    if (PlatformEnergyEffect && IsValid(PlatformEnergyEffect))
    {

        if (TransitionEnvironments.Num() > 0)
        {
            switch (TransitionEnvironments[0])
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    PlatformColor = FLinearColor::Yellow;
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    PlatformColor = FLinearColor(0.0f, 1.0f, 1.0f); // Cyan
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    PlatformColor = FLinearColor(0.8f, 0.2f, 0.8f); // Violeta espectral
                    break;
                default:
                    PlatformColor = FLinearColor::Blue;
                    break;
            }
        }

        // Aplicar cor usando APIs modernas
        PlatformEnergyEffect->SetColorParameter(FName("PlatformColor"), PlatformColor);

        // Configurar intensidade baseada na atividade
        float CurrentActivityLevel = bIsActive ? 1.0f : 0.5f;
        PlatformEnergyEffect->SetFloatParameter(FName("ActivityIntensity"), CurrentActivityLevel);

        // Configurar pulsação baseada na proximidade de transição
        if (bIsNearEnvironmentTransition)
        {
            PlatformEnergyEffect->SetFloatParameter(FName("PulseSpeed"), 2.0f);
            PlatformEnergyEffect->SetFloatParameter(FName("PulseAmplitude"), 0.8f);
        }
        else
        {
            PlatformEnergyEffect->SetFloatParameter(FName("PulseSpeed"), 1.0f);
            PlatformEnergyEffect->SetFloatParameter(FName("PulseAmplitude"), 0.5f);
        }
    }

    // Atualizar materiais dinâmicos das plataformas de armas
    for (UStaticMeshComponent* Platform : WeaponPlatforms)
    {
        if (Platform && IsValid(Platform))
        {
            if (UMaterialInterface* BaseMaterial = Platform->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = Platform->CreateDynamicMaterialInstance(0, BaseMaterial);
                if (DynamicMaterial)
                {
                    // Configurar parâmetros do material usando APIs modernas
                    DynamicMaterial->SetScalarParameterValue(FName("GlowIntensity"), WeaponBonusIntensity);
                    DynamicMaterial->SetVectorParameterValue(FName("GlowColor"), FVector(PlatformColor.R, PlatformColor.G, PlatformColor.B));
                    DynamicMaterial->SetScalarParameterValue(FName("Metallic"), 0.8f);
                    DynamicMaterial->SetScalarParameterValue(FName("Roughness"), 0.2f);
                }
            }
        }
    }

    // Atualizar depósitos de munição
    for (UStaticMeshComponent* Depot : AmmoDepots)
    {
        if (Depot && IsValid(Depot))
        {
            // Configurar escala baseada na quantidade de munição disponível
            float ScaleMultiplier = FMath::Clamp(SpecialAmmoCount / 100.0f, 0.5f, 2.0f);
            Depot->SetRelativeScale3D(FVector(ScaleMultiplier));

            // Atualizar material
            if (UMaterialInterface* BaseMaterial = Depot->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = Depot->CreateDynamicMaterialInstance(0, BaseMaterial);
                if (DynamicMaterial)
                {
                    DynamicMaterial->SetScalarParameterValue(FName("AmmoLevel"), SpecialAmmoCount / 100.0f);
                    DynamicMaterial->SetVectorParameterValue(FName("AmmoColor"), FVector(1.0f, 0.5f, 0.0f)); // Laranja para munição
                }
            }
        }
    }
}

void AArsenalIsland::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades específicas da Arsenal Island usando APIs modernas do UE 5.6
    DOREPLIFETIME(AArsenalIsland, WeaponBonusIntensity);
    DOREPLIFETIME(AArsenalIsland, WeaponBonusDuration);
    DOREPLIFETIME(AArsenalIsland, SpecialAmmoCount);
    DOREPLIFETIME(AArsenalIsland, AbilityBoostIntensity);
    DOREPLIFETIME(AArsenalIsland, AbilityBoostDuration);
    DOREPLIFETIME(AArsenalIsland, bIsNearEnvironmentTransition);
    DOREPLIFETIME(AArsenalIsland, TransitionEnvironments);
}