{"Version": "1.2", "Data": {"Source": "c:\\auracron\\source\\auracron\\private\\sigils\\sigilitem.cpp", "ProvidedModule": "", "PCH": "c:\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracron\\definitions.auracron.h", "c:\\auracron\\source\\auracron\\public\\sigils\\sigilitem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilitysysteminterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\abilitysysteminterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilitysystemcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\attributeset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\attributeset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayprediction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\classes\\net\\serialization\\fastarrayserializer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\misc\\guidreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\pushmodel\\pushmodel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\netcoremodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\netcore\\uht\\fastarrayserializer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayprediction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplaycueinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffecttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\activegameplayeffecthandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\activegameplayeffecthandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffectattributecapturedefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffectattributecapturedefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffecttypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplaycueinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagassetinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagassetinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspec.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspechandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspechandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\scalablefloat.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\source\\dataregistry\\public\\dataregistryid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\intermediate\\build\\win64\\unrealeditor\\inc\\dataregistry\\uht\\dataregistryid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\scalablefloat.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspec.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffect.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffectaggregator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\activegameplayeffectiterator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\visuallogger\\visualloggerdebugsnapshotinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\visualloggerdebugsnapshotinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffect.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytasks\\classes\\gameplaytaskscomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytasks\\classes\\gameplaytaskresource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytasks\\uht\\gameplaytaskresource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytasks\\uht\\gameplaytaskscomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilityrepanimmontage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityrepanimmontage.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilitytargettypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilitytargettypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayability.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilitytypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilitytypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayability.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilitysystemreplicationproxyinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\abilitysystemreplicationproxyinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\abilitysystemcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\public\\gameplaytags.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagsmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagsmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagarasystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraassettagdefinitions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraassettagdefinitions.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaradatasetcompileddata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstats.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponentpoolmethodenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracomponentpoolmethodenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaradefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarascalabilitystate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarascalabilitystate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratickbehaviorenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaratickbehaviorenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagaracore\\public\\niagaracore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagaracore\\uht\\niagaracore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaratypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratyperegistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaradatasetcompileddata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaradatasetaccessor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraeffecttype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ingameperformancetracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraplatformset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraplatformset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraperfbaseline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstatsmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraperfbaseline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaravalidationrule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravalidationrule.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaravalidationruleset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravalidationruleset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraeffecttype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraemitterhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraemitterhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaramessagestore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaramessagestore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraparametercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraparameterstore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparameterstore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagaracore\\public\\niagaracompilehash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagaracore\\uht\\niagaracompilehash.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparametercollection.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraparameterdefinitionssubscriber.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraparameterdefinitionsdelegates.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparameterdefinitionssubscriber.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarauserredirectionparameterstore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarauserredirectionparameterstore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\instancedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\fxbudget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarasystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaravariant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravariant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystemcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\emitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\emitter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\statestream\\particlesystemstatestreamhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystemstatestreamhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystemcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\net\\unrealnetwork.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\propertyconditions\\propertyconditions.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\sigilitem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spherecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarafunctionlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponentpool.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracomponentpool.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\vectorvm\\public\\vectorvm.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\vectorvm\\uht\\vectorvm.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarafunctionlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\auracron\\source\\auracron\\public\\sigils\\sigilmanagercomponent.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\sigilmanagercomponent.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}