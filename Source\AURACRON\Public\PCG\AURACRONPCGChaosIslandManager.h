// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONPCGChaosIsland.h"
#include "PCG/AURACRONPCGChaosPortal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "AURACRONPCGChaosIslandManager.generated.h"

/**
 * Gerenciador de Ilhas <PERSON>s
 * Responsável por coordenar a geração, posicionamento e comportamento das Ilhas Caos
 * Garante que as ilhas estejam corretamente posicionadas em pontos de interseção do Fluxo
 * Implementa os requisitos do GDD:
 * - Localização: Em pontos de interseção do Fluxo
 * - Características: Perigos ambientais, recompensas de alto risco, terreno instável
 * - Valor Estratégico: Itens que mudam o jogo com risco significativo
 */
UCLASS()
class AURACRON_API AAURACRONPCGChaosIslandManager : public AActor
{
    GENERATED_BODY()

public:
    // Sets default values for this actor's properties
    AAURACRONPCGChaosIslandManager();

    // Called when the game starts or when spawned
    virtual void BeginPlay() override;

    // Called every frame
    virtual void Tick(float DeltaTime) override;
    
    /** Inicializar o gerenciador com referência ao fluxo prismal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void Initialize(AAURACRONPCGPrismalFlow* InPrismalFlow);
    
    /** Gerar as ilhas caos nos pontos de interseção do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void GenerateChaosIslands();
    
    /** Atualizar as ilhas caos para a fase atual do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    /** Obter todas as ilhas caos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    TArray<AChaosIsland*> GetAllChaosIslands() const { return ChaosIslands; }
    
    /** Obter todos os portais caos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    TArray<AAURACRONPCGChaosPortal*> GetAllChaosPortals() const { return ChaosPortals; }
    
    /** Verificar se um ponto está em uma interseção do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    bool IsPointAtFlowIntersection(const FVector& Point, float Tolerance = 500.0f) const;
    
    /** Encontrar todos os pontos de interseção do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    TArray<FVector> FindAllFlowIntersections() const;
    
    /** Ativar/desativar todas as ilhas caos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void SetAllChaosIslandsActive(bool bActive);
    
    /** Ativar/desativar todos os portais caos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void SetAllChaosPortalsActive(bool bActive, float Duration = 0.0f, float Intensity = 1.0f);

protected:
    /** Referência ao fluxo prismal */
    UPROPERTY()
    AAURACRONPCGPrismalFlow* PrismalFlow;
    
    /** Lista de ilhas caos */
    UPROPERTY()
    TArray<AChaosIsland*> ChaosIslands;
    
    /** Lista de portais caos */
    UPROPERTY()
    TArray<AAURACRONPCGChaosPortal*> ChaosPortals;
    
    /** Número máximo de ilhas caos (conforme GDD) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland")
    int32 MaxChaosIslands;
    
    /** Classe da ilha caos para spawn */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland")
    TSubclassOf<AChaosIsland> ChaosIslandClass;
    
    /** Classe do portal caos para spawn */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland")
    TSubclassOf<AAURACRONPCGChaosPortal> ChaosPortalClass;
    
    /** Distância mínima entre ilhas caos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland")
    float MinDistanceBetweenIslands;

    /** Auto-spawnar portais quando criar ilhas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland")
    bool bAutoSpawnPortals;

    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;
    
    /** Criar uma ilha caos em um ponto específico */
    UFUNCTION()
    AChaosIsland* SpawnChaosIsland(const FVector& Location);
    
    /** Criar um portal caos em um ponto específico */
    UFUNCTION()
    AAURACRONPCGChaosPortal* SpawnChaosPortal(const FVector& Location);
    
    /** Verificar se um ponto está muito próximo de ilhas existentes */
    UFUNCTION()
    bool IsPointTooCloseToExistingIslands(const FVector& Point) const;
    
    /** Atualizar a intensidade dos efeitos baseado na fase do mapa */
    UFUNCTION()
    void UpdateEffectsIntensity();

    /** Calcular interseção entre duas linhas 2D */
    UFUNCTION()
    bool CalculateLineIntersection(const FVector& P1, const FVector& P2, const FVector& P3, const FVector& P4, FVector& OutIntersection) const;
};