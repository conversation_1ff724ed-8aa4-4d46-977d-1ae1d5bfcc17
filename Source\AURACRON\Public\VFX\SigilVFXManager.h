// SigilVFXManager.h
// AURACRON - Sistema de Sígilos
// Gerenciador de efeitos visuais Niagara com pooling para MOBA 5x5
// APIs verificadas: NiagaraComponent.h, NiagaraSystem.h, ObjectPool.h

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Engine/ObjectLibrary.h"
#include "GameplayTagContainer.h"
#include "Sigils/SigilItem.h"
#include "SigilVFXManager.generated.h"

// Forward Declarations
class UNiagaraSystem;
class UNiagaraComponent;
class ASigilItem;
class UWorld;

// ========================================
// ENUMS E ESTRUTURAS
// ========================================

/**
 * Tipos de efeitos VFX para sígilos
 */
UENUM(BlueprintType)
enum class ESigilVFXType : uint8
{
    None            UMETA(DisplayName = "None"),
    Equip           UMETA(DisplayName = "Equip"),
    Unequip         UMETA(DisplayName = "Unequip"),
    FusionStart     UMETA(DisplayName = "Fusion Start"),
    FusionProgress  UMETA(DisplayName = "Fusion Progress"),
    FusionComplete  UMETA(DisplayName = "Fusion Complete"),
    Reforge         UMETA(DisplayName = "Reforge"),
    LevelUp         UMETA(DisplayName = "Level Up"),
    SpectralPower   UMETA(DisplayName = "Spectral Power"),
    SpectralAura    UMETA(DisplayName = "Spectral Aura"),
    TeamFight       UMETA(DisplayName = "Team Fight"),
    Objective       UMETA(DisplayName = "Objective"),
    Critical        UMETA(DisplayName = "Critical"),
    Death           UMETA(DisplayName = "Death"),
    Respawn         UMETA(DisplayName = "Respawn")
};

/**
 * Prioridade dos efeitos VFX
 */
UENUM(BlueprintType)
enum class ESigilVFXPriority : uint8
{
    Low     UMETA(DisplayName = "Low"),
    Medium  UMETA(DisplayName = "Medium"),
    High    UMETA(DisplayName = "High"),
    Critical UMETA(DisplayName = "Critical")
};

/**
 * Configuração de um efeito VFX
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilVFXConfig
{
    GENERATED_BODY()

    /** Sistema Niagara para o efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> NiagaraSystem;

    /** Duração do efeito em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX", meta = (ClampMin = "0.1", ClampMax = "30.0"))
    float Duration = 2.0f;

    /** Prioridade do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    ESigilVFXPriority Priority = ESigilVFXPriority::Medium;

    /** Se o efeito deve fazer loop */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    bool bShouldLoop = false;

    /** Se o efeito deve ser anexado ao ator */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    bool bAttachToActor = true;

    /** Nome do socket para anexar o efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    FName AttachSocketName = NAME_None;

    /** Offset relativo para o efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    FVector RelativeOffset = FVector::ZeroVector;

    /** Escala do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    FVector Scale = FVector::OneVector;

    /** Cor do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    FLinearColor Color = FLinearColor::White;

    /** Tags de gameplay associadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    FGameplayTagContainer GameplayTags;

    /** Parâmetros customizados do Niagara */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TMap<FString, float> FloatParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TMap<FString, FVector> VectorParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TMap<FString, FLinearColor> ColorParameters;

    FSigilVFXConfig()
    {
        Duration = 2.0f;
        Priority = ESigilVFXPriority::Medium;
        bShouldLoop = false;
        bAttachToActor = true;
        AttachSocketName = NAME_None;
        RelativeOffset = FVector::ZeroVector;
        Scale = FVector::OneVector;
        Color = FLinearColor::White;
    }
};

/**
 * Instância ativa de um efeito VFX
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilVFXInstance
{
    GENERATED_BODY()

    /** Componente Niagara ativo */
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    TObjectPtr<UNiagaraComponent> NiagaraComponent;

    /** Tipo do efeito */
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    ESigilVFXType VFXType = ESigilVFXType::None;

    /** Configuração do efeito */
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    FSigilVFXConfig Config;

    /** Ator proprietário do efeito */
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    TWeakObjectPtr<AActor> OwnerActor;

    /** Tempo restante do efeito */
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    float RemainingTime = 0.0f;

    /** ID único da instância */
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    int32 InstanceID = -1;

    /** Se a instância está ativa */
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    bool bIsActive = false;

    /** Timestamp de criação */
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    float CreationTime = 0.0f;

    FSigilVFXInstance()
    {
        NiagaraComponent = nullptr;
        VFXType = ESigilVFXType::None;
        OwnerActor = nullptr;
        RemainingTime = 0.0f;
        InstanceID = -1;
        bIsActive = false;
        CreationTime = 0.0f;
    }
};

/**
 * Pool de componentes Niagara para otimização
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilVFXPool
{
    GENERATED_BODY()

    /** Componentes disponíveis no pool */
    UPROPERTY()
    TArray<TObjectPtr<UNiagaraComponent>> AvailableComponents;

    /** Componentes atualmente em uso */
    UPROPERTY()
    TArray<TObjectPtr<UNiagaraComponent>> ActiveComponents;

    /** Tamanho máximo do pool */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool")
    int32 MaxPoolSize = 50;

    /** Tamanho inicial do pool */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool")
    int32 InitialPoolSize = 10;

    /** Sistema Niagara associado ao pool */
    UPROPERTY()
    TSoftObjectPtr<UNiagaraSystem> AssociatedSystem;

    FSigilVFXPool()
    {
        MaxPoolSize = 50;
        InitialPoolSize = 10;
    }
};

/**
 * Estatísticas do sistema VFX
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilVFXStats
{
    GENERATED_BODY()

    /** Total de efeitos ativos */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 ActiveEffects = 0;

    /** Total de componentes no pool */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 PooledComponents = 0;

    /** Efeitos por tipo */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    TMap<ESigilVFXType, int32> EffectsByType;

    /** Efeitos por prioridade */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    TMap<ESigilVFXPriority, int32> EffectsByPriority;

    /** Tempo médio de vida dos efeitos */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float AverageEffectLifetime = 0.0f;

    /** Pico de efeitos simultâneos */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 PeakSimultaneousEffects = 0;

    /** Eficiência do pool (%) */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float PoolEfficiency = 0.0f;

    FSigilVFXStats()
    {
        ActiveEffects = 0;
        PooledComponents = 0;
        AverageEffectLifetime = 0.0f;
        PeakSimultaneousEffects = 0;
        PoolEfficiency = 0.0f;
    }
};

// ========================================
// DELEGATES
// ========================================

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnVFXStarted, ESigilVFXType, VFXType, int32, InstanceID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnVFXCompleted, ESigilVFXType, VFXType, int32, InstanceID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnVFXStatsChanged, const FSigilVFXStats&, NewStats);

// ========================================
// VFX MANAGER COMPONENT
// ========================================

/**
 * Componente responsável por gerenciar efeitos visuais Niagara para sígilos
 * Otimizado para MOBA 5x5 com sistema de pooling
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(AURACRON), meta=(BlueprintSpawnableComponent))
class AURACRON_API USigilVFXManager : public UActorComponent
{
    GENERATED_BODY()

public:
    USigilVFXManager();

    // ========================================
    // CORE FUNCTIONS
    // ========================================

    /** Inicializar o manager VFX */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    void InitializeVFXManager();

    /** Finalizar o manager VFX */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    void ShutdownVFXManager();

    /** Tocar efeito VFX */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlayVFXEffect(ESigilVFXType VFXType, AActor* TargetActor, const FSigilVFXConfig& Config = FSigilVFXConfig());

    /** Parar efeito VFX específico */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    bool StopVFXEffect(int32 InstanceID);

    /** Parar todos os efeitos de um ator */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    void StopAllVFXForActor(AActor* TargetActor);

    /** Parar todos os efeitos de um tipo */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    void StopAllVFXOfType(ESigilVFXType VFXType);

    /** Limpar todos os efeitos */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    void ClearAllVFX();

    // ========================================
    // SIGIL-SPECIFIC FUNCTIONS
    // ========================================

    /** Tocar efeito de equipar sigilo */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlaySigilEquipVFX(ASigilItem* Sigil, AActor* TargetActor);

    /** Tocar efeito de desequipar sigilo */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlaySigilUnequipVFX(ASigilItem* Sigil, AActor* TargetActor);

    /** Tocar efeito de fusão de sigilo */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlaySigilFusionVFX(ASigilItem* Sigil, AActor* TargetActor, bool bIsComplete = false);

    /** Tocar efeito de reforge de sigilo */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlaySigilReforgeVFX(ASigilItem* Sigil, AActor* TargetActor);

    /** Tocar efeito de aura espectral */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlaySpectralAuraVFX(AActor* TargetActor, ESigilRarity Rarity);

    // ========================================
    // MOBA-SPECIFIC FUNCTIONS
    // ========================================

    /** Tocar efeito de team fight */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlayTeamFightVFX(AActor* TargetActor, int32 TeamID);

    /** Tocar efeito de objetivo */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlayObjectiveVFX(AActor* TargetActor, FGameplayTag ObjectiveTag);

    /** Tocar efeito crítico */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    int32 PlayCriticalVFX(AActor* TargetActor, float Damage);

    // ========================================
    // QUERY FUNCTIONS
    // ========================================

    /** Verificar se um efeito está ativo */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil VFX")
    bool IsVFXActive(int32 InstanceID) const;

    /** Obter instância de efeito */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil VFX")
    FSigilVFXInstance GetVFXInstance(int32 InstanceID) const;

    /** Obter todos os efeitos ativos de um ator */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil VFX")
    TArray<FSigilVFXInstance> GetActiveVFXForActor(AActor* TargetActor) const;

    /** Obter estatísticas do sistema VFX */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil VFX")
    FSigilVFXStats GetVFXStats() const;

    /** Obter configuração padrão para um tipo de VFX */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil VFX")
    FSigilVFXConfig GetDefaultVFXConfig(ESigilVFXType VFXType) const;

    // ========================================
    // POOL MANAGEMENT
    // ========================================

    /** Pré-carregar pool de componentes */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    void PreloadVFXPool(UNiagaraSystem* NiagaraSystem, int32 PoolSize = 10);

    /** Limpar pools não utilizados */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    void CleanupUnusedPools();

    /** Otimizar pools baseado no uso */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX")
    void OptimizePools();

    // ========================================
    // CONFIGURATION
    // ========================================

    /** Configurações padrão de VFX por tipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Config")
    TMap<ESigilVFXType, FSigilVFXConfig> DefaultVFXConfigs;

    /** Configurações de VFX por raridade de sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Config")
    TMap<ESigilRarity, FSigilVFXConfig> RarityVFXConfigs;

    /** Máximo de efeitos simultâneos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Config", meta = (ClampMin = "1", ClampMax = "200"))
    int32 MaxSimultaneousEffects = 100;

    /** Máximo de efeitos por ator */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Config", meta = (ClampMin = "1", ClampMax = "50"))
    int32 MaxEffectsPerActor = 10;

    /** Intervalo de limpeza automática (segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Config", meta = (ClampMin = "1.0", ClampMax = "60.0"))
    float CleanupInterval = 5.0f;

    /** Se deve usar pooling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Config")
    bool bUsePooling = true;

    /** Se deve otimizar automaticamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Config")
    bool bAutoOptimize = true;

    // ========================================
    // DELEGATES
    // ========================================

    /** Evento quando um VFX é iniciado */
    UPROPERTY(BlueprintAssignable, Category = "Sigil VFX")
    FOnVFXStarted OnVFXStarted;

    /** Evento quando um VFX é completado */
    UPROPERTY(BlueprintAssignable, Category = "Sigil VFX")
    FOnVFXCompleted OnVFXCompleted;

    /** Evento quando as estatísticas mudam */
    UPROPERTY(BlueprintAssignable, Category = "Sigil VFX")
    FOnVFXStatsChanged OnVFXStatsChanged;

    // ========================================
    // DEBUG FUNCTIONS
    // ========================================

    /** Imprimir estatísticas do VFX */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX|Debug")
    void PrintVFXStats() const;

    /** Imprimir informações dos pools */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX|Debug")
    void PrintPoolInfo() const;

    /** Forçar limpeza de todos os pools */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX|Debug")
    void ForceCleanupAllPools();

    /** Resetar estatísticas */
    UFUNCTION(BlueprintCallable, Category = "Sigil VFX|Debug")
    void ResetVFXStats();

protected:
    // ========================================
    // UNREAL ENGINE OVERRIDES
    // ========================================

    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

private:
    // ========================================
    // INTERNAL DATA
    // ========================================

    /** Instâncias ativas de VFX */
    UPROPERTY()
    TMap<int32, FSigilVFXInstance> ActiveVFXInstances;

    /** Pools de componentes Niagara */
    UPROPERTY()
    TMap<TSoftObjectPtr<UNiagaraSystem>, FSigilVFXPool> ComponentPools;

    /** Próximo ID de instância */
    int32 NextInstanceID;

    /** Estatísticas do sistema */
    FSigilVFXStats CurrentStats;

    /** Timer para limpeza automática */
    float CleanupTimer;

    /** Se o manager está inicializado */
    bool bIsInitialized;

    /** Timestamp da última otimização */
    float LastOptimizationTime;

    // ========================================
    // INTERNAL FUNCTIONS
    // ========================================

    /** Criar nova instância de VFX */
    FSigilVFXInstance CreateVFXInstance(ESigilVFXType VFXType, AActor* TargetActor, const FSigilVFXConfig& Config);

    /** Obter componente do pool */
    UNiagaraComponent* GetPooledComponent(UNiagaraSystem* NiagaraSystem);

    /** Retornar componente ao pool */
    void ReturnComponentToPool(UNiagaraComponent* Component, UNiagaraSystem* NiagaraSystem);

    /** Criar novo pool */
    void CreatePool(UNiagaraSystem* NiagaraSystem, int32 InitialSize);

    /** Expandir pool existente */
    void ExpandPool(UNiagaraSystem* NiagaraSystem, int32 AdditionalSize);

    /** Configurar componente Niagara */
    void ConfigureNiagaraComponent(UNiagaraComponent* Component, const FSigilVFXConfig& Config, AActor* TargetActor);

    /** Aplicar parâmetros customizados */
    void ApplyCustomParameters(UNiagaraComponent* Component, const FSigilVFXConfig& Config);

    /** Atualizar instâncias ativas */
    void UpdateActiveInstances(float DeltaTime);

    /** Remover instância expirada */
    void RemoveExpiredInstance(int32 InstanceID);

    /** Limpeza automática */
    void PerformAutomaticCleanup();

    /** Atualizar estatísticas */
    void UpdateStats();

    /** Validar configuração */
    bool ValidateVFXConfig(const FSigilVFXConfig& Config) const;

    /** Obter configuração baseada em sigilo */
    FSigilVFXConfig GetSigilBasedConfig(ASigilItem* Sigil, ESigilVFXType VFXType) const;

    /** Verificar limites de efeitos */
    bool CanCreateNewEffect(AActor* TargetActor) const;

    /** Remover efeitos de baixa prioridade se necessário */
    void RemoveLowPriorityEffects(int32 CountToRemove);

    /** Inicializar configurações padrão */
    void InitializeDefaultConfigs();

    /** Inicializar configurações de raridade */
    void InitializeRarityConfigs();

    /** Debug: Definir número máximo de efeitos simultâneos */
    void DebugSetMaxEffects(int32 NewMaxEffects);

    /** Debug: Alternar uso de pooling */
    void DebugTogglePooling();

    /** Debug: Limpar todos os efeitos VFX */
    void DebugClearAllVFX();

    /** Pré-carregar pools de VFX comuns */
    void PreloadCommonVFXPools();

    /** Debug: Spawnar VFX de teste */
    void DebugSpawnTestVFX(ESigilVFXType VFXType, AActor* TargetActor = nullptr);

    /** Obter tamanho ótimo do pool baseado no tipo de VFX */
    int32 GetOptimalPoolSize(ESigilVFXType VFXType) const;

    /** Obter tamanho do pool baseado na raridade */
    int32 GetRarityPoolSize(ESigilRarity Rarity) const;

    // ========================================
    // CONSTANTS
    // ========================================

    /** Tamanho padrão do pool */
    static constexpr int32 DEFAULT_POOL_SIZE = 10;

    /** Tamanho máximo do pool */
    static constexpr int32 MAX_POOL_SIZE = 100;

    /** Intervalo de otimização automática */
    static constexpr float AUTO_OPTIMIZATION_INTERVAL = 30.0f;

    /** Tempo máximo de vida de um efeito */
    static constexpr float MAX_EFFECT_LIFETIME = 300.0f;
};