// SigilWidgets.h
// AURACRON - Sistema de Sígilos
// UMG Widgets para interface do usuário do sistema de sígilos
// APIs verificadas: Blueprint.h, UserWidget.h, DragDropOperation.h

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Blueprint/DragDropOperation.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Components/ProgressBar.h"
#include "Components/Button.h"
#include "Components/Border.h"
#include "Components/CanvasPanel.h"
#include "Components/HorizontalBox.h"
#include "Components/VerticalBox.h"
#include "Components/Overlay.h"
#include "Engine/Texture2D.h"
#include "Materials/MaterialInterface.h"
#include "GameplayTagContainer.h"
#include "Sigils/SigilItem.h"
#include "Sigils/SigilManagerComponent.h"
#include "SigilWidgets.generated.h"

// Forward Declarations
class ASigilItem;
class USigilManagerComponent;
class UNiagaraComponent;
class USoundBase;
class UMaterialParameterCollection;

// ========================================
// ENUMS E ESTRUTURAS
// ========================================

/**
 * Estados visuais do slot de sigilo
 */
UENUM(BlueprintType)
enum class ESigilSlotState : uint8
{
    Empty           UMETA(DisplayName = "Empty"),
    Equipped        UMETA(DisplayName = "Equipped"),
    FusionReady     UMETA(DisplayName = "Fusion Ready"),
    Fusing          UMETA(DisplayName = "Fusing"),
    Locked          UMETA(DisplayName = "Locked"),
    Highlighted     UMETA(DisplayName = "Highlighted"),
    DragTarget      UMETA(DisplayName = "Drag Target"),
    Invalid         UMETA(DisplayName = "Invalid")
};

/**
 * Configuração visual do slot
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilSlotVisualConfig
{
    GENERATED_BODY()

    // Materiais por estado
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
    TMap<ESigilSlotState, TSoftObjectPtr<UMaterialInterface>> StateMaterials;

    // Cores por raridade
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
    TMap<ESigilRarity, FLinearColor> RarityColors;

    // Efeitos VFX por estado
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TMap<ESigilSlotState, TSoftObjectPtr<UNiagaraSystem>> StateVFX;

    // Sons por ação
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    TSoftObjectPtr<USoundBase> EquipSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    TSoftObjectPtr<USoundBase> UnequipSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    TSoftObjectPtr<USoundBase> FusionSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    TSoftObjectPtr<USoundBase> InvalidSound;

    // Configurações de animação
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float SlotAnimationDuration = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float FusionAnimationDuration = 6.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float HighlightPulseDuration = 1.0f;

    FSigilSlotVisualConfig()
    {
        // Configurações padrão
        RarityColors.Add(ESigilRarity::Common, FLinearColor::White);
        RarityColors.Add(ESigilRarity::Rare, FLinearColor::Blue);
        RarityColors.Add(ESigilRarity::Epic, FLinearColor(0.5f, 0.0f, 1.0f, 1.0f)); // Roxo
        RarityColors.Add(ESigilRarity::Legendary, FLinearColor(1.0f, 0.5f, 0.0f, 1.0f)); // Dourado
    }
};

/**
 * Dados de notificação do sistema
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilNotificationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    FText Title;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    FText Description;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    TSoftObjectPtr<UTexture2D> Icon;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    FLinearColor Color = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    float Duration = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    ESigilRarity NotificationRarity = ESigilRarity::Common;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    FGameplayTagContainer NotificationTags;
};

// ========================================
// DRAG & DROP OPERATION
// ========================================

/**
 * Operação de drag & drop para sígilos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilDragDropOperation : public UDragDropOperation
{
    GENERATED_BODY()

public:
    USigilDragDropOperation();

    // Dados do sigilo sendo arrastado
    UPROPERTY(BlueprintReadWrite, Category = "Drag Drop")
    TObjectPtr<ASigilItem> DraggedSigil;

    UPROPERTY(BlueprintReadWrite, Category = "Drag Drop")
    int32 SourceSlotIndex = -1;

    UPROPERTY(BlueprintReadWrite, Category = "Drag Drop")
    TObjectPtr<class USigilSlotWidget> SourceSlotWidget;

    // Visual do drag
    UPROPERTY(BlueprintReadWrite, Category = "Drag Drop")
    TObjectPtr<UUserWidget> DragVisual;

    UPROPERTY(BlueprintReadWrite, Category = "Drag Drop")
    FVector2D DragOffset = FVector2D::ZeroVector;

    // Configurações de validação
    UPROPERTY(BlueprintReadWrite, Category = "Drag Drop")
    bool bCanDropOnEmpty = true;

    UPROPERTY(BlueprintReadWrite, Category = "Drag Drop")
    bool bCanSwapSigils = true;

    UPROPERTY(BlueprintReadWrite, Category = "Drag Drop")
    bool bCanDropOnSameSlot = false;

    // Funções de validação
    UFUNCTION(BlueprintCallable, Category = "Drag Drop")
    bool CanDropOnSlot(class USigilSlotWidget* TargetSlot) const;

    UFUNCTION(BlueprintCallable, Category = "Drag Drop")
    bool IsValidDrop() const;

    // Eventos
    UFUNCTION(BlueprintImplementableEvent, Category = "Drag Drop")
    void OnDragStarted();

    UFUNCTION(BlueprintImplementableEvent, Category = "Drag Drop")
    void OnDragCancelled();

    UFUNCTION(BlueprintImplementableEvent, Category = "Drag Drop")
    void OnDropCompleted(bool bSuccess);
};

// ========================================
// SLOT WIDGET
// ========================================

/**
 * Widget individual para slot de sigilo
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilSlotWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    USigilSlotWidget(const FObjectInitializer& ObjectInitializer);

    // ========================================
    // COMPONENTES UI
    // ========================================

    // Componente principal do slot
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UBorder> SlotBorder;

    // Imagem do sigilo
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UImage> SigilImage;

    // Overlay para efeitos
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UOverlay> EffectOverlay;

    // Barra de progresso de fusão
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UProgressBar> FusionProgressBar;

    // Texto de timer
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UTextBlock> TimerText;

    // Indicador de raridade
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UImage> RarityIndicator;

    // Botão invisível para interação
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UButton> InteractionButton;

    // Indicador de bloqueio
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UImage> LockIndicator;

    // ========================================
    // PROPRIEDADES
    // ========================================

    // Índice do slot
    UPROPERTY(BlueprintReadWrite, Category = "Slot")
    int32 SlotIndex = 0;

    // Sigilo equipado
    UPROPERTY(BlueprintReadWrite, Category = "Slot")
    TObjectPtr<ASigilItem> EquippedSigil;

    // Estado atual do slot
    UPROPERTY(BlueprintReadWrite, Category = "Slot")
    ESigilSlotState CurrentState = ESigilSlotState::Empty;

    // Configuração visual
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
    FSigilSlotVisualConfig VisualConfig;

    // Referência ao manager
    UPROPERTY(BlueprintReadWrite, Category = "Slot")
    TObjectPtr<USigilManagerComponent> SigilManager;

    // Componente VFX
    UPROPERTY(BlueprintReadOnly, Category = "VFX")
    TObjectPtr<UNiagaraComponent> VFXComponent;

    // ========================================
    // FUNÇÕES PRINCIPAIS
    // ========================================

    // Inicialização
    UFUNCTION(BlueprintCallable, Category = "Slot")
    void InitializeSlot(int32 InSlotIndex, USigilManagerComponent* InSigilManager);

    // Equipar/Desequipar
    UFUNCTION(BlueprintCallable, Category = "Slot")
    bool EquipSigil(ASigilItem* Sigil);

    UFUNCTION(BlueprintCallable, Category = "Slot")
    bool UnequipSigil();

    // Atualizar estado
    UFUNCTION(BlueprintCallable, Category = "Slot")
    void UpdateSlotState(ESigilSlotState NewState);

    UFUNCTION(BlueprintCallable, Category = "Slot")
    void UpdateFusionProgress(float Progress);

    UFUNCTION(BlueprintCallable, Category = "Slot")
    void UpdateTimerDisplay(float RemainingTime);

    // Validação
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Slot")
    bool IsSlotEmpty() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Slot")
    bool IsSlotLocked() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Slot")
    bool CanAcceptSigil(ASigilItem* Sigil) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Slot")
    bool IsFusionReady() const;

    // ========================================
    // DRAG & DROP
    // ========================================

    // Eventos de drag & drop
    virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
    virtual void NativeOnDragDetected(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent, UDragDropOperation*& OutOperation) override;
    virtual bool NativeOnDrop(const FGeometry& InGeometry, const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation) override;
    virtual void NativeOnDragEnter(const FGeometry& InGeometry, const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation) override;
    virtual void NativeOnDragLeave(const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation) override;

    // Funções de drag & drop
    UFUNCTION(BlueprintCallable, Category = "Drag Drop")
    USigilDragDropOperation* CreateDragDropOperation();

    UFUNCTION(BlueprintCallable, Category = "Drag Drop")
    bool HandleDrop(USigilDragDropOperation* DropOperation);

    UFUNCTION(BlueprintCallable, Category = "Drag Drop")
    void SetDragHighlight(bool bHighlighted);

    // ========================================
    // VISUAL & ANIMAÇÕES
    // ========================================

    // Atualizar visual
    UFUNCTION(BlueprintCallable, Category = "Visual")
    void UpdateVisualState();

    UFUNCTION(BlueprintCallable, Category = "Visual")
    void UpdateRarityIndicator();

    UFUNCTION(BlueprintCallable, Category = "Visual")
    void PlaySlotAnimation(ESigilSlotState FromState, ESigilSlotState ToState);

    // VFX
    UFUNCTION(BlueprintCallable, Category = "VFX")
    void PlayVFXEffect(ESigilSlotState EffectState);

    UFUNCTION(BlueprintCallable, Category = "VFX")
    void StopVFXEffect();

    // Audio
    UFUNCTION(BlueprintCallable, Category = "Audio")
    void PlaySlotSound(ESigilSlotState SoundState);

    // ========================================
    // EVENTOS BLUEPRINT
    // ========================================

    // Eventos de slot
    UFUNCTION(BlueprintImplementableEvent, Category = "Slot Events")
    void OnSigilEquipped(ASigilItem* Sigil);

    UFUNCTION(BlueprintImplementableEvent, Category = "Slot Events")
    void OnSigilUnequipped(ASigilItem* Sigil);

    UFUNCTION(BlueprintImplementableEvent, Category = "Slot Events")
    void OnFusionStarted();

    UFUNCTION(BlueprintImplementableEvent, Category = "Slot Events")
    void OnFusionCompleted();

    UFUNCTION(BlueprintImplementableEvent, Category = "Slot Events")
    void OnSlotUnlocked();

    UFUNCTION(BlueprintImplementableEvent, Category = "Slot Events")
    void OnSlotClicked();

    UFUNCTION(BlueprintImplementableEvent, Category = "Slot Events")
    void OnSlotHovered(bool bHovered);

    // Eventos de drag & drop
    UFUNCTION(BlueprintImplementableEvent, Category = "Drag Drop Events")
    void OnDragStarted(USigilDragDropOperation* DragOperation);

    UFUNCTION(BlueprintImplementableEvent, Category = "Drag Drop Events")
    void OnDragEntered(USigilDragDropOperation* DragOperation);

    UFUNCTION(BlueprintImplementableEvent, Category = "Drag Drop Events")
    void OnDragLeft(USigilDragDropOperation* DragOperation);

    UFUNCTION(BlueprintImplementableEvent, Category = "Drag Drop Events")
    void OnDropReceived(USigilDragDropOperation* DragOperation, bool bSuccess);

protected:
    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================

    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;
    virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

    // Bind de eventos
    UFUNCTION()
    void OnInteractionButtonClicked();

    UFUNCTION()
    void OnInteractionButtonHovered();

    UFUNCTION()
    void OnInteractionButtonUnhovered();

    // Helpers internos
    void UpdateSlotMaterial();
    void UpdateSlotColor();
    void SetupVFXComponent();
    FText FormatTimerText(float TimeInSeconds) const;

private:
    // ========================================
    // VARIÁVEIS INTERNAS
    // ========================================

    // Estado anterior para animações
    ESigilSlotState PreviousState = ESigilSlotState::Empty;

    // Timer para animações
    float AnimationTimer = 0.0f;
    bool bIsAnimating = false;

    // Cache de materiais
    TMap<ESigilSlotState, TObjectPtr<UMaterialInterface>> CachedMaterials;

    // Flags de estado
    bool bIsDragTarget = false;
    bool bIsHighlighted = false;
    bool bIsHovered = false;
};

// ========================================
// SIGIL INVENTORY WIDGET
// ========================================

/**
 * Widget principal do inventário de sígilos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilInventoryWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    USigilInventoryWidget(const FObjectInitializer& ObjectInitializer);

    // ========================================
    // COMPONENTES UI
    // ========================================

    // Container principal
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UCanvasPanel> MainCanvas;

    // Container dos slots
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UHorizontalBox> SlotsContainer;

    // Painel de estatísticas
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UVerticalBox> StatsPanel;

    // Texto de poder total
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UTextBlock> TotalPowerText;

    // Texto de sígilos equipados
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UTextBlock> EquippedSigilsText;

    // Botão de reforge
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UButton> ReforgeButton;

    // Texto de cooldown de reforge
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UTextBlock> ReforgeCooldownText;

    // ========================================
    // PROPRIEDADES
    // ========================================

    // Slots de sígilos
    UPROPERTY(BlueprintReadWrite, Category = "Inventory")
    TArray<TObjectPtr<USigilSlotWidget>> SigilSlots;

    // Número máximo de slots
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
    int32 MaxSlots = 6;

    // Referência ao manager
    UPROPERTY(BlueprintReadWrite, Category = "Inventory")
    TObjectPtr<USigilManagerComponent> SigilManager;

    // Classe do slot widget
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
    TSubclassOf<USigilSlotWidget> SlotWidgetClass;

    // ========================================
    // FUNÇÕES PRINCIPAIS
    // ========================================

    // Inicialização
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    void InitializeInventory(USigilManagerComponent* InSigilManager);

    // Gerenciamento de slots
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    void CreateSlots();

    UFUNCTION(BlueprintCallable, Category = "Inventory")
    void UpdateAllSlots();

    UFUNCTION(BlueprintCallable, Category = "Inventory")
    void UnlockSlot(int32 SlotIndex);

    // Atualização de dados
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    void UpdateStats();

    UFUNCTION(BlueprintCallable, Category = "Inventory")
    void UpdateReforgeButton();

    // Obter slots
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
    USigilSlotWidget* GetSlotWidget(int32 SlotIndex) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
    TArray<USigilSlotWidget*> GetAllSlotWidgets() const;

    // ========================================
    // EVENTOS BLUEPRINT
    // ========================================

    UFUNCTION(BlueprintImplementableEvent, Category = "Inventory Events")
    void OnInventoryInitialized();

    UFUNCTION(BlueprintImplementableEvent, Category = "Inventory Events")
    void OnSlotUnlocked(int32 SlotIndex);

    UFUNCTION(BlueprintImplementableEvent, Category = "Inventory Events")
    void OnStatsUpdated(const FSigilSystemStats& NewStats);

    UFUNCTION(BlueprintImplementableEvent, Category = "Inventory Events")
    void OnReforgeAvailable(bool bAvailable);

protected:
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;
    virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

    // Bind de eventos
    UFUNCTION()
    void OnReforgeButtonClicked();

    // Callbacks do manager
    UFUNCTION()
    void OnSigilEquipped(int32 SlotIndex, ASigilItem* Sigil);

    UFUNCTION()
    void OnSigilUnequipped(int32 SlotIndex, ASigilItem* Sigil);

    UFUNCTION()
    void OnFusionCompleted(int32 SlotIndex, ASigilItem* FusedSigil);

    UFUNCTION()
    void OnSlotUnlockedCallback(int32 SlotIndex);

    UFUNCTION()
    void OnStatsChanged(const FSigilSystemStats& NewStats);

private:
    // Flags de estado
    bool bIsInitialized = false;
    float LastStatsUpdate = 0.0f;
};

// ========================================
// NOTIFICATION WIDGET
// ========================================

/**
 * Widget para notificações do sistema de sígilos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilNotificationWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    USigilNotificationWidget(const FObjectInitializer& ObjectInitializer);

    // ========================================
    // COMPONENTES UI
    // ========================================

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UBorder> NotificationBorder;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UImage> NotificationIcon;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UTextBlock> TitleText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UTextBlock> DescriptionText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UProgressBar> DurationBar;

    // ========================================
    // FUNÇÕES PRINCIPAIS
    // ========================================

    UFUNCTION(BlueprintCallable, Category = "Notification")
    void ShowNotification(const FSigilNotificationData& NotificationData);

    UFUNCTION(BlueprintCallable, Category = "Notification")
    void HideNotification();

    // ========================================
    // EVENTOS BLUEPRINT
    // ========================================

    UFUNCTION(BlueprintImplementableEvent, Category = "Notification Events")
    void OnNotificationShown(const FSigilNotificationData& Data);

    UFUNCTION(BlueprintImplementableEvent, Category = "Notification Events")
    void OnNotificationHidden();

protected:
    virtual void NativeConstruct() override;
    virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

private:
    FSigilNotificationData CurrentNotification;
    float RemainingDuration = 0.0f;
    bool bIsShowing = false;
};

// ========================================
// HUD PRINCIPAL
// ========================================

/**
 * HUD principal do sistema de sígilos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilHUDWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    USigilHUDWidget(const FObjectInitializer& ObjectInitializer);

    // ========================================
    // COMPONENTES UI
    // ========================================

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<USigilInventoryWidget> InventoryWidget;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "UI Components")
    TObjectPtr<UVerticalBox> NotificationsContainer;

    // ========================================
    // PROPRIEDADES
    // ========================================

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD")
    TSubclassOf<USigilNotificationWidget> NotificationWidgetClass;

    UPROPERTY(BlueprintReadWrite, Category = "HUD")
    TArray<TObjectPtr<USigilNotificationWidget>> ActiveNotifications;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD")
    int32 MaxNotifications = 5;

    // ========================================
    // FUNÇÕES PRINCIPAIS
    // ========================================

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void InitializeHUD(USigilManagerComponent* SigilManager);

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void ShowNotification(const FSigilNotificationData& NotificationData);

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void ClearAllNotifications();

    // ========================================
    // EVENTOS BLUEPRINT
    // ========================================

    UFUNCTION(BlueprintImplementableEvent, Category = "HUD Events")
    void OnHUDInitialized();

protected:
    virtual void NativeConstruct() override;

private:
    void RemoveOldestNotification();
};

// ========================================
// CONSTANTES E HELPERS
// ========================================

namespace SigilWidgetConstants
{
    // Durações de animação
    constexpr float SLOT_ANIMATION_DURATION = 0.3f;
    constexpr float FUSION_ANIMATION_DURATION = 6.0f;
    constexpr float NOTIFICATION_DURATION = 3.0f;
    
    // Tamanhos
    constexpr float SLOT_SIZE = 64.0f;
    constexpr float NOTIFICATION_HEIGHT = 80.0f;
    
    // Cores padrão
    const FLinearColor COMMON_COLOR = FLinearColor::White;
    const FLinearColor RARE_COLOR = FLinearColor::Blue;
    const FLinearColor EPIC_COLOR = FLinearColor(0.5f, 0.0f, 1.0f, 1.0f);
    const FLinearColor LEGENDARY_COLOR = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f);
}