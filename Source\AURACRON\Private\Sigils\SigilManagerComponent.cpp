// SigilManagerComponent.cpp
// AURACRON - Sistema de Sígilos
// Implementação do componente principal para gerenciar sígilos em MOBA 5x5
// APIs verificadas: AbilitySystemComponent.h, GameplayTags.h, GameplayEffect.h

#include "Sigils/SigilManagerComponent.h"
#include "Sigils/SigilItem.h"
#include "Sigils/SigilAttributeSet.h"
#include "Fusion/SigilFusionSystem.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "GameplayAbilities/Public/GameplayEffect.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "GameplayTagsManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Character.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "VFX/SigilVFXManager.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Kismet/GameplayStatics.h"

USigilManagerComponent::USigilManagerComponent()
{
    // Configurar componente para replicação
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // Tick a cada 100ms
    SetIsReplicatedByDefault(true);

    // Configurações padrão
    MaxSigilSlots = 6;
    bAutoFusionEnabled = true;
    bVisualNotificationsEnabled = true;
    ReforgeCooldownSeconds = DEFAULT_REFORGE_COOLDOWN;
    bSystemActive = true;
    LastReforgeTimestamp = 0.0f;
    LastUpdateTimestamp = 0.0f;
    bIsInitialized = false;

    // Configurar tags do sistema
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.System.Active")))
    {
        SystemTags.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.System.Active")));
    }

    // Inicializar configuração de fusão
    FusionConfig = FSigilFusionConfig();

    // Inicializar estatísticas
    SystemStats = FSigilSystemStats();
}

void USigilManagerComponent::BeginPlay()
{
    Super::BeginPlay();

    // Inicializar sistema apenas no servidor ou em single player
    if (GetOwner()->HasAuthority())
    {
        InitializeSigilSlots();
        
        // Configurar timer de validação periódica
        GetWorld()->GetTimerManager().SetTimer(
            ValidationTimerHandle,
            [this]()
            {
                if (ValidateSystemConfiguration())
                {
                    UpdateSystemStatistics();
                }
            },
            VALIDATION_INTERVAL,
            true
        );
        
        bIsInitialized = true;
        UE_LOG(LogTemp, Log, TEXT("SigilManagerComponent initialized for %s"), *GetOwner()->GetName());
    }

    // Cache componentes importantes
    CachedAbilitySystemComponent = GetOwnerAbilitySystemComponent();
    CachedSigilAttributeSet = GetOwnerSigilAttributeSet();

    // Inicializar habilidades exclusivas
    InitializeExclusiveAbilities();

    // Inicializar componentes VFX para cada slot
    SlotVFXComponents.SetNum(MaxSigilSlots);
    for (int32 i = 0; i < MaxSigilSlots; i++)
    {
        SlotVFXComponents[i] = NewObject<UNiagaraComponent>(this);
        if (SlotVFXComponents[i])
        {
            SlotVFXComponents[i]->SetupAttachment(GetOwner()->GetRootComponent());
            SlotVFXComponents[i]->SetAutoActivate(false);
        }
    }
}

void USigilManagerComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bIsInitialized || !bSystemActive)
    {
        return;
    }

    // Atualizar apenas no servidor
    if (GetOwner()->HasAuthority())
    {
        UpdateFusionTimers(DeltaTime);
        LastUpdateTimestamp = GetWorld()->GetTimeSeconds();
    }
}

void USigilManagerComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar dados principais
    DOREPLIFETIME(USigilManagerComponent, SigilSlots);
    DOREPLIFETIME(USigilManagerComponent, SystemStats);
    DOREPLIFETIME(USigilManagerComponent, LastReforgeTimestamp);
    DOREPLIFETIME(USigilManagerComponent, bSystemActive);
}

void USigilManagerComponent::InitializeSigilSlots()
{
    SigilSlots.Empty();
    SigilSlots.SetNum(MaxSigilSlots);
    ActiveSigilEffects.SetNum(MaxSigilSlots);

    for (int32 i = 0; i < MaxSigilSlots; i++)
    {
        FSigilSlotData& SlotData = SigilSlots[i];
        SlotData.bIsUnlocked = (i < 3); // Primeiros 3 slots desbloqueados
        SlotData.UnlockLevel = (i < 3) ? 1 : (i * 2); // Levels 1, 1, 1, 6, 8, 10
        
        // Configurar tags do slot
        FString SlotTagString = FString::Printf(TEXT("Sigil.Slot.%d"), i);
        if (UGameplayTagsManager::Get().IsValidGameplayTagString(SlotTagString))
        {
            SlotData.SlotTags.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName(*SlotTagString)));
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Initialized %d sigil slots"), MaxSigilSlots);
}

void USigilManagerComponent::UpdateFusionTimers(float DeltaTime)
{
    if (!bAutoFusionEnabled)
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();

    for (int32 i = 0; i < SigilSlots.Num(); i++)
    {
        FSigilSlotData& SlotData = SigilSlots[i];
        
        if (SlotData.EquippedSigil && !SlotData.bReadyForFusion)
        {
            float ElapsedTime = CurrentTime - SlotData.EquipTimestamp;
            
            if (ElapsedTime >= FusionConfig.FusionTimeSeconds)
            {
                ProcessAutoFusion(i);
            }
        }
    }
}

void USigilManagerComponent::ProcessAutoFusion(int32 SlotIndex)
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return;
    }

    FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    ASigilItem* Sigil = SlotData.EquippedSigil;
    
    if (!Sigil)
    {
        return;
    }

    // Calcular multiplicador baseado na raridade
    float Multiplier = 1.0f;
    if (FusionConfig.RarityMultipliers.Contains(Sigil->GetSigilRarity()))
    {
        Multiplier = FusionConfig.RarityMultipliers[Sigil->GetSigilRarity()];
    }

    // Aplicar fusão
    SlotData.bReadyForFusion = true;
    SlotData.FusionMultiplier = Multiplier;

    // Aplicar GameplayEffect de fusão
    if (FusionConfig.FusionEffect && CachedAbilitySystemComponent)
    {
        FGameplayEffectContextHandle EffectContext = CachedAbilitySystemComponent->MakeEffectContext();
        FGameplayEffectSpecHandle EffectSpec = CachedAbilitySystemComponent->MakeOutgoingSpec(
            FusionConfig.FusionEffect, 1.0f, EffectContext);
        
        if (EffectSpec.IsValid())
        {
            // Adicionar magnitude do multiplicador
            EffectSpec.Data->SetSetByCallerMagnitude(
                UGameplayTagsManager::Get().RequestGameplayTag(FName("Data.FusionMultiplier")), 
                Multiplier);
            
            ActiveSigilEffects[SlotIndex] = CachedAbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data);
        }
    }

    // Aplicar multiplicador aos atributos do sigilo
    if (CachedSigilAttributeSet)
    {
        CachedSigilAttributeSet->ApplyFusionMultiplier(Multiplier);
    }

    // Notificar fusão
    OnSigilFusion.Broadcast(Sigil, Multiplier);
    MulticastNotifyFusion(SlotIndex, Multiplier);

    // Atualizar estatísticas
    SystemStats.TotalFusedSigils++;
    UpdateSystemStatistics();

    UE_LOG(LogTemp, Log, TEXT("Auto-fusion applied to slot %d with multiplier %f"), SlotIndex, Multiplier);
}

bool USigilManagerComponent::EquipSigil(ASigilItem* Sigil, int32 SlotIndex)
{
    // Validações
    if (!CanEquipSigil(Sigil, SlotIndex))
    {
        return false;
    }

    // Executar no servidor
    if (!GetOwner()->HasAuthority())
    {
        ServerEquipSigil(Sigil, SlotIndex);
        return true; // Assumir sucesso para responsividade
    }

    FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    
    // Desequipar sigilo anterior se existir
    if (SlotData.EquippedSigil)
    {
        UnequipSigil(SlotIndex);
    }

    // Equipar novo sigilo
    SlotData.EquippedSigil = Sigil;
    SlotData.EquipTimestamp = GetWorld()->GetTimeSeconds();
    SlotData.bReadyForFusion = false;
    SlotData.FusionMultiplier = 1.0f;

    // Aplicar efeitos do sigilo
    ApplySigilEffects(Sigil, true);

    // Configurar sigilo
    if (Sigil)
    {
        Sigil->SetSigilState(ESigilState::Equipped);
        Sigil->SetOwningSlot(SlotIndex);
    }

    // Disparar evento
    FGameplayTag EquipTag = UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Event.Equipped"));
    OnSigilEvent.Broadcast(Sigil, SlotIndex, EquipTag);

    // Efeitos visuais
    MulticastPlayVFX(SlotIndex, UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.VFX.Equip")));

    // Atualizar estatísticas
    UpdateSystemStatistics();

    UE_LOG(LogTemp, Log, TEXT("Sigil equipped in slot %d: %s"), SlotIndex, Sigil ? *Sigil->GetName() : TEXT("None"));
    return true;
}

ASigilItem* USigilManagerComponent::UnequipSigil(int32 SlotIndex)
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return nullptr;
    }

    // Executar no servidor
    if (!GetOwner()->HasAuthority())
    {
        ServerUnequipSigil(SlotIndex);
        return nullptr; // Cliente não pode retornar o sigilo diretamente
    }

    FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    ASigilItem* RemovedSigil = SlotData.EquippedSigil;

    if (!RemovedSigil)
    {
        return nullptr;
    }

    // Remover efeitos do sigilo
    ApplySigilEffects(RemovedSigil, false);

    // Remover GameplayEffect ativo
    if (ActiveSigilEffects[SlotIndex].IsValid() && CachedAbilitySystemComponent)
    {
        CachedAbilitySystemComponent->RemoveActiveGameplayEffect(ActiveSigilEffects[SlotIndex]);
        ActiveSigilEffects[SlotIndex] = FActiveGameplayEffectHandle();
    }

    // Limpar dados do slot
    SlotData.EquippedSigil = nullptr;
    SlotData.EquipTimestamp = 0.0f;
    SlotData.bReadyForFusion = false;
    SlotData.FusionMultiplier = 1.0f;

    // Configurar sigilo removido
    RemovedSigil->SetSigilState(ESigilState::Unequipped);
    RemovedSigil->SetOwningSlot(-1);

    // Disparar evento
    FGameplayTag UnequipTag = UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Event.Unequipped"));
    OnSigilEvent.Broadcast(RemovedSigil, SlotIndex, UnequipTag);

    // Efeitos visuais
    MulticastPlayVFX(SlotIndex, UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.VFX.Unequip")));

    // Atualizar estatísticas
    UpdateSystemStatistics();

    UE_LOG(LogTemp, Log, TEXT("Sigil unequipped from slot %d: %s"), SlotIndex, *RemovedSigil->GetName());
    return RemovedSigil;
}

bool USigilManagerComponent::SwapSigils(int32 FromSlot, int32 ToSlot)
{
    if (!IsValidSlotIndex(FromSlot) || !IsValidSlotIndex(ToSlot))
    {
        return false;
    }

    if (FromSlot == ToSlot)
    {
        return false;
    }

    // Verificar se ambos os slots estão desbloqueados
    if (!IsSlotUnlocked(FromSlot) || !IsSlotUnlocked(ToSlot))
    {
        return false;
    }

    // Executar no servidor
    if (!GetOwner()->HasAuthority())
    {
        // Implementar RPC para swap se necessário
        return false;
    }

    FSigilSlotData& FromSlotData = SigilSlots[FromSlot];
    FSigilSlotData& ToSlotData = SigilSlots[ToSlot];

    // Trocar sígilos
    ASigilItem* TempSigil = FromSlotData.EquippedSigil;
    FromSlotData.EquippedSigil = ToSlotData.EquippedSigil;
    ToSlotData.EquippedSigil = TempSigil;

    // Atualizar slots dos sígilos
    if (FromSlotData.EquippedSigil)
    {
        FromSlotData.EquippedSigil->SetOwningSlot(FromSlot);
    }
    if (ToSlotData.EquippedSigil)
    {
        ToSlotData.EquippedSigil->SetOwningSlot(ToSlot);
    }

    // Trocar timestamps (manter progresso de fusão)
    float TempTimestamp = FromSlotData.EquipTimestamp;
    FromSlotData.EquipTimestamp = ToSlotData.EquipTimestamp;
    ToSlotData.EquipTimestamp = TempTimestamp;

    // Trocar status de fusão
    bool TempFusionReady = FromSlotData.bReadyForFusion;
    FromSlotData.bReadyForFusion = ToSlotData.bReadyForFusion;
    ToSlotData.bReadyForFusion = TempFusionReady;

    float TempMultiplier = FromSlotData.FusionMultiplier;
    FromSlotData.FusionMultiplier = ToSlotData.FusionMultiplier;
    ToSlotData.FusionMultiplier = TempMultiplier;

    UE_LOG(LogTemp, Log, TEXT("Swapped sigils between slots %d and %d"), FromSlot, ToSlot);
    return true;
}

bool USigilManagerComponent::ForceFuseSigil(int32 SlotIndex)
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return false;
    }

    // Executar no servidor
    if (!GetOwner()->HasAuthority())
    {
        ServerForceFusion(SlotIndex);
        return true;
    }

    FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    
    if (!SlotData.EquippedSigil || SlotData.bReadyForFusion)
    {
        return false;
    }

    // Forçar fusão imediatamente
    ProcessAutoFusion(SlotIndex);
    return true;
}

bool USigilManagerComponent::ReforgeSigil(int32 SlotIndex)
{
    if (!CanReforge() || !IsValidSlotIndex(SlotIndex))
    {
        return false;
    }

    // Executar no servidor
    if (!GetOwner()->HasAuthority())
    {
        ServerReforge(SlotIndex);
        return true;
    }

    FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    ASigilItem* Sigil = SlotData.EquippedSigil;
    
    if (!Sigil)
    {
        return false;
    }

    // Remover fusão se aplicada
    if (SlotData.bReadyForFusion)
    {
        if (CachedSigilAttributeSet)
        {
            CachedSigilAttributeSet->RemoveFusionMultiplier();
        }
        
        SlotData.bReadyForFusion = false;
        SlotData.FusionMultiplier = 1.0f;
    }

    // Reset timestamp para reiniciar timer de fusão
    SlotData.EquipTimestamp = GetWorld()->GetTimeSeconds();
    
    // Atualizar cooldown de reforge
    LastReforgeTimestamp = GetWorld()->GetTimeSeconds();

    // Reconfigurar sigilo (implementar lógica específica se necessário)
    Sigil->RegenerateSigilData();

    // Disparar evento
    FGameplayTag ReforgeTag = UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Event.Reforged"));
    OnSigilEvent.Broadcast(Sigil, SlotIndex, ReforgeTag);

    // Efeitos visuais
    MulticastPlayVFX(SlotIndex, UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.VFX.Reforge")));

    UE_LOG(LogTemp, Log, TEXT("Sigil reforged in slot %d"), SlotIndex);
    return true;
}

bool USigilManagerComponent::UnlockSigilSlot(int32 SlotIndex)
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return false;
    }

    FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    
    if (SlotData.bIsUnlocked)
    {
        return false; // Já desbloqueado
    }

    // Verificar level necessário (implementar verificação de level do jogador)
    // Por enquanto, desbloquear diretamente
    SlotData.bIsUnlocked = true;

    // Disparar evento
    OnSigilSlotUnlocked.Broadcast(SlotIndex);

    // Efeitos visuais
    MulticastPlayVFX(SlotIndex, UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.VFX.SlotUnlock")));

    UE_LOG(LogTemp, Log, TEXT("Sigil slot %d unlocked"), SlotIndex);
    return true;
}

// ========================================
// FUNÇÕES DE CONSULTA
// ========================================

ASigilItem* USigilManagerComponent::GetEquippedSigil(int32 SlotIndex) const
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return nullptr;
    }
    
    return SigilSlots[SlotIndex].EquippedSigil;
}

bool USigilManagerComponent::IsSlotUnlocked(int32 SlotIndex) const
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return false;
    }
    
    return SigilSlots[SlotIndex].bIsUnlocked;
}

bool USigilManagerComponent::IsSigilReadyForFusion(int32 SlotIndex) const
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return false;
    }
    
    return SigilSlots[SlotIndex].bReadyForFusion;
}

float USigilManagerComponent::GetTimeToFusion(int32 SlotIndex) const
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return 0.0f;
    }
    
    const FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    
    if (!SlotData.EquippedSigil || SlotData.bReadyForFusion)
    {
        return 0.0f;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float ElapsedTime = CurrentTime - SlotData.EquipTimestamp;
    float RemainingTime = FusionConfig.FusionTimeSeconds - ElapsedTime;
    
    return FMath::Max(0.0f, RemainingTime);
}

float USigilManagerComponent::GetFusionProgress(int32 SlotIndex) const
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return 0.0f;
    }
    
    const FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    
    if (!SlotData.EquippedSigil)
    {
        return 0.0f;
    }
    
    // Buscar o FusionSystem no owner
    if (AActor* Owner = GetOwner())
    {
        if (USigilFusionSystem* FusionSystem = Owner->FindComponentByClass<USigilFusionSystem>())
        {
            // Obter fusões ativas para este sigilo
            TArray<FSigilFusionInstance> ActiveFusions = FusionSystem->GetActiveFusions(Owner);
            for (const FSigilFusionInstance& Fusion : ActiveFusions)
            {
                // Verificar se alguma fusão envolve este sigilo
                if (Fusion.InputSigils.Contains(SlotData.EquippedSigil))
                {
                    return FusionSystem->GetFusionProgress(Fusion.FusionID);
                }
            }
        }
    }
    
    // Se não há fusão ativa, calcular progresso baseado no tempo de equipamento
    if (SlotData.bReadyForFusion)
    {
        return 1.0f;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float ElapsedTime = CurrentTime - SlotData.EquipTimestamp;
    float Progress = ElapsedTime / FusionConfig.FusionTimeSeconds;
    
    return FMath::Clamp(Progress, 0.0f, 1.0f);
}

bool USigilManagerComponent::CanReforge() const
{
    if (!bSystemActive)
    {
        return false;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float TimeSinceLastReforge = CurrentTime - LastReforgeTimestamp;
    
    return TimeSinceLastReforge >= ReforgeCooldownSeconds;
}

float USigilManagerComponent::GetReforgeTimeRemaining() const
{
    if (CanReforge())
    {
        return 0.0f;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float TimeSinceLastReforge = CurrentTime - LastReforgeTimestamp;
    
    return ReforgeCooldownSeconds - TimeSinceLastReforge;
}

TArray<ASigilItem*> USigilManagerComponent::GetAllEquippedSigils() const
{
    TArray<ASigilItem*> EquippedSigils;
    
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil)
        {
            EquippedSigils.Add(SlotData.EquippedSigil);
        }
    }
    
    return EquippedSigils;
}

TArray<ASigilItem*> USigilManagerComponent::GetSigilsByType(ESigilType Type) const
{
    TArray<ASigilItem*> SigilsByType;
    
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil && SlotData.EquippedSigil->GetSigilType() == Type)
        {
            SigilsByType.Add(SlotData.EquippedSigil);
        }
    }
    
    return SigilsByType;
}

TArray<ASigilItem*> USigilManagerComponent::GetSigilsByRarity(ESigilRarity Rarity) const
{
    TArray<ASigilItem*> SigilsByRarity;
    
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil && SlotData.EquippedSigil->GetSigilRarity() == Rarity)
        {
            SigilsByRarity.Add(SlotData.EquippedSigil);
        }
    }
    
    return SigilsByRarity;
}

float USigilManagerComponent::CalculateTotalSigilPower() const
{
    if (CachedSigilAttributeSet)
    {
        return CachedSigilAttributeSet->CalculateTotalSigilPower();
    }
    
    // Fallback: calcular manualmente
    float TotalPower = 0.0f;
    
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil)
        {
            TotalPower += SlotData.EquippedSigil->GetTotalSpectralPower() * SlotData.FusionMultiplier;
        }
    }
    
    return TotalPower;
}

FSigilSystemStats USigilManagerComponent::GetSystemStatistics() const
{
    return SystemStats;
}

ASigilItem* USigilManagerComponent::GetSigilByID(int32 SigilID) const
{
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil && SlotData.EquippedSigil->GetSigilID() == SigilID)
        {
            return SlotData.EquippedSigil;
        }
    }
    return nullptr;
}

// ========================================
// FUNÇÕES DE VALIDAÇÃO
// ========================================

bool USigilManagerComponent::CanEquipSigil(ASigilItem* Sigil, int32 SlotIndex) const
{
    if (!Sigil || !IsValidSlotIndex(SlotIndex))
    {
        return false;
    }
    
    if (!IsSlotUnlocked(SlotIndex))
    {
        return false;
    }
    
    if (!bSystemActive)
    {
        return false;
    }
    
    // Verificar se sigilo já está equipado em outro slot
    for (int32 i = 0; i < SigilSlots.Num(); i++)
    {
        if (SigilSlots[i].EquippedSigil == Sigil)
        {
            return false;
        }
    }
    
    return true;
}

bool USigilManagerComponent::IsValidSlotIndex(int32 SlotIndex) const
{
    return SlotIndex >= 0 && SlotIndex < SigilSlots.Num();
}

// ========================================
// FUNÇÕES DE DEBUGGING
// ========================================

void USigilManagerComponent::DEBUG_UnlockAllSlots()
{
    for (FSigilSlotData& SlotData : SigilSlots)
    {
        SlotData.bIsUnlocked = true;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("DEBUG: All sigil slots unlocked"));
}

void USigilManagerComponent::DEBUG_ForceAllFusions()
{
    for (int32 i = 0; i < SigilSlots.Num(); i++)
    {
        if (SigilSlots[i].EquippedSigil && !SigilSlots[i].bReadyForFusion)
        {
            ProcessAutoFusion(i);
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("DEBUG: All sigils force-fused"));
}

void USigilManagerComponent::DEBUG_ResetSystem()
{
    // Desequipar todos os sígilos
    for (int32 i = 0; i < SigilSlots.Num(); i++)
    {
        if (SigilSlots[i].EquippedSigil)
        {
            UnequipSigil(i);
        }
    }
    
    // Reset estatísticas
    SystemStats = FSigilSystemStats();
    
    // Reset timestamps
    LastReforgeTimestamp = 0.0f;
    
    UE_LOG(LogTemp, Warning, TEXT("DEBUG: Sigil system reset"));
}

void USigilManagerComponent::DEBUG_PrintSystemInfo()
{
    UE_LOG(LogTemp, Warning, TEXT("=== SIGIL SYSTEM INFO ==="));
    UE_LOG(LogTemp, Warning, TEXT("System Active: %s"), bSystemActive ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Warning, TEXT("Max Slots: %d"), MaxSigilSlots);
    UE_LOG(LogTemp, Warning, TEXT("Auto Fusion: %s"), bAutoFusionEnabled ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Warning, TEXT("Total Equipped: %d"), SystemStats.TotalEquippedSigils);
    UE_LOG(LogTemp, Warning, TEXT("Total Fused: %d"), SystemStats.TotalFusedSigils);
    UE_LOG(LogTemp, Warning, TEXT("Total Power: %f"), SystemStats.TotalSigilPower);
    
    for (int32 i = 0; i < SigilSlots.Num(); i++)
    {
        const FSigilSlotData& SlotData = SigilSlots[i];
        UE_LOG(LogTemp, Warning, TEXT("Slot %d: %s | Unlocked: %s | Fused: %s | Multiplier: %f"),
            i,
            SlotData.EquippedSigil ? *SlotData.EquippedSigil->GetName() : TEXT("Empty"),
            SlotData.bIsUnlocked ? TEXT("Yes") : TEXT("No"),
            SlotData.bReadyForFusion ? TEXT("Yes") : TEXT("No"),
            SlotData.FusionMultiplier
        );
    }
}

void USigilManagerComponent::DEBUG_EquipRandomSigil(int32 SlotIndex)
{
    // Esta função seria implementada com lógica para criar sígilos aleatórios
    // Por enquanto, apenas log
    UE_LOG(LogTemp, Warning, TEXT("DEBUG: Random sigil equip requested for slot %d"), SlotIndex);
}

// ========================================
// FUNÇÕES INTERNAS PROTEGIDAS
// ========================================

void USigilManagerComponent::ApplySigilEffects(ASigilItem* Sigil, bool bApply)
{
    if (!Sigil || !CachedAbilitySystemComponent)
    {
        return;
    }
    
    // Aplicar ou remover GameplayEffects do sigilo
    const TArray<TSubclassOf<UGameplayEffect>>& SigilEffects = Sigil->SigilData.PassiveEffects;
    
    for (TSubclassOf<UGameplayEffect> EffectClass : SigilEffects)
    {
        if (EffectClass)
        {
            if (bApply)
            {
                FGameplayEffectContextHandle EffectContext = CachedAbilitySystemComponent->MakeEffectContext();
                FGameplayEffectSpecHandle EffectSpec = CachedAbilitySystemComponent->MakeOutgoingSpec(
                    EffectClass, 1.0f, EffectContext);
                
                if (EffectSpec.IsValid())
                {
                    CachedAbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data);
                }
            }
            // Remoção seria mais complexa, requerendo tracking de handles específicos
        }
    }
}

void USigilManagerComponent::UpdateSystemStatistics()
{
    SystemStats.TotalEquippedSigils = 0;
    SystemStats.TotalSigilPower = 0.0f;
    SystemStats.SigilsByRarity.Empty();
    SystemStats.SigilsByType.Empty();
    
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil)
        {
            SystemStats.TotalEquippedSigils++;
            SystemStats.TotalSigilPower += SlotData.EquippedSigil->CalculateSigilPower() * SlotData.FusionMultiplier;
            
            // Contar por raridade
            ESigilRarity Rarity = SlotData.EquippedSigil->GetSigilRarity();
            SystemStats.SigilsByRarity.FindOrAdd(Rarity)++;
            
            // Contar por tipo
            ESigilType Type = SlotData.EquippedSigil->GetSigilType();
            SystemStats.SigilsByType.FindOrAdd(Type)++;
        }
    }
    
    // Disparar evento de mudança de estatísticas
    OnSigilStatsChanged.Broadcast(SystemStats);
}

bool USigilManagerComponent::ValidateSystemConfiguration() const
{
    // Validar configuração básica
    if (MaxSigilSlots <= 0 || MaxSigilSlots > ABSOLUTE_MAX_SLOTS)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid MaxSigilSlots: %d"), MaxSigilSlots);
        return false;
    }
    
    if (FusionConfig.FusionTimeSeconds <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid FusionTimeSeconds: %f"), FusionConfig.FusionTimeSeconds);
        return false;
    }
    
    if (ReforgeCooldownSeconds < 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid ReforgeCooldownSeconds: %f"), ReforgeCooldownSeconds);
        return false;
    }
    
    return true;
}

UAbilitySystemComponent* USigilManagerComponent::GetOwnerAbilitySystemComponent() const
{
    if (CachedAbilitySystemComponent)
    {
        return CachedAbilitySystemComponent;
    }
    
    if (AActor* Owner = GetOwner())
    {
        return Owner->FindComponentByClass<UAbilitySystemComponent>();
    }
    
    return nullptr;
}

USigilAttributeSet* USigilManagerComponent::GetOwnerSigilAttributeSet() const
{
    if (CachedSigilAttributeSet)
    {
        return CachedSigilAttributeSet;
    }
    
    UAbilitySystemComponent* ASC = GetOwnerAbilitySystemComponent();
    if (ASC)
    {
        return const_cast<USigilAttributeSet*>(ASC->GetSet<USigilAttributeSet>());
    }
    
    return nullptr;
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO
// ========================================

void USigilManagerComponent::OnRep_SigilSlots()
{
    // Atualizar VFX e UI quando slots replicam
    for (int32 i = 0; i < SigilSlots.Num(); i++)
    {
        const FSigilSlotData& SlotData = SigilSlots[i];
        
        if (SlotData.bReadyForFusion && SlotVFXComponents.IsValidIndex(i) && SlotVFXComponents[i])
        {
            // Ativar VFX de fusão
            if (FusionConfig.FusionVFX)
            {
                SlotVFXComponents[i]->SetAsset(FusionConfig.FusionVFX);
                SlotVFXComponents[i]->Activate();
            }
        }
    }
}

void USigilManagerComponent::OnRep_SystemStats()
{
    // Disparar evento quando estatísticas replicam
    OnSigilStatsChanged.Broadcast(SystemStats);
}

// ========================================
// FUNÇÕES RPC
// ========================================

void USigilManagerComponent::ServerEquipSigil_Implementation(ASigilItem* Sigil, int32 SlotIndex)
{
    EquipSigil(Sigil, SlotIndex);
}

bool USigilManagerComponent::ServerEquipSigil_Validate(ASigilItem* Sigil, int32 SlotIndex)
{
    return CanEquipSigil(Sigil, SlotIndex);
}

void USigilManagerComponent::ServerUnequipSigil_Implementation(int32 SlotIndex)
{
    UnequipSigil(SlotIndex);
}

bool USigilManagerComponent::ServerUnequipSigil_Validate(int32 SlotIndex)
{
    return IsValidSlotIndex(SlotIndex) && IsSlotUnlocked(SlotIndex);
}

void USigilManagerComponent::ServerForceFusion_Implementation(int32 SlotIndex)
{
    ForceFuseSigil(SlotIndex);
}

bool USigilManagerComponent::ServerForceFusion_Validate(int32 SlotIndex)
{
    return IsValidSlotIndex(SlotIndex) && SigilSlots[SlotIndex].EquippedSigil != nullptr;
}

void USigilManagerComponent::ServerReforge_Implementation(int32 SlotIndex)
{
    ReforgeSigil(SlotIndex);
}

bool USigilManagerComponent::ServerReforge_Validate(int32 SlotIndex)
{
    return CanReforge() && IsValidSlotIndex(SlotIndex);
}

void USigilManagerComponent::MulticastNotifyFusion_Implementation(int32 SlotIndex, float Multiplier)
{
    // Reproduzir efeitos visuais e sonoros de fusão
    if (SlotVFXComponents.IsValidIndex(SlotIndex) && SlotVFXComponents[SlotIndex])
    {
        if (FusionConfig.FusionVFX)
        {
            SlotVFXComponents[SlotIndex]->SetAsset(FusionConfig.FusionVFX);
            SlotVFXComponents[SlotIndex]->Activate();
        }
    }
    
    if (FusionConfig.FusionSound)
    {
        // Reproduzir som de fusão
        if (AActor* Owner = GetOwner())
        {
            UGameplayStatics::PlaySoundAtLocation(GetWorld(), FusionConfig.FusionSound, Owner->GetActorLocation());
        }
    }
}

void USigilManagerComponent::MulticastPlayVFX_Implementation(int32 SlotIndex, FGameplayTag VFXTag)
{
    // Reproduzir VFX específico baseado na tag
    if (!SlotVFXComponents.IsValidIndex(SlotIndex) || !SlotVFXComponents[SlotIndex])
    {
        return;
    }
    
    UNiagaraSystem* VFXToPlay = nullptr;
    
    // Determinar qual VFX reproduzir baseado na tag
    if (VFXTag.MatchesTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.VFX.Equip"))))
    {
        // VFX de equipar (implementar)
    }
    else if (VFXTag.MatchesTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.VFX.Unequip"))))
    {
        // VFX de desequipar (implementar)
    }
    else if (VFXTag.MatchesTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.VFX.Fusion"))))
    {
        VFXToPlay = FusionConfig.FusionVFX;
    }
    
    if (VFXToPlay)
    {
        SlotVFXComponents[SlotIndex]->SetAsset(VFXToPlay);
        SlotVFXComponents[SlotIndex]->Activate();
    }
}

// ========================================
// HABILIDADES EXCLUSIVAS
// ========================================

void USigilManagerComponent::InitializeExclusiveAbilities()
{
    // Inicializar cooldowns das habilidades exclusivas
    ExclusiveAbilityCooldowns.Empty();
    
    // Configurar cooldowns padrão para cada subtipo
     ExclusiveAbilityCooldowns.Add(ESigilSubType::Aegis, 0.0f);
     ExclusiveAbilityCooldowns.Add(ESigilSubType::Ruin, 0.0f);
     ExclusiveAbilityCooldowns.Add(ESigilSubType::Vesper, 0.0f);
    
    UE_LOG(LogTemp, Log, TEXT("Exclusive abilities initialized"));
}

bool USigilManagerComponent::CanActivateExclusiveAbility(ESigilSubType SubType) const
 {
     if (!bSystemActive)
     {
         return false;
     }
     
     // Verificar se há sígilos do subtipo equipados
     if (!HasSigilOfSubType(SubType))
     {
         return false;
     }
     
     // Verificar cooldown
     if (IsExclusiveAbilityOnCooldown(SubType))
     {
         return false;
     }
     
     // Verificar se o AbilitySystemComponent está disponível
     if (!CachedAbilitySystemComponent)
     {
         return false;
     }
     
     return true;
 }

bool USigilManagerComponent::ActivateExclusiveAbility(ESigilSubType SubType)
 {
     if (!CanActivateExclusiveAbility(SubType))
     {
         return false;
     }
     
     // Executar no servidor
     if (!GetOwner()->HasAuthority())
     {
         ServerActivateExclusiveAbility(SubType);
         return true;
     }
     
     bool bSuccess = false;
     float CooldownDuration = 0.0f;
     
     switch (SubType)
     {
         case ESigilSubType::Aegis:
             bSuccess = ActivateMurallion();
             CooldownDuration = CalculateAbilityCooldown(SubType, 60.0f); // Base 60 segundos
             break;
             
         case ESigilSubType::Ruin:
             bSuccess = ActivateFracassoPrismal();
             CooldownDuration = CalculateAbilityCooldown(SubType, 45.0f); // Base 45 segundos
             break;
             
         case ESigilSubType::Vesper:
             bSuccess = ActivateSoproDeFluxo();
             CooldownDuration = CalculateAbilityCooldown(SubType, 30.0f); // Base 30 segundos
             break;
             
         default:
             UE_LOG(LogTemp, Warning, TEXT("Unknown sigil subtype for exclusive ability: %d"), (int32)SubType);
             return false;
     }
     
     if (bSuccess)
     {
         // Aplicar cooldown
         float CurrentTime = GetWorld()->GetTimeSeconds();
         ExclusiveAbilityCooldowns[SubType] = CurrentTime + CooldownDuration;
         
         // Efeitos visuais
         FVector VFXLocation = GetOwner()->GetActorLocation();
         MulticastPlayExclusiveAbilityVFX(SubType, VFXLocation);
         
         // Disparar evento de ativação
         OnExclusiveAbilityActivated.Broadcast(SubType, CooldownDuration);
         
         UE_LOG(LogTemp, Log, TEXT("Exclusive ability activated: %s"), *UEnum::GetValueAsString(SubType));
     }
     
     return bSuccess;
 }

bool USigilManagerComponent::ActivateMurallion()
 {
     if (!CachedAbilitySystemComponent)
     {
         return false;
     }
     
     // Murallion (Aegis): Cria uma barreira esférica que protege aliados
     FVector SpawnLocation = GetOwner()->GetActorLocation();
     float BarrierRadius = CalculateBarrierRadius();
     float BarrierDuration = CalculateBarrierDuration();
     float ProtectionAmount = CalculateProtectionAmount();
     
     // Criar GameplayEffect para proteção da barreira
     if (UClass* BarrierEffectClass = LoadClass<UGameplayEffect>(nullptr, TEXT("/Game/Sigils/Effects/GE_Murallion_BarrierProtection.GE_Murallion_BarrierProtection_C")))
     {
         FGameplayEffectContextHandle EffectContext = CachedAbilitySystemComponent->MakeEffectContext();
         EffectContext.AddSourceObject(GetOwner());
         
         FGameplayEffectSpecHandle SpecHandle = CachedAbilitySystemComponent->MakeOutgoingSpec(BarrierEffectClass, 1.0f, EffectContext);
         if (SpecHandle.IsValid())
         {
             // Configurar magnitude da proteção
             SpecHandle.Data->SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(FName("Data.Protection.Amount")), ProtectionAmount);
             SpecHandle.Data->SetDuration(BarrierDuration, false);
             
             // Aplicar efeito
             CachedAbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
         }
     }
     
     // Spawnar VFX da barreira
     SpawnBarrierVFX(SpawnLocation, BarrierRadius, BarrierDuration);
     
     // Aplicar proteção a aliados na área
     ApplyBarrierProtectionToAllies(SpawnLocation, BarrierRadius, ProtectionAmount, BarrierDuration);
     
     UE_LOG(LogTemp, Log, TEXT("Murallion activated: Barrier at %s, Radius: %f, Protection: %f"), *SpawnLocation.ToString(), BarrierRadius, ProtectionAmount);
     
     return true;
 }

bool USigilManagerComponent::ActivateFracassoPrismal()
 {
     if (!CachedAbilitySystemComponent)
     {
         return false;
     }
     
     // Fracasso Prismal (Ruin): Reseta cooldowns e aplica buff de dano
     float CooldownReduction = CalculateCooldownReduction();
     float DamageBonus = CalculateDamageBonus();
     float BuffDuration = CalculateBuffDuration();
     
     // Resetar cooldowns de outras habilidades
     ResetAbilityCooldowns(CooldownReduction);
     
     // Aplicar buff de dano temporário
     if (UClass* DamageBuffClass = LoadClass<UGameplayEffect>(nullptr, TEXT("/Game/Sigils/Effects/GE_FracassoPrismal_DamageBuff.GE_FracassoPrismal_DamageBuff_C")))
     {
         FGameplayEffectContextHandle EffectContext = CachedAbilitySystemComponent->MakeEffectContext();
         EffectContext.AddSourceObject(GetOwner());
         
         FGameplayEffectSpecHandle SpecHandle = CachedAbilitySystemComponent->MakeOutgoingSpec(DamageBuffClass, 1.0f, EffectContext);
         if (SpecHandle.IsValid())
         {
             // Configurar magnitude do buff
             SpecHandle.Data->SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(FName("Data.Damage.Multiplier")), DamageBonus);
             SpecHandle.Data->SetDuration(BuffDuration, false);
             
             // Aplicar efeito
             CachedAbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
         }
     }
     
     // Spawnar VFX de energia prismal
     SpawnPrismalEnergyVFX();
     
     UE_LOG(LogTemp, Log, TEXT("Fracasso Prismal activated: Cooldown reduction: %f%%, Damage bonus: %f%%"), CooldownReduction * 100.0f, DamageBonus * 100.0f);
     
     return true;
 }

bool USigilManagerComponent::ActivateSoproDeFluxo()
 {
     if (!CachedAbilitySystemComponent)
     {
         return false;
     }
     
     // Sopro de Fluxo (Vesper): Dash para aliado mais próximo e aplica escudo
     AActor* ClosestAlly = FindClosestAlly();
     if (!ClosestAlly)
     {
         UE_LOG(LogTemp, Warning, TEXT("Sopro de Fluxo: No allies found in range"));
         return false;
     }
     
     float DashSpeed = CalculateDashSpeed();
     float ShieldAmount = CalculateShieldAmount();
     float ShieldDuration = CalculateShieldDuration();
     
     // Executar dash para o aliado
     ExecuteDashToTarget(ClosestAlly, DashSpeed);
     
     // Aplicar escudo ao aliado ao final do dash
     if (UAbilitySystemComponent* AllyASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(ClosestAlly))
     {
         if (UClass* ShieldEffectClass = LoadClass<UGameplayEffect>(nullptr, TEXT("/Game/Sigils/Effects/GE_SoproDeFluxo_Shield.GE_SoproDeFluxo_Shield_C")))
         {
             FGameplayEffectContextHandle EffectContext = CachedAbilitySystemComponent->MakeEffectContext();
             EffectContext.AddSourceObject(GetOwner());
             
             FGameplayEffectSpecHandle SpecHandle = CachedAbilitySystemComponent->MakeOutgoingSpec(ShieldEffectClass, 1.0f, EffectContext);
             if (SpecHandle.IsValid())
             {
                 // Configurar magnitude do escudo
                 SpecHandle.Data->SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(FName("Data.Shield.Amount")), ShieldAmount);
                 SpecHandle.Data->SetDuration(ShieldDuration, false);
                 
                 // Aplicar efeito ao aliado
                 AllyASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
             }
         }
     }
     
     // Spawnar VFX de dash e escudo
     SpawnDashVFX(ClosestAlly->GetActorLocation());
     SpawnShieldVFX(ClosestAlly);
     
     UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo activated: Dashed to %s, Shield: %f"), *ClosestAlly->GetName(), ShieldAmount);
     
     return true;
 }

bool USigilManagerComponent::HasSigilOfSubType(ESigilSubType SubType) const
 {
     for (const FSigilSlotData& SlotData : SigilSlots)
     {
         if (SlotData.EquippedSigil && SlotData.EquippedSigil->GetSigilData().SigilSubType == SubType)
         {
             return true;
         }
     }
     return false;
 }
 
 int32 USigilManagerComponent::CountSigilsOfSubType(ESigilSubType SubType) const
 {
     int32 Count = 0;
     for (const FSigilSlotData& SlotData : SigilSlots)
     {
         if (SlotData.EquippedSigil && SlotData.EquippedSigil->GetSigilData().SigilSubType == SubType)
         {
             Count++;
         }
     }
     return Count;
 }
 
 bool USigilManagerComponent::IsExclusiveAbilityOnCooldown(ESigilSubType SubType) const
 {
     if (!ExclusiveAbilityCooldowns.Contains(SubType))
     {
         return false;
     }
     
     float CurrentTime = GetWorld()->GetTimeSeconds();
     float CooldownEndTime = ExclusiveAbilityCooldowns[SubType];
     
     return CurrentTime < CooldownEndTime;
 }
 
 float USigilManagerComponent::GetExclusiveAbilityCooldownRemaining(ESigilSubType SubType) const
 {
     if (!IsExclusiveAbilityOnCooldown(SubType))
     {
         return 0.0f;
     }
     
     float CurrentTime = GetWorld()->GetTimeSeconds();
     float CooldownEndTime = ExclusiveAbilityCooldowns[SubType];
     
     return FMath::Max(0.0f, CooldownEndTime - CurrentTime);
 }

TArray<ESigilSubType> USigilManagerComponent::GetAvailableExclusiveAbilities() const
 {
     TArray<ESigilSubType> AvailableAbilities;
     
     // Verificar todos os subtipos
     TArray<ESigilSubType> AllSubTypes = {
         ESigilSubType::Aegis,
         ESigilSubType::Ruin,
         ESigilSubType::Vesper
     };
     
     for (ESigilSubType SubType : AllSubTypes)
     {
         if (CanActivateExclusiveAbility(SubType))
         {
             AvailableAbilities.Add(SubType);
         }
     }
     
     return AvailableAbilities;
 }

void USigilManagerComponent::ServerActivateExclusiveAbility_Implementation(ESigilSubType SubType)
{
    ActivateExclusiveAbility(SubType);
}

bool USigilManagerComponent::ServerActivateExclusiveAbility_Validate(ESigilSubType SubType)
{
    return CanActivateExclusiveAbility(SubType);
}

void USigilManagerComponent::MulticastPlayExclusiveAbilityVFX_Implementation(ESigilSubType SubType, FVector Location)
{
    // Reproduzir VFX específico para cada habilidade exclusiva
    if (AActor* Owner = GetOwner())
    {
        // Se Location não foi fornecida, usar a localização do owner
        if (Location.IsZero())
        {
            Location = Owner->GetActorLocation();
        }
        
        switch (SubType)
        {
            case ESigilSubType::Aegis:
                // VFX de Murallion (barreira protetiva)
                if (USigilVFXManager* VFXManager = GetOwner()->FindComponentByClass<USigilVFXManager>())
                {
                    // Usar PlayVFXEffect com tipo SpectralAura para barreira
                    FSigilVFXConfig Config = VFXManager->GetDefaultVFXConfig(ESigilVFXType::SpectralAura);
                    VFXManager->PlayVFXEffect(ESigilVFXType::SpectralAura, GetOwner(), Config);
                }
                UE_LOG(LogTemp, Log, TEXT("Playing Murallion Barrier VFX at %s"), *Location.ToString());
                break;
                
            case ESigilSubType::Ruin:
                // VFX de Fracasso Prismal (explosão de energia)
                if (USigilVFXManager* VFXManager = GetOwner()->FindComponentByClass<USigilVFXManager>())
                {
                    // Usar PlayVFXEffect com tipo Critical para explosão
                    FSigilVFXConfig Config = VFXManager->GetDefaultVFXConfig(ESigilVFXType::Critical);
                    VFXManager->PlayVFXEffect(ESigilVFXType::Critical, GetOwner(), Config);
                }
                UE_LOG(LogTemp, Log, TEXT("Playing Fracasso Prismal VFX at %s"), *Location.ToString());
                break;
                
            case ESigilSubType::Vesper:
                // VFX de Sopro de Fluxo (dash e escudo)
                if (USigilVFXManager* VFXManager = GetOwner()->FindComponentByClass<USigilVFXManager>())
                {
                    // Usar PlayVFXEffect com tipo SpectralPower para dash
                    FSigilVFXConfig Config = VFXManager->GetDefaultVFXConfig(ESigilVFXType::SpectralPower);
                    VFXManager->PlayVFXEffect(ESigilVFXType::SpectralPower, GetOwner(), Config);
                }
                UE_LOG(LogTemp, Log, TEXT("Playing Sopro de Fluxo VFX at %s"), *Location.ToString());
                break;
        }
    }
}

// ========================================
// FUNÇÕES DE CÁLCULO PARA HABILIDADES EXCLUSIVAS
// ========================================

float USigilManagerComponent::CalculateAbilityCooldown(ESigilSubType SubType, float BaseCooldown) const
{
    float CooldownReduction = 0.0f;
    int32 SigilCount = CountSigilsOfSubType(SubType);
    
    // Redução de cooldown baseada na quantidade de sígilos do mesmo subtipo
    CooldownReduction = SigilCount * 0.1f; // 10% por sigilo adicional
    
    // Aplicar redução máxima de 50%
    CooldownReduction = FMath::Clamp(CooldownReduction, 0.0f, 0.5f);
    
    return BaseCooldown * (1.0f - CooldownReduction);
}

float USigilManagerComponent::CalculateBarrierRadius() const
{
    float BaseRadius = 500.0f;
    int32 AegisSigils = CountSigilsOfSubType(ESigilSubType::Aegis);
    
    // Aumentar raio baseado na quantidade de sígilos Aegis
    return BaseRadius + (AegisSigils * 100.0f);
}

float USigilManagerComponent::CalculateBarrierDuration() const
{
    float BaseDuration = 10.0f;
    int32 AegisSigils = CountSigilsOfSubType(ESigilSubType::Aegis);
    
    // Aumentar duração baseado na quantidade de sígilos Aegis
    return BaseDuration + (AegisSigils * 2.0f);
}

float USigilManagerComponent::CalculateProtectionAmount() const
{
    float BaseProtection = 100.0f;
    int32 AegisSigils = CountSigilsOfSubType(ESigilSubType::Aegis);
    
    // Calcular proteção baseada nos sígilos equipados
    float TotalPower = 0.0f;
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil && SlotData.EquippedSigil->GetSigilData().SigilSubType == ESigilSubType::Aegis)
        {
            TotalPower += SlotData.EquippedSigil->GetTotalSpectralPower() * SlotData.FusionMultiplier;
        }
    }
    
    return BaseProtection + (TotalPower * 0.5f);
}

float USigilManagerComponent::CalculateCooldownReduction() const
{
    float BaseReduction = 0.3f; // 30% base
    int32 RuinSigils = CountSigilsOfSubType(ESigilSubType::Ruin);
    
    // Aumentar redução baseado na quantidade de sígilos Ruin
    return FMath::Clamp(BaseReduction + (RuinSigils * 0.1f), 0.0f, 0.8f); // Máximo 80%
}

float USigilManagerComponent::CalculateDamageBonus() const
{
    float BaseBonus = 0.5f; // 50% base
    int32 RuinSigils = CountSigilsOfSubType(ESigilSubType::Ruin);
    
    // Calcular bonus baseado no poder total dos sígilos Ruin
    float TotalPower = 0.0f;
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil && SlotData.EquippedSigil->GetSigilData().SigilSubType == ESigilSubType::Ruin)
        {
            TotalPower += SlotData.EquippedSigil->GetTotalSpectralPower() * SlotData.FusionMultiplier;
        }
    }
    
    return BaseBonus + (TotalPower * 0.01f);
}

float USigilManagerComponent::CalculateBuffDuration() const
{
    float BaseDuration = 15.0f;
    int32 RuinSigils = CountSigilsOfSubType(ESigilSubType::Ruin);
    
    // Aumentar duração baseado na quantidade de sígilos Ruin
    return BaseDuration + (RuinSigils * 3.0f);
}

float USigilManagerComponent::CalculateDashSpeed() const
{
    float BaseSpeed = 2000.0f;
    int32 VesperSigils = CountSigilsOfSubType(ESigilSubType::Vesper);
    
    // Aumentar velocidade baseado na quantidade de sígilos Vesper
    return BaseSpeed + (VesperSigils * 300.0f);
}

float USigilManagerComponent::CalculateShieldAmount() const
{
    float BaseShield = 200.0f;
    
    // Calcular escudo baseado no poder total dos sígilos Vesper
    float TotalPower = 0.0f;
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil && SlotData.EquippedSigil->GetSigilData().SigilSubType == ESigilSubType::Vesper)
        {
            TotalPower += SlotData.EquippedSigil->GetTotalSpectralPower() * SlotData.FusionMultiplier;
        }
    }
    
    return BaseShield + (TotalPower * 0.8f);
}

float USigilManagerComponent::CalculateShieldDuration() const
{
    float BaseDuration = 8.0f;
    int32 VesperSigils = CountSigilsOfSubType(ESigilSubType::Vesper);
    
    // Aumentar duração baseado na quantidade de sígilos Vesper
    return BaseDuration + (VesperSigils * 2.0f);
}

// ========================================
// FUNÇÕES AUXILIARES PARA HABILIDADES EXCLUSIVAS
// ========================================

void USigilManagerComponent::SpawnBarrierVFX(const FVector& Location, float Radius, float Duration)
{
    // Implementar spawn de VFX da barreira
    UE_LOG(LogTemp, Log, TEXT("Spawning barrier VFX at %s with radius %f for %f seconds"), 
           *Location.ToString(), Radius, Duration);
    
    // Em implementação completa, spawnar Niagara System ou Static Mesh
}

void USigilManagerComponent::ApplyBarrierProtectionToAllies(const FVector& Location, float Radius, float Protection, float Duration)
{
    // Encontrar aliados na área e aplicar proteção
    TArray<AActor*> FoundActors;
    UKismetSystemLibrary::SphereOverlapActors(
        GetWorld(),
        Location,
        Radius,
        TArray<TEnumAsByte<EObjectTypeQuery>>(),
        nullptr,
        TArray<AActor*>{GetOwner()},
        FoundActors
    );
    
    for (AActor* Actor : FoundActors)
    {
        if (UAbilitySystemComponent* ASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Actor))
        {
            // Aplicar efeito de proteção
            UE_LOG(LogTemp, Log, TEXT("Applying barrier protection to %s"), *Actor->GetName());
        }
    }
}

void USigilManagerComponent::ResetAbilityCooldowns(float ReductionPercentage)
{
    // Reduzir cooldowns de outras habilidades
    for (auto& CooldownPair : ExclusiveAbilityCooldowns)
    {
        if (CooldownPair.Key != ESigilSubType::Ruin) // Não afetar o próprio cooldown
        {
            float CurrentTime = GetWorld()->GetTimeSeconds();
            float RemainingCooldown = FMath::Max(0.0f, CooldownPair.Value - CurrentTime);
            float ReducedCooldown = RemainingCooldown * (1.0f - ReductionPercentage);
            
            CooldownPair.Value = CurrentTime + ReducedCooldown;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("Ability cooldowns reduced by %f%%"), ReductionPercentage * 100.0f);
}

void USigilManagerComponent::SpawnPrismalEnergyVFX()
{
    // Implementar VFX de energia prismal
    if (AActor* Owner = GetOwner())
    {
        FVector Location = Owner->GetActorLocation();
        UE_LOG(LogTemp, Log, TEXT("Spawning prismal energy VFX at %s"), *Location.ToString());
        
        // Em implementação completa, spawnar Niagara System
    }
}

AActor* USigilManagerComponent::FindClosestAlly() const
{
    AActor* ClosestAlly = nullptr;
    float ClosestDistance = 1500.0f; // Alcance máximo
    
    if (AActor* Owner = GetOwner())
    {
        FVector OwnerLocation = Owner->GetActorLocation();
        
        // Encontrar aliados próximos
        TArray<AActor*> FoundActors;
        UKismetSystemLibrary::SphereOverlapActors(
            GetWorld(),
            OwnerLocation,
            ClosestDistance,
            TArray<TEnumAsByte<EObjectTypeQuery>>(),
            nullptr,
            TArray<AActor*>{Owner},
            FoundActors
        );
        
        for (AActor* Actor : FoundActors)
        {
            // Verificar se é aliado (implementar lógica de team)
            float Distance = FVector::Dist(OwnerLocation, Actor->GetActorLocation());
            if (Distance < ClosestDistance)
            {
                ClosestAlly = Actor;
                ClosestDistance = Distance;
            }
        }
    }
    
    return ClosestAlly;
}

void USigilManagerComponent::ExecuteDashToTarget(AActor* Target, float Speed)
{
    if (!Target || !GetOwner())
    {
        return;
    }
    
    FVector StartLocation = GetOwner()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();
    
    UE_LOG(LogTemp, Log, TEXT("Executing dash from %s to %s at speed %f"), 
           *StartLocation.ToString(), *TargetLocation.ToString(), Speed);
    
    // Em implementação completa, usar Timeline ou movimento interpolado
    // Por enquanto, teleporte instantâneo
    GetOwner()->SetActorLocation(TargetLocation);
}

void USigilManagerComponent::SpawnDashVFX(const FVector& TargetLocation)
{
    // Implementar VFX de dash
    UE_LOG(LogTemp, Log, TEXT("Spawning dash VFX to %s"), *TargetLocation.ToString());
    
    // Em implementação completa, spawnar trail VFX
}

void USigilManagerComponent::SpawnShieldVFX(AActor* Target)
{
    if (!Target)
    {
        return;
    }
    
    // Implementar VFX de escudo no alvo
    UE_LOG(LogTemp, Log, TEXT("Spawning shield VFX on %s"), *Target->GetName());
    
    // Em implementação completa, spawnar Niagara System no alvo
}

// ========================================
// IMPLEMENTAÇÕES DOS MÉTODOS ADICIONAIS
// ========================================

bool USigilManagerComponent::HasAvailableSlots() const
{
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.bIsUnlocked && !SlotData.EquippedSigil)
        {
            return true;
        }
    }
    return false;
}

bool USigilManagerComponent::IsSlotAvailable(int32 SlotIndex) const
{
    if (!IsValidSlotIndex(SlotIndex))
    {
        return false;
    }

    const FSigilSlotData& SlotData = SigilSlots[SlotIndex];
    return SlotData.bIsUnlocked && !SlotData.EquippedSigil;
}

bool USigilManagerComponent::TriggerFusionForSigil(ASigilItem* Sigil)
{
    if (!Sigil)
    {
        return false;
    }

    // Encontrar o slot do sigilo
    int32 SigilSlotIndex = -1;
    for (int32 i = 0; i < SigilSlots.Num(); i++)
    {
        if (SigilSlots[i].EquippedSigil == Sigil)
        {
            SigilSlotIndex = i;
            break;
        }
    }

    if (SigilSlotIndex == -1)
    {
        return false;
    }

    // Verificar se há sígilos compatíveis
    if (!HasCompatibleSigilsForFusion(Sigil))
    {
        return false;
    }

    // Forçar fusão
    return ForceFuseSigil(SigilSlotIndex);
}

bool USigilManagerComponent::HasCompatibleSigilsForFusion(ASigilItem* Sigil) const
{
    if (!Sigil)
    {
        return false;
    }

    ESigilType SigilType = Sigil->GetSigilType();
    ESigilRarity SigilRarity = Sigil->GetSigilRarity();

    // Contar sígilos compatíveis (mesmo tipo ou raridade)
    int32 CompatibleCount = 0;
    for (const FSigilSlotData& SlotData : SigilSlots)
    {
        if (SlotData.EquippedSigil && SlotData.EquippedSigil != Sigil)
        {
            if (SlotData.EquippedSigil->GetSigilType() == SigilType ||
                SlotData.EquippedSigil->GetSigilRarity() == SigilRarity)
            {
                CompatibleCount++;
            }
        }
    }

    // Precisa de pelo menos 1 sigilo compatível para fusão
    return CompatibleCount >= 1;
}

bool USigilManagerComponent::CanAffordReforge(ASigilItem* Sigil) const
{
    if (!Sigil)
    {
        return false;
    }

    // Verificar cooldown global
    if (!CanReforge())
    {
        return false;
    }

    // Verificar recursos específicos baseados na raridade
    ESigilRarity Rarity = Sigil->GetSigilRarity();

    // Por enquanto, sempre permitir (implementar sistema de recursos depois)
    // Em um sistema completo, verificaria moedas, materiais, etc.
    switch (Rarity)
    {
        case ESigilRarity::Common:
            return true; // Custo baixo
        case ESigilRarity::Rare:
            return true; // Custo médio
        case ESigilRarity::Epic:
            return true; // Custo alto
        case ESigilRarity::Legendary:
            return true; // Custo muito alto
        default:
            return true;
    }
}

void USigilManagerComponent::ConsumeReforgeResources(ASigilItem* Sigil)
{
    if (!Sigil)
    {
        return;
    }

    ESigilRarity Rarity = Sigil->GetSigilRarity();

    // Implementar consumo de recursos baseado na raridade
    // Por enquanto, apenas log
    UE_LOG(LogTemp, Log, TEXT("Consuming reforge resources for %s sigil"),
           *UEnum::GetValueAsString(Rarity));

    // Em um sistema completo, consumiria:
    // - Moedas baseadas na raridade
    // - Materiais específicos
    // - Possivelmente outros sígilos como combustível

    // Atualizar estatísticas
    SystemStats.TotalReforges++;
    UpdateSystemStatistics();
}

void USigilManagerComponent::OnSigilEquipped(ASigilItem* Sigil, int32 SlotIndex)
{
    if (!Sigil || !IsValidSlotIndex(SlotIndex))
    {
        return;
    }

    // Callback quando um sigilo é equipado
    UE_LOG(LogTemp, Log, TEXT("Sigil equipped callback: %s in slot %d"),
           *Sigil->GetName(), SlotIndex);

    // Atualizar estatísticas
    UpdateSystemStatistics();

    // Disparar eventos
    FGameplayTag EquipTag = UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Event.Equipped"));
    OnSigilEvent.Broadcast(Sigil, SlotIndex, EquipTag);
}

void USigilManagerComponent::OnSigilUnequipped(ASigilItem* Sigil, int32 SlotIndex)
{
    if (!Sigil || !IsValidSlotIndex(SlotIndex))
    {
        return;
    }

    // Callback quando um sigilo é desequipado
    UE_LOG(LogTemp, Log, TEXT("Sigil unequipped callback: %s from slot %d"),
           *Sigil->GetName(), SlotIndex);

    // Atualizar estatísticas
    UpdateSystemStatistics();

    // Disparar eventos
    FGameplayTag UnequipTag = UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Event.Unequipped"));
    OnSigilEvent.Broadcast(Sigil, SlotIndex, UnequipTag);
}

void USigilManagerComponent::SetExclusiveAbilityCooldown(ESigilSubType SubType, float CooldownTime)
{
    if (CooldownTime < 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid cooldown time: %f. Must be >= 0"), CooldownTime);
        return;
    }
    
    // Definir o cooldown
    ExclusiveAbilityCooldowns.Add(SubType, CooldownTime);
    
    // Broadcast do evento de mudança de cooldown
    OnExclusiveAbilityCooldownChanged.Broadcast(SubType);
    
    UE_LOG(LogTemp, Log, TEXT("Set exclusive ability cooldown for %s: %f seconds"),
           *UEnum::GetValueAsString(SubType), CooldownTime);
}

float USigilManagerComponent::GetExclusiveAbilityCooldown(ESigilSubType SubType) const
{
    if (const float* CooldownPtr = ExclusiveAbilityCooldowns.Find(SubType))
    {
        return *CooldownPtr;
    }

    // Retornar cooldown padrão baseado no tipo
    switch (SubType)
    {
        case ESigilSubType::Aegis: return 45.0f;
        case ESigilSubType::Ruin: return 60.0f;
        case ESigilSubType::Vesper: return 30.0f;
        default: return 0.0f;
    }
}

void USigilManagerComponent::DEBUG_ForceActivateExclusiveAbility(ESigilSubType SubType)
{
    UE_LOG(LogTemp, Warning, TEXT("DEBUG: Force activating exclusive ability: %s"), *UEnum::GetValueAsString(SubType));

    // Remover cooldown temporariamente
    ExclusiveAbilityCooldowns.Remove(SubType);

    // Ativar habilidade
    ActivateExclusiveAbility(SubType);

    UE_LOG(LogTemp, Warning, TEXT("DEBUG: Exclusive ability %s force activated"), *UEnum::GetValueAsString(SubType));
}