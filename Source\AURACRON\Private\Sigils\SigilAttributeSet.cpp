// SigilAttributeSet.cpp
// AURACRON - Sistema de Sígilos
// Implementação do conjunto de atributos espectrais para GameplayAbilities System UE 5.6
// APIs verificadas: AttributeSet.h, AbilitySystemComponent.h, GameplayEffectExtension.h

#include "Sigils/SigilAttributeSet.h"
#include "GameplayAbilities/Public/GameplayEffect.h"
#include "GameplayAbilities/Public/GameplayEffectExtension.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"

USigilAttributeSet::USigilAttributeSet()
{
    // Inicializar atributos primários espectrais com valores base
    SpectralPower = 10.0f;
    SpectralResilience = 10.0f;
    SpectralVelocity = 10.0f;
    SpectralFocus = 10.0f;

    // Inicializar atributos derivados (serão recalculados)
    AttackPower = 25.0f;  // 10 + (10 * 1.5)
    DefensePower = 22.0f; // 10 + (10 * 1.2)
    AttackSpeed = 1.1f;   // 1.0 + (10 * 0.01)
    CriticalChance = 0.02f; // (10 + 10) * 0.001
    CriticalMultiplier = 150.2f; // 150 + (10 * 0.002)

    // Inicializar atributos de mobilidade
    MovementSpeed = 320.0f; // 300 + (10 * 2.0)
    CooldownReduction = 0.1f; // 10 * 0.01

    // Inicializar atributos de recursos
    ManaRegeneration = 5.0f; // 10 * 0.5
    HealthRegeneration = 3.0f; // 10 * 0.3

    // Inicializar atributos de estado
    FusionMultiplier = 1.0f;
    SigilSlots = 3.0f; // Começa com 3 slots
    SigilEfficiency = 1.0f;
    SigilExperience = 0.0f;

    // Inicializar atributos MOBA
    TeamFightBonus = 0.0f;
    ObjectiveBonus = 0.0f;
    CCResistance = 0.0f;

    // Marcar cache como sujo
    bTotalPowerCacheDirty = true;
}

void USigilAttributeSet::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar atributos primários espectrais
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SpectralPower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SpectralResilience, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SpectralVelocity, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SpectralFocus, COND_None, REPNOTIFY_Always);

    // Replicar atributos derivados de combate
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, AttackPower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, DefensePower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, AttackSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, CriticalChance, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, CriticalMultiplier, COND_None, REPNOTIFY_Always);

    // Replicar atributos de mobilidade
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, MovementSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, CooldownReduction, COND_None, REPNOTIFY_Always);

    // Replicar atributos de recursos
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, ManaRegeneration, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, HealthRegeneration, COND_None, REPNOTIFY_Always);

    // Replicar atributos de estado
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, FusionMultiplier, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SigilSlots, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SigilEfficiency, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SigilExperience, COND_None, REPNOTIFY_Always);

    // Replicar atributos MOBA
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, TeamFightBonus, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, ObjectiveBonus, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, CCResistance, COND_None, REPNOTIFY_Always);
}

void USigilAttributeSet::PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue)
{
    Super::PreAttributeChange(Attribute, NewValue);

    // Aplicar limites aos atributos antes da mudança
    ClampAttributeValue(Attribute, NewValue);

    // Marcar cache como sujo se atributo primário mudou
    if (Attribute == GetSpectralPowerAttribute() ||
        Attribute == GetSpectralResilienceAttribute() ||
        Attribute == GetSpectralVelocityAttribute() ||
        Attribute == GetSpectralFocusAttribute())
    {
        bTotalPowerCacheDirty = true;
    }
}

void USigilAttributeSet::PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data)
{
    Super::PostGameplayEffectExecute(Data);

    FGameplayEffectContextHandle Context = Data.EffectSpec.GetContext();
    UAbilitySystemComponent* Source = Context.GetOriginalInstigatorAbilitySystemComponent();
    const FGameplayTagContainer& SourceTags = *Data.EffectSpec.CapturedSourceTags.GetAggregatedTags();
    const FGameplayTagContainer& TargetTags = *Data.EffectSpec.CapturedTargetTags.GetAggregatedTags();

    // Obter magnitude da mudança
    float DeltaValue = 0;
    if (Data.EvaluatedData.ModifierOp == EGameplayModOp::Type::Additive)
    {
        DeltaValue = Data.EvaluatedData.Magnitude;
    }

    // Processar mudanças em atributos específicos
    FGameplayAttribute Attribute = Data.EvaluatedData.Attribute;

    // Se atributo primário mudou, recalcular derivados
    if (Attribute == GetSpectralPowerAttribute() ||
        Attribute == GetSpectralResilienceAttribute() ||
        Attribute == GetSpectralVelocityAttribute() ||
        Attribute == GetSpectralFocusAttribute())
    {
        RecalculateDerivedAttributes();
    }

    // Aplicar multiplicador de fusão se ativo
    if (GetFusionMultiplier() > 1.0f)
    {
        // Aplicar multiplicador apenas a atributos de combate
        if (Attribute == GetAttackPowerAttribute() ||
            Attribute == GetDefensePowerAttribute() ||
            Attribute == GetAttackSpeedAttribute())
        {
            float CurrentValue = 0.0f;
            if (Attribute == GetAttackPowerAttribute()) CurrentValue = AttackPower.GetCurrentValue();
            else if (Attribute == GetDefensePowerAttribute()) CurrentValue = DefensePower.GetCurrentValue();
            else if (Attribute == GetAttackSpeedAttribute()) CurrentValue = AttackSpeed.GetCurrentValue();
            
            float NewValue = CurrentValue * GetFusionMultiplier();
            
            if (Attribute == GetAttackPowerAttribute()) AttackPower.SetBaseValue(NewValue);
            else if (Attribute == GetDefensePowerAttribute()) DefensePower.SetBaseValue(NewValue);
            else if (Attribute == GetAttackSpeedAttribute()) AttackSpeed.SetBaseValue(NewValue);
        }
    }

    // Notificar mudança para sistemas dependentes
    float NewValue = 0.0f;
    if (Attribute == GetSpectralPowerAttribute()) NewValue = SpectralPower.GetCurrentValue();
    else if (Attribute == GetSpectralResilienceAttribute()) NewValue = SpectralResilience.GetCurrentValue();
    else if (Attribute == GetSpectralVelocityAttribute()) NewValue = SpectralVelocity.GetCurrentValue();
    else if (Attribute == GetSpectralFocusAttribute()) NewValue = SpectralFocus.GetCurrentValue();
    else if (Attribute == GetAttackPowerAttribute()) NewValue = AttackPower.GetCurrentValue();
    else if (Attribute == GetDefensePowerAttribute()) NewValue = DefensePower.GetCurrentValue();
    else if (Attribute == GetAttackSpeedAttribute()) NewValue = AttackSpeed.GetCurrentValue();
    else if (Attribute == GetCriticalChanceAttribute()) NewValue = CriticalChance.GetCurrentValue();
    else if (Attribute == GetCriticalMultiplierAttribute()) NewValue = CriticalMultiplier.GetCurrentValue();
    else if (Attribute == GetMovementSpeedAttribute()) NewValue = MovementSpeed.GetCurrentValue();
    else if (Attribute == GetCooldownReductionAttribute()) NewValue = CooldownReduction.GetCurrentValue();
    else if (Attribute == GetManaRegenerationAttribute()) NewValue = ManaRegeneration.GetCurrentValue();
    else if (Attribute == GetHealthRegenerationAttribute()) NewValue = HealthRegeneration.GetCurrentValue();
    else if (Attribute == GetFusionMultiplierAttribute()) NewValue = FusionMultiplier.GetCurrentValue();
    else if (Attribute == GetSigilSlotsAttribute()) NewValue = SigilSlots.GetCurrentValue();
    else if (Attribute == GetSigilEfficiencyAttribute()) NewValue = SigilEfficiency.GetCurrentValue();
    else if (Attribute == GetSigilExperienceAttribute()) NewValue = SigilExperience.GetCurrentValue();
    else if (Attribute == GetTeamFightBonusAttribute()) NewValue = TeamFightBonus.GetCurrentValue();
    else if (Attribute == GetObjectiveBonusAttribute()) NewValue = ObjectiveBonus.GetCurrentValue();
    else if (Attribute == GetCCResistanceAttribute()) NewValue = CCResistance.GetCurrentValue();
    
    float OldValue = NewValue - Data.EvaluatedData.Magnitude;
    
    NotifyAttributeChange(Attribute, OldValue, NewValue);
}

void USigilAttributeSet::PreAttributeBaseChange(const FGameplayAttribute& Attribute, float& NewValue) const
{
    Super::PreAttributeBaseChange(Attribute, NewValue);

    // Aplicar limites finais antes de definir valor base
    ClampAttributeValue(Attribute, NewValue);
}

void USigilAttributeSet::PostAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue)
{
    Super::PostAttributeChange(Attribute, OldValue, NewValue);

    // Recalcular atributos derivados se necessário
    if (Attribute == GetSpectralPowerAttribute() ||
        Attribute == GetSpectralResilienceAttribute() ||
        Attribute == GetSpectralVelocityAttribute() ||
        Attribute == GetSpectralFocusAttribute())
    {
        RecalculateDerivedAttributes();
        bTotalPowerCacheDirty = true;
    }

    // Notificar mudança
    NotifyAttributeChange(Attribute, OldValue, NewValue);
}

void USigilAttributeSet::RecalculateDerivedAttributes()
{
    // Recalcular atributos derivados de combate
    float NewAttackPower = CalculateDerivedValue(GetSpectralPowerAttribute(), ATTACK_POWER_MULTIPLIER);
    SetAttackPower(NewAttackPower);

    float NewDefensePower = CalculateDerivedValue(GetSpectralResilienceAttribute(), DEFENSE_POWER_MULTIPLIER);
    SetDefensePower(NewDefensePower);

    float NewAttackSpeed = CalculateDerivedValue(GetSpectralVelocityAttribute(), ATTACK_SPEED_MULTIPLIER, BASE_ATTACK_SPEED);
    SetAttackSpeed(NewAttackSpeed);

    // Recalcular chance de crítico (baseado em dois atributos)
    float NewCritChance = (GetSpectralPower() + GetSpectralFocus()) * CRIT_CHANCE_MULTIPLIER;
    NewCritChance = FMath::Clamp(NewCritChance, 0.0f, MAX_CRITICAL_CHANCE);
    SetCriticalChance(NewCritChance);

    float NewCritMultiplier = CalculateDerivedValue(GetSpectralPowerAttribute(), CRIT_MULTIPLIER_MULTIPLIER, BASE_CRITICAL_MULTIPLIER);
    SetCriticalMultiplier(NewCritMultiplier);

    // Recalcular atributos de mobilidade
    float NewMovementSpeed = CalculateDerivedValue(GetSpectralVelocityAttribute(), MOVEMENT_SPEED_MULTIPLIER, BASE_MOVEMENT_SPEED);
    SetMovementSpeed(NewMovementSpeed);

    float NewCDR = CalculateDerivedValue(GetSpectralFocusAttribute(), COOLDOWN_REDUCTION_MULTIPLIER);
    NewCDR = FMath::Clamp(NewCDR, 0.0f, MAX_COOLDOWN_REDUCTION);
    SetCooldownReduction(NewCDR);

    // Recalcular atributos de recursos
    float NewManaRegen = CalculateDerivedValue(GetSpectralFocusAttribute(), MANA_REGEN_MULTIPLIER);
    SetManaRegeneration(NewManaRegen);

    float NewHealthRegen = CalculateDerivedValue(GetSpectralResilienceAttribute(), HEALTH_REGEN_MULTIPLIER);
    SetHealthRegeneration(NewHealthRegen);

    // Recalcular eficiência de sigilo
    float NewEfficiency = 1.0f + (GetSpectralFocus() * 0.005f);
    SetSigilEfficiency(NewEfficiency);

    // Recalcular resistência a CC
    float NewCCResistance = GetSpectralResilience() * 0.002f;
    NewCCResistance = FMath::Clamp(NewCCResistance, 0.0f, MAX_CC_RESISTANCE);
    SetCCResistance(NewCCResistance);

    // Marcar cache como sujo
    bTotalPowerCacheDirty = true;
}

void USigilAttributeSet::ApplyFusionMultiplier(float Multiplier)
{
    SetFusionMultiplier(Multiplier);
    
    // Aplicar multiplicador aos atributos de combate
    SetAttackPower(GetAttackPower() * Multiplier);
    SetDefensePower(GetDefensePower() * Multiplier);
    SetAttackSpeed(GetAttackSpeed() * Multiplier);
    
    // Marcar cache como sujo
    bTotalPowerCacheDirty = true;

    // Log para debugging
    UE_LOG(LogTemp, Log, TEXT("Fusion Multiplier Applied: %f"), Multiplier);
}

void USigilAttributeSet::RemoveFusionMultiplier()
{
    if (GetFusionMultiplier() > 1.0f)
    {
        float InverseMultiplier = 1.0f / GetFusionMultiplier();
        
        // Remover multiplicador dos atributos de combate
        SetAttackPower(GetAttackPower() * InverseMultiplier);
        SetDefensePower(GetDefensePower() * InverseMultiplier);
        SetAttackSpeed(GetAttackSpeed() * InverseMultiplier);
        
        SetFusionMultiplier(1.0f);
        bTotalPowerCacheDirty = true;

        UE_LOG(LogTemp, Log, TEXT("Fusion Multiplier Removed"));
    }
}

float USigilAttributeSet::CalculateTotalSigilPower() const
{
    if (bTotalPowerCacheDirty)
    {
        // Calcular poder total como soma ponderada dos atributos espectrais
        CachedTotalPower = (GetSpectralPower() * 1.0f) +
                          (GetSpectralResilience() * 0.8f) +
                          (GetSpectralVelocity() * 0.9f) +
                          (GetSpectralFocus() * 0.7f);
        
        // Aplicar multiplicador de fusão
        CachedTotalPower *= GetFusionMultiplier();
        
        // Aplicar eficiência de sigilo
        CachedTotalPower *= GetSigilEfficiency();
        
        bTotalPowerCacheDirty = false;
    }
    
    return CachedTotalPower;
}

bool USigilAttributeSet::IsAttributeAtMaximum(const FGameplayAttribute& Attribute) const
{
    float CurrentValue = 0.0f;
    if (Attribute == GetSpectralPowerAttribute()) CurrentValue = SpectralPower.GetCurrentValue();
    else if (Attribute == GetSpectralResilienceAttribute()) CurrentValue = SpectralResilience.GetCurrentValue();
    else if (Attribute == GetSpectralVelocityAttribute()) CurrentValue = SpectralVelocity.GetCurrentValue();
    else if (Attribute == GetSpectralFocusAttribute()) CurrentValue = SpectralFocus.GetCurrentValue();
    else if (Attribute == GetAttackPowerAttribute()) CurrentValue = AttackPower.GetCurrentValue();
    else if (Attribute == GetDefensePowerAttribute()) CurrentValue = DefensePower.GetCurrentValue();
    else if (Attribute == GetAttackSpeedAttribute()) CurrentValue = AttackSpeed.GetCurrentValue();
    else if (Attribute == GetCriticalChanceAttribute()) CurrentValue = CriticalChance.GetCurrentValue();
    else if (Attribute == GetCriticalMultiplierAttribute()) CurrentValue = CriticalMultiplier.GetCurrentValue();
    else if (Attribute == GetMovementSpeedAttribute()) CurrentValue = MovementSpeed.GetCurrentValue();
    else if (Attribute == GetCooldownReductionAttribute()) CurrentValue = CooldownReduction.GetCurrentValue();
    else if (Attribute == GetManaRegenerationAttribute()) CurrentValue = ManaRegeneration.GetCurrentValue();
    else if (Attribute == GetHealthRegenerationAttribute()) CurrentValue = HealthRegeneration.GetCurrentValue();
    else if (Attribute == GetFusionMultiplierAttribute()) CurrentValue = FusionMultiplier.GetCurrentValue();
    else if (Attribute == GetSigilSlotsAttribute()) CurrentValue = SigilSlots.GetCurrentValue();
    else if (Attribute == GetSigilEfficiencyAttribute()) CurrentValue = SigilEfficiency.GetCurrentValue();
    else if (Attribute == GetSigilExperienceAttribute()) CurrentValue = SigilExperience.GetCurrentValue();
    else if (Attribute == GetTeamFightBonusAttribute()) CurrentValue = TeamFightBonus.GetCurrentValue();
    else if (Attribute == GetObjectiveBonusAttribute()) CurrentValue = ObjectiveBonus.GetCurrentValue();
    else if (Attribute == GetCCResistanceAttribute()) CurrentValue = CCResistance.GetCurrentValue();
    
    // Verificar limites específicos por atributo
    if (Attribute == GetCooldownReductionAttribute())
    {
        return CurrentValue >= MAX_COOLDOWN_REDUCTION;
    }
    else if (Attribute == GetCriticalChanceAttribute())
    {
        return CurrentValue >= MAX_CRITICAL_CHANCE;
    }
    else if (Attribute == GetCCResistanceAttribute())
    {
        return CurrentValue >= MAX_CC_RESISTANCE;
    }
    else if (Attribute == GetSigilSlotsAttribute())
    {
        return CurrentValue >= MAX_SIGIL_SLOTS;
    }
    
    // Para outros atributos, assumir que não há máximo fixo
    return false;
}

float USigilAttributeSet::GetEffectiveAttributeValue(const FGameplayAttribute& Attribute) const
{
    float BaseValue = 0.0f;
    if (Attribute == GetSpectralPowerAttribute()) BaseValue = SpectralPower.GetCurrentValue();
    else if (Attribute == GetSpectralResilienceAttribute()) BaseValue = SpectralResilience.GetCurrentValue();
    else if (Attribute == GetSpectralVelocityAttribute()) BaseValue = SpectralVelocity.GetCurrentValue();
    else if (Attribute == GetSpectralFocusAttribute()) BaseValue = SpectralFocus.GetCurrentValue();
    else if (Attribute == GetAttackPowerAttribute()) BaseValue = AttackPower.GetCurrentValue();
    else if (Attribute == GetDefensePowerAttribute()) BaseValue = DefensePower.GetCurrentValue();
    else if (Attribute == GetAttackSpeedAttribute()) BaseValue = AttackSpeed.GetCurrentValue();
    else if (Attribute == GetCriticalChanceAttribute()) BaseValue = CriticalChance.GetCurrentValue();
    else if (Attribute == GetCriticalMultiplierAttribute()) BaseValue = CriticalMultiplier.GetCurrentValue();
    else if (Attribute == GetMovementSpeedAttribute()) BaseValue = MovementSpeed.GetCurrentValue();
    else if (Attribute == GetCooldownReductionAttribute()) BaseValue = CooldownReduction.GetCurrentValue();
    else if (Attribute == GetManaRegenerationAttribute()) BaseValue = ManaRegeneration.GetCurrentValue();
    else if (Attribute == GetHealthRegenerationAttribute()) BaseValue = HealthRegeneration.GetCurrentValue();
    else if (Attribute == GetFusionMultiplierAttribute()) BaseValue = FusionMultiplier.GetCurrentValue();
    else if (Attribute == GetSigilSlotsAttribute()) BaseValue = SigilSlots.GetCurrentValue();
    else if (Attribute == GetSigilEfficiencyAttribute()) BaseValue = SigilEfficiency.GetCurrentValue();
    else if (Attribute == GetSigilExperienceAttribute()) BaseValue = SigilExperience.GetCurrentValue();
    else if (Attribute == GetTeamFightBonusAttribute()) BaseValue = TeamFightBonus.GetCurrentValue();
    else if (Attribute == GetObjectiveBonusAttribute()) BaseValue = ObjectiveBonus.GetCurrentValue();
    else if (Attribute == GetCCResistanceAttribute()) BaseValue = CCResistance.GetCurrentValue();
    
    // Aplicar multiplicadores relevantes
    if (Attribute == GetAttackPowerAttribute() ||
        Attribute == GetDefensePowerAttribute() ||
        Attribute == GetAttackSpeedAttribute())
    {
        BaseValue *= GetFusionMultiplier();
    }
    
    // Aplicar eficiência de sigilo para atributos espectrais
    if (Attribute == GetSpectralPowerAttribute() ||
        Attribute == GetSpectralResilienceAttribute() ||
        Attribute == GetSpectralVelocityAttribute() ||
        Attribute == GetSpectralFocusAttribute())
    {
        BaseValue *= GetSigilEfficiency();
    }
    
    return BaseValue;
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - ATRIBUTOS PRIMÁRIOS
// ========================================

void USigilAttributeSet::OnRep_SpectralPower(const FGameplayAttributeData& OldSpectralPower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SpectralPower, OldSpectralPower);
    RecalculateDerivedAttributes();
}

void USigilAttributeSet::OnRep_SpectralResilience(const FGameplayAttributeData& OldSpectralResilience)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SpectralResilience, OldSpectralResilience);
    RecalculateDerivedAttributes();
}

void USigilAttributeSet::OnRep_SpectralVelocity(const FGameplayAttributeData& OldSpectralVelocity)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SpectralVelocity, OldSpectralVelocity);
    RecalculateDerivedAttributes();
}

void USigilAttributeSet::OnRep_SpectralFocus(const FGameplayAttributeData& OldSpectralFocus)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SpectralFocus, OldSpectralFocus);
    RecalculateDerivedAttributes();
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - ATRIBUTOS DERIVADOS
// ========================================

void USigilAttributeSet::OnRep_AttackPower(const FGameplayAttributeData& OldAttackPower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, AttackPower, OldAttackPower);
}

void USigilAttributeSet::OnRep_DefensePower(const FGameplayAttributeData& OldDefensePower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, DefensePower, OldDefensePower);
}

void USigilAttributeSet::OnRep_AttackSpeed(const FGameplayAttributeData& OldAttackSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, AttackSpeed, OldAttackSpeed);
}

void USigilAttributeSet::OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, CriticalChance, OldCriticalChance);
}

void USigilAttributeSet::OnRep_CriticalMultiplier(const FGameplayAttributeData& OldCriticalMultiplier)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, CriticalMultiplier, OldCriticalMultiplier);
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - MOBILIDADE
// ========================================

void USigilAttributeSet::OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, MovementSpeed, OldMovementSpeed);
}

void USigilAttributeSet::OnRep_CooldownReduction(const FGameplayAttributeData& OldCooldownReduction)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, CooldownReduction, OldCooldownReduction);
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - RECURSOS
// ========================================

void USigilAttributeSet::OnRep_ManaRegeneration(const FGameplayAttributeData& OldManaRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, ManaRegeneration, OldManaRegeneration);
}

void USigilAttributeSet::OnRep_HealthRegeneration(const FGameplayAttributeData& OldHealthRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, HealthRegeneration, OldHealthRegeneration);
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - ESTADO
// ========================================

void USigilAttributeSet::OnRep_FusionMultiplier(const FGameplayAttributeData& OldFusionMultiplier)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, FusionMultiplier, OldFusionMultiplier);
    bTotalPowerCacheDirty = true;
}

void USigilAttributeSet::OnRep_SigilSlots(const FGameplayAttributeData& OldSigilSlots)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SigilSlots, OldSigilSlots);
}

void USigilAttributeSet::OnRep_SigilEfficiency(const FGameplayAttributeData& OldSigilEfficiency)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SigilEfficiency, OldSigilEfficiency);
    bTotalPowerCacheDirty = true;
}

void USigilAttributeSet::OnRep_SigilExperience(const FGameplayAttributeData& OldSigilExperience)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SigilExperience, OldSigilExperience);
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - MOBA
// ========================================

void USigilAttributeSet::OnRep_TeamFightBonus(const FGameplayAttributeData& OldTeamFightBonus)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, TeamFightBonus, OldTeamFightBonus);
}

void USigilAttributeSet::OnRep_ObjectiveBonus(const FGameplayAttributeData& OldObjectiveBonus)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, ObjectiveBonus, OldObjectiveBonus);
}

void USigilAttributeSet::OnRep_CCResistance(const FGameplayAttributeData& OldCCResistance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, CCResistance, OldCCResistance);
}

// ========================================
// FUNÇÕES INTERNAS PRIVADAS
// ========================================

void USigilAttributeSet::AdjustAttributeForMaxChange(const FGameplayAttributeData& AffectedAttribute,
                                                    const FGameplayAttributeData& MaxAttribute,
                                                    float NewMaxValue,
                                                    const FGameplayAttribute& AffectedAttributeProperty) const
{
    UAbilitySystemComponent* AbilityComp = GetOwningAbilitySystemComponent();
    const float CurrentMaxValue = MaxAttribute.GetCurrentValue();
    if (NewMaxValue != CurrentMaxValue && AbilityComp)
    {
        const float CurrentValue = AffectedAttribute.GetCurrentValue();
        const float NewDelta = (CurrentMaxValue > 0.f) ? (CurrentValue * NewMaxValue / CurrentMaxValue) - CurrentValue : NewMaxValue;

        AbilityComp->ApplyModToAttributeUnsafe(AffectedAttributeProperty, EGameplayModOp::Additive, NewDelta);
    }
}

float USigilAttributeSet::CalculateDerivedValue(const FGameplayAttribute& PrimaryAttribute,
                                              float Multiplier,
                                              float BaseValue) const
{
    float PrimaryValue = 0.0f;
    if (PrimaryAttribute == GetSpectralPowerAttribute()) PrimaryValue = SpectralPower.GetCurrentValue();
    else if (PrimaryAttribute == GetSpectralResilienceAttribute()) PrimaryValue = SpectralResilience.GetCurrentValue();
    else if (PrimaryAttribute == GetSpectralVelocityAttribute()) PrimaryValue = SpectralVelocity.GetCurrentValue();
    else if (PrimaryAttribute == GetSpectralFocusAttribute()) PrimaryValue = SpectralFocus.GetCurrentValue();
    else if (PrimaryAttribute == GetAttackPowerAttribute()) PrimaryValue = AttackPower.GetCurrentValue();
    else if (PrimaryAttribute == GetDefensePowerAttribute()) PrimaryValue = DefensePower.GetCurrentValue();
    else if (PrimaryAttribute == GetAttackSpeedAttribute()) PrimaryValue = AttackSpeed.GetCurrentValue();
    else if (PrimaryAttribute == GetCriticalChanceAttribute()) PrimaryValue = CriticalChance.GetCurrentValue();
    else if (PrimaryAttribute == GetCriticalMultiplierAttribute()) PrimaryValue = CriticalMultiplier.GetCurrentValue();
    else if (PrimaryAttribute == GetMovementSpeedAttribute()) PrimaryValue = MovementSpeed.GetCurrentValue();
    else if (PrimaryAttribute == GetCooldownReductionAttribute()) PrimaryValue = CooldownReduction.GetCurrentValue();
    else if (PrimaryAttribute == GetManaRegenerationAttribute()) PrimaryValue = ManaRegeneration.GetCurrentValue();
    else if (PrimaryAttribute == GetHealthRegenerationAttribute()) PrimaryValue = HealthRegeneration.GetCurrentValue();
    else if (PrimaryAttribute == GetFusionMultiplierAttribute()) PrimaryValue = FusionMultiplier.GetCurrentValue();
    else if (PrimaryAttribute == GetSigilSlotsAttribute()) PrimaryValue = SigilSlots.GetCurrentValue();
    else if (PrimaryAttribute == GetSigilEfficiencyAttribute()) PrimaryValue = SigilEfficiency.GetCurrentValue();
    else if (PrimaryAttribute == GetSigilExperienceAttribute()) PrimaryValue = SigilExperience.GetCurrentValue();
    else if (PrimaryAttribute == GetTeamFightBonusAttribute()) PrimaryValue = TeamFightBonus.GetCurrentValue();
    else if (PrimaryAttribute == GetObjectiveBonusAttribute()) PrimaryValue = ObjectiveBonus.GetCurrentValue();
    else if (PrimaryAttribute == GetCCResistanceAttribute()) PrimaryValue = CCResistance.GetCurrentValue();
    return BaseValue + (PrimaryValue * Multiplier);
}

void USigilAttributeSet::ClampAttributeValue(const FGameplayAttribute& Attribute, float& Value) const
{
    // Aplicar limites mínimos (todos os atributos >= 0)
    Value = FMath::Max(Value, 0.0f);

    // Aplicar limites máximos específicos
    if (Attribute == GetCooldownReductionAttribute())
    {
        Value = FMath::Min(Value, MAX_COOLDOWN_REDUCTION);
    }
    else if (Attribute == GetCriticalChanceAttribute())
    {
        Value = FMath::Min(Value, MAX_CRITICAL_CHANCE);
    }
    else if (Attribute == GetCCResistanceAttribute())
    {
        Value = FMath::Min(Value, MAX_CC_RESISTANCE);
    }
    else if (Attribute == GetSigilSlotsAttribute())
    {
        Value = FMath::Min(Value, static_cast<float>(MAX_SIGIL_SLOTS));
    }
    else if (Attribute == GetFusionMultiplierAttribute())
    {
        // Multiplicador de fusão entre 1.0 e 3.0
        Value = FMath::Clamp(Value, 1.0f, 3.0f);
    }
    else if (Attribute == GetSigilEfficiencyAttribute())
    {
        // Eficiência entre 0.5 e 2.0
        Value = FMath::Clamp(Value, 0.5f, 2.0f);
    }
}

void USigilAttributeSet::NotifyAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue)
{
    // Broadcast para sistemas que precisam saber sobre mudanças de atributos
    UAbilitySystemComponent* AbilityComp = GetOwningAbilitySystemComponent();
    if (AbilityComp)
    {
        // Notificar mudança através do AbilitySystemComponent
        // Outros sistemas podem se inscrever para essas notificações
        
        // Log para debugging
        if (GEngine && GEngine->GetNetMode(GetWorld()) != NM_DedicatedServer)
        {
            FString AttributeName = Attribute.GetName();
            UE_LOG(LogTemp, VeryVerbose, TEXT("Sigil Attribute Changed: %s from %f to %f"), 
                   *AttributeName, OldValue, NewValue);
        }
    }
}