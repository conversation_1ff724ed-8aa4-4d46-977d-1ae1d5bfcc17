// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SigilAbilities.h"
#include "ActiveGameplayEffectHandle.h"
#include "Engine/HitResult.h"
#include "Engine/TimerHandle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilAbilities() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Aegis_Murallion();
AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Aegis_Murallion_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal();
AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo();
AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityBase();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityBase_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilSubType();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayAbility();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FActiveGameplayEffectHandle();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class USigilAbilityBase Function ApplyAbilityEffect ****************************
struct Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics
{
	struct SigilAbilityBase_eventApplyAbilityEffect_Parms
	{
		AActor* Target;
		float Duration;
		FActiveGameplayEffectHandle ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplica o efeito de gameplay da habilidade */" },
#endif
		{ "CPP_Default_Duration", "-1.000000" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica o efeito de gameplay da habilidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventApplyAbilityEffect_Parms, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventApplyAbilityEffect_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventApplyAbilityEffect_Parms, ReturnValue), Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityBase, nullptr, "ApplyAbilityEffect", Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::SigilAbilityBase_eventApplyAbilityEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::SigilAbilityBase_eventApplyAbilityEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityBase::execApplyAbilityEffect)
{
	P_GET_OBJECT(AActor,Z_Param_Target);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FActiveGameplayEffectHandle*)Z_Param__Result=P_THIS->ApplyAbilityEffect(Z_Param_Target,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityBase Function ApplyAbilityEffect ******************************

// ********** Begin Class USigilAbilityBase Function CalculateEffectiveCooldown ********************
struct Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics
{
	struct SigilAbilityBase_eventCalculateEffectiveCooldown_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Calculation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcula o cooldown efetivo baseado na raridade do sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcula o cooldown efetivo baseado na raridade do sigilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventCalculateEffectiveCooldown_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityBase, nullptr, "CalculateEffectiveCooldown", Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::SigilAbilityBase_eventCalculateEffectiveCooldown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54080400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::SigilAbilityBase_eventCalculateEffectiveCooldown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityBase::execCalculateEffectiveCooldown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateEffectiveCooldown();
	P_NATIVE_END;
}
// ********** End Class USigilAbilityBase Function CalculateEffectiveCooldown **********************

// ********** Begin Class USigilAbilityBase Function CalculateEffectiveDuration ********************
struct Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics
{
	struct SigilAbilityBase_eventCalculateEffectiveDuration_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Calculation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcula a dura\xc3\xa7\xc3\xa3o efetiva baseada na raridade do sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcula a dura\xc3\xa7\xc3\xa3o efetiva baseada na raridade do sigilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventCalculateEffectiveDuration_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityBase, nullptr, "CalculateEffectiveDuration", Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::SigilAbilityBase_eventCalculateEffectiveDuration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54080400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::SigilAbilityBase_eventCalculateEffectiveDuration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityBase::execCalculateEffectiveDuration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateEffectiveDuration();
	P_NATIVE_END;
}
// ********** End Class USigilAbilityBase Function CalculateEffectiveDuration **********************

// ********** Begin Class USigilAbilityBase Function InitializeWithSigil ***************************
struct Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics
{
	struct SigilAbilityBase_eventInitializeWithSigil_Parms
	{
		ASigilItem* Sigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializa a habilidade com o sigilo propriet\xc3\xa1rio */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializa a habilidade com o sigilo propriet\xc3\xa1rio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventInitializeWithSigil_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::NewProp_Sigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityBase, nullptr, "InitializeWithSigil", Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::SigilAbilityBase_eventInitializeWithSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::SigilAbilityBase_eventInitializeWithSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityBase::execInitializeWithSigil)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeWithSigil(Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityBase Function InitializeWithSigil *****************************

// ********** Begin Class USigilAbilityBase Function SpawnAbilityVFX *******************************
struct Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics
{
	struct SigilAbilityBase_eventSpawnAbilityVFX_Parms
	{
		FVector Location;
		FRotator Rotation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Spawna efeitos visuais da habilidade */" },
#endif
		{ "CPP_Default_Rotation", "" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawna efeitos visuais da habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventSpawnAbilityVFX_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventSpawnAbilityVFX_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::NewProp_Rotation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityBase, nullptr, "SpawnAbilityVFX", Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::SigilAbilityBase_eventSpawnAbilityVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C80400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::SigilAbilityBase_eventSpawnAbilityVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityBase::execSpawnAbilityVFX)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnAbilityVFX(Z_Param_Out_Location,Z_Param_Out_Rotation);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityBase Function SpawnAbilityVFX *********************************

// ********** Begin Class USigilAbilityBase Function ValidateSigilRequirements *********************
struct Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics
{
	struct SigilAbilityBase_eventValidateSigilRequirements_Parms
	{
		ASigilItem* Sigil;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se o sigilo atende aos requisitos para esta habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se o sigilo atende aos requisitos para esta habilidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityBase_eventValidateSigilRequirements_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilAbilityBase_eventValidateSigilRequirements_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilAbilityBase_eventValidateSigilRequirements_Parms), &Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityBase, nullptr, "ValidateSigilRequirements", Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::SigilAbilityBase_eventValidateSigilRequirements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54080400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::SigilAbilityBase_eventValidateSigilRequirements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityBase::execValidateSigilRequirements)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSigilRequirements(Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityBase Function ValidateSigilRequirements ***********************

// ********** Begin Class USigilAbilityBase ********************************************************
void USigilAbilityBase::StaticRegisterNativesUSigilAbilityBase()
{
	UClass* Class = USigilAbilityBase::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyAbilityEffect", &USigilAbilityBase::execApplyAbilityEffect },
		{ "CalculateEffectiveCooldown", &USigilAbilityBase::execCalculateEffectiveCooldown },
		{ "CalculateEffectiveDuration", &USigilAbilityBase::execCalculateEffectiveDuration },
		{ "InitializeWithSigil", &USigilAbilityBase::execInitializeWithSigil },
		{ "SpawnAbilityVFX", &USigilAbilityBase::execSpawnAbilityVFX },
		{ "ValidateSigilRequirements", &USigilAbilityBase::execValidateSigilRequirements },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAbilityBase;
UClass* USigilAbilityBase::GetPrivateStaticClass()
{
	using TClass = USigilAbilityBase;
	if (!Z_Registration_Info_UClass_USigilAbilityBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAbilityBase"),
			Z_Registration_Info_UClass_USigilAbilityBase.InnerSingleton,
			StaticRegisterNativesUSigilAbilityBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAbilityBase.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAbilityBase_NoRegister()
{
	return USigilAbilityBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAbilityBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe base para todas as habilidades exclusivas de s\xc3\xadgilos\n * Implementa funcionalidades comuns e integra\xc3\xa7\xc3\xa3o com o sistema de s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilities.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe base para todas as habilidades exclusivas de s\xc3\xadgilos\nImplementa funcionalidades comuns e integra\xc3\xa7\xc3\xa3o com o sistema de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerSigil_MetaData[] = {
		{ "Category", "Sigil" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancia ao sigilo que possui esta habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia ao sigilo que possui esta habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredSubType_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Subtipo de sigilo requerido para esta habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Subtipo de sigilo requerido para esta habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumRarity_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raridade m\xc3\xadnima do sigilo para usar esta habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raridade m\xc3\xadnima do sigilo para usar esta habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\xad""culas para a habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para a habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityEffect_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de gameplay aplicado durante a habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay aplicado durante a habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDuration_MetaData[] = {
		{ "Category", "Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o base da habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o base da habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseCooldown_MetaData[] = {
		{ "Category", "Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown base da habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown base da habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseManaeCost_MetaData[] = {
		{ "Category", "Cost" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Custo de mana base */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custo de mana base" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_OwnerSigil;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequiredSubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequiredSubType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumRarity;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AbilityVFX;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AbilityEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseManaeCost;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilAbilityBase_ApplyAbilityEffect, "ApplyAbilityEffect" }, // 1726667538
		{ &Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveCooldown, "CalculateEffectiveCooldown" }, // 4170920263
		{ &Z_Construct_UFunction_USigilAbilityBase_CalculateEffectiveDuration, "CalculateEffectiveDuration" }, // 3415354644
		{ &Z_Construct_UFunction_USigilAbilityBase_InitializeWithSigil, "InitializeWithSigil" }, // 471660439
		{ &Z_Construct_UFunction_USigilAbilityBase_SpawnAbilityVFX, "SpawnAbilityVFX" }, // 723129412
		{ &Z_Construct_UFunction_USigilAbilityBase_ValidateSigilRequirements, "ValidateSigilRequirements" }, // 95704003
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAbilityBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_OwnerSigil = { "OwnerSigil", nullptr, (EPropertyFlags)0x0024080000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityBase, OwnerSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerSigil_MetaData), NewProp_OwnerSigil_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_RequiredSubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_RequiredSubType = { "RequiredSubType", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityBase, RequiredSubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredSubType_MetaData), NewProp_RequiredSubType_MetaData) }; // 3161995902
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_MinimumRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_MinimumRarity = { "MinimumRarity", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityBase, MinimumRarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumRarity_MetaData), NewProp_MinimumRarity_MetaData) }; // 3544987888
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_AbilityVFX = { "AbilityVFX", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityBase, AbilityVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityVFX_MetaData), NewProp_AbilityVFX_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_AbilityEffect = { "AbilityEffect", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityBase, AbilityEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityEffect_MetaData), NewProp_AbilityEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_BaseDuration = { "BaseDuration", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityBase, BaseDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDuration_MetaData), NewProp_BaseDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_BaseCooldown = { "BaseCooldown", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityBase, BaseCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseCooldown_MetaData), NewProp_BaseCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_BaseManaeCost = { "BaseManaeCost", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityBase, BaseManaeCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseManaeCost_MetaData), NewProp_BaseManaeCost_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAbilityBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_OwnerSigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_RequiredSubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_RequiredSubType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_MinimumRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_MinimumRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_AbilityVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_AbilityEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_BaseDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_BaseCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityBase_Statics::NewProp_BaseManaeCost,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAbilityBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayAbility,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAbilityBase_Statics::ClassParams = {
	&USigilAbilityBase::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilAbilityBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityBase_Statics::PropPointers),
	0,
	0x001000A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityBase_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAbilityBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAbilityBase()
{
	if (!Z_Registration_Info_UClass_USigilAbilityBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAbilityBase.OuterSingleton, Z_Construct_UClass_USigilAbilityBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAbilityBase.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAbilityBase);
USigilAbilityBase::~USigilAbilityBase() {}
// ********** End Class USigilAbilityBase **********************************************************

// ********** Begin Class USigilAbility_Aegis_Murallion Function ApplyProtectionToAlly *************
struct Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics
{
	struct SigilAbility_Aegis_Murallion_eventApplyProtectionToAlly_Parms
	{
		AActor* Ally;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Protection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplica prote\xc3\xa7\xc3\xa3o a um aliado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica prote\xc3\xa7\xc3\xa3o a um aliado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Ally;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::NewProp_Ally = { "Ally", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventApplyProtectionToAlly_Parms, Ally), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::NewProp_Ally,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Aegis_Murallion, nullptr, "ApplyProtectionToAlly", Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::SigilAbility_Aegis_Murallion_eventApplyProtectionToAlly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::SigilAbility_Aegis_Murallion_eventApplyProtectionToAlly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Aegis_Murallion::execApplyProtectionToAlly)
{
	P_GET_OBJECT(AActor,Z_Param_Ally);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyProtectionToAlly(Z_Param_Ally);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Aegis_Murallion Function ApplyProtectionToAlly ***************

// ********** Begin Class USigilAbility_Aegis_Murallion Function CreateBarrier *********************
struct Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics
{
	struct SigilAbility_Aegis_Murallion_eventCreateBarrier_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Barrier" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cria a barreira f\xc3\xadsica */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cria a barreira f\xc3\xadsica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventCreateBarrier_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Aegis_Murallion, nullptr, "CreateBarrier", Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::SigilAbility_Aegis_Murallion_eventCreateBarrier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::SigilAbility_Aegis_Murallion_eventCreateBarrier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Aegis_Murallion::execCreateBarrier)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateBarrier(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Aegis_Murallion Function CreateBarrier ***********************

// ********** Begin Class USigilAbility_Aegis_Murallion Function IsAlly ****************************
struct Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics
{
	struct SigilAbility_Aegis_Murallion_eventIsAlly_Parms
	{
		AActor* Actor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se um ator \xc3\xa9 aliado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se um ator \xc3\xa9 aliado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventIsAlly_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilAbility_Aegis_Murallion_eventIsAlly_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilAbility_Aegis_Murallion_eventIsAlly_Parms), &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Aegis_Murallion, nullptr, "IsAlly", Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::SigilAbility_Aegis_Murallion_eventIsAlly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::SigilAbility_Aegis_Murallion_eventIsAlly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Aegis_Murallion::execIsAlly)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAlly(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Aegis_Murallion Function IsAlly ******************************

// ********** Begin Class USigilAbility_Aegis_Murallion Function OnActorEnterBarrier ***************
struct Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics
{
	struct SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detecta aliados que entram na barreira */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detecta aliados que entram na barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms), &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Aegis_Murallion, nullptr, "OnActorEnterBarrier", Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::SigilAbility_Aegis_Murallion_eventOnActorEnterBarrier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Aegis_Murallion::execOnActorEnterBarrier)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActorEnterBarrier(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Aegis_Murallion Function OnActorEnterBarrier *****************

// ********** Begin Class USigilAbility_Aegis_Murallion Function OnActorExitBarrier ****************
struct Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics
{
	struct SigilAbility_Aegis_Murallion_eventOnActorExitBarrier_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detecta aliados que saem da barreira */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detecta aliados que saem da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorExitBarrier_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorExitBarrier_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorExitBarrier_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventOnActorExitBarrier_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::NewProp_OtherBodyIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Aegis_Murallion, nullptr, "OnActorExitBarrier", Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::SigilAbility_Aegis_Murallion_eventOnActorExitBarrier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::SigilAbility_Aegis_Murallion_eventOnActorExitBarrier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Aegis_Murallion::execOnActorExitBarrier)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActorExitBarrier(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Aegis_Murallion Function OnActorExitBarrier ******************

// ********** Begin Class USigilAbility_Aegis_Murallion Function RemoveBarrier *********************
struct Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveBarrier_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Barrier" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove a barreira */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove a barreira" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveBarrier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Aegis_Murallion, nullptr, "RemoveBarrier", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveBarrier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveBarrier_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveBarrier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveBarrier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Aegis_Murallion::execRemoveBarrier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveBarrier();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Aegis_Murallion Function RemoveBarrier ***********************

// ********** Begin Class USigilAbility_Aegis_Murallion Function RemoveProtectionFromAlly **********
struct Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics
{
	struct SigilAbility_Aegis_Murallion_eventRemoveProtectionFromAlly_Parms
	{
		AActor* Ally;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Protection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove prote\xc3\xa7\xc3\xa3o de um aliado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove prote\xc3\xa7\xc3\xa3o de um aliado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Ally;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::NewProp_Ally = { "Ally", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Aegis_Murallion_eventRemoveProtectionFromAlly_Parms, Ally), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::NewProp_Ally,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Aegis_Murallion, nullptr, "RemoveProtectionFromAlly", Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::SigilAbility_Aegis_Murallion_eventRemoveProtectionFromAlly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::SigilAbility_Aegis_Murallion_eventRemoveProtectionFromAlly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Aegis_Murallion::execRemoveProtectionFromAlly)
{
	P_GET_OBJECT(AActor,Z_Param_Ally);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveProtectionFromAlly(Z_Param_Ally);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Aegis_Murallion Function RemoveProtectionFromAlly ************

// ********** Begin Class USigilAbility_Aegis_Murallion ********************************************
void USigilAbility_Aegis_Murallion::StaticRegisterNativesUSigilAbility_Aegis_Murallion()
{
	UClass* Class = USigilAbility_Aegis_Murallion::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyProtectionToAlly", &USigilAbility_Aegis_Murallion::execApplyProtectionToAlly },
		{ "CreateBarrier", &USigilAbility_Aegis_Murallion::execCreateBarrier },
		{ "IsAlly", &USigilAbility_Aegis_Murallion::execIsAlly },
		{ "OnActorEnterBarrier", &USigilAbility_Aegis_Murallion::execOnActorEnterBarrier },
		{ "OnActorExitBarrier", &USigilAbility_Aegis_Murallion::execOnActorExitBarrier },
		{ "RemoveBarrier", &USigilAbility_Aegis_Murallion::execRemoveBarrier },
		{ "RemoveProtectionFromAlly", &USigilAbility_Aegis_Murallion::execRemoveProtectionFromAlly },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAbility_Aegis_Murallion;
UClass* USigilAbility_Aegis_Murallion::GetPrivateStaticClass()
{
	using TClass = USigilAbility_Aegis_Murallion;
	if (!Z_Registration_Info_UClass_USigilAbility_Aegis_Murallion.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAbility_Aegis_Murallion"),
			Z_Registration_Info_UClass_USigilAbility_Aegis_Murallion.InnerSingleton,
			StaticRegisterNativesUSigilAbility_Aegis_Murallion,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAbility_Aegis_Murallion.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAbility_Aegis_Murallion_NoRegister()
{
	return USigilAbility_Aegis_Murallion::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Habilidade exclusiva do Aegis: \"Murallion\"\n * Cria uma barreira circular que dura 3 segundos\n * Fornece prote\xc3\xa7\xc3\xa3o para aliados dentro da \xc3\xa1rea\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilities.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade exclusiva do Aegis: \"Murallion\"\nCria uma barreira circular que dura 3 segundos\nFornece prote\xc3\xa7\xc3\xa3o para aliados dentro da \xc3\xa1rea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BarrierRadius_MetaData[] = {
		{ "Category", "Barrier" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da barreira circular */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da barreira circular" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BarrierHeight_MetaData[] = {
		{ "Category", "Barrier" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura da barreira */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageReduction_MetaData[] = {
		{ "Category", "Barrier" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Redu\xc3\xa7\xc3\xa3o de dano fornecida pela barreira */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Redu\xc3\xa7\xc3\xa3o de dano fornecida pela barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BarrierProtectionEffect_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito aplicado aos aliados dentro da barreira */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito aplicado aos aliados dentro da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BarrierVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VFX da barreira */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BarrierCollision_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de colis\xc3\xa3o da barreira */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveBarrierVFX_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente VFX ativo da barreira */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente VFX ativo da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProtectedAllies_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aliados atualmente protegidos pela barreira */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aliados atualmente protegidos pela barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveProtectionEffects_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Handles dos efeitos ativos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Handles dos efeitos ativos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BarrierRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BarrierHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageReduction;
	static const UECodeGen_Private::FClassPropertyParams NewProp_BarrierProtectionEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BarrierVFX;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BarrierCollision;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveBarrierVFX;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ProtectedAllies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ProtectedAllies;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveProtectionEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveProtectionEffects;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_ApplyProtectionToAlly, "ApplyProtectionToAlly" }, // 1389650391
		{ &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_CreateBarrier, "CreateBarrier" }, // 2908893740
		{ &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_IsAlly, "IsAlly" }, // 2685872788
		{ &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorEnterBarrier, "OnActorEnterBarrier" }, // 4219476191
		{ &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_OnActorExitBarrier, "OnActorExitBarrier" }, // 436252694
		{ &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveBarrier, "RemoveBarrier" }, // 1810105691
		{ &Z_Construct_UFunction_USigilAbility_Aegis_Murallion_RemoveProtectionFromAlly, "RemoveProtectionFromAlly" }, // 93025877
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAbility_Aegis_Murallion>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierRadius = { "BarrierRadius", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, BarrierRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BarrierRadius_MetaData), NewProp_BarrierRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierHeight = { "BarrierHeight", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, BarrierHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BarrierHeight_MetaData), NewProp_BarrierHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_DamageReduction = { "DamageReduction", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, DamageReduction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageReduction_MetaData), NewProp_DamageReduction_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierProtectionEffect = { "BarrierProtectionEffect", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, BarrierProtectionEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BarrierProtectionEffect_MetaData), NewProp_BarrierProtectionEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierVFX = { "BarrierVFX", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, BarrierVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BarrierVFX_MetaData), NewProp_BarrierVFX_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierCollision = { "BarrierCollision", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, BarrierCollision), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BarrierCollision_MetaData), NewProp_BarrierCollision_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ActiveBarrierVFX = { "ActiveBarrierVFX", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, ActiveBarrierVFX), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveBarrierVFX_MetaData), NewProp_ActiveBarrierVFX_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ProtectedAllies_Inner = { "ProtectedAllies", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ProtectedAllies = { "ProtectedAllies", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, ProtectedAllies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProtectedAllies_MetaData), NewProp_ProtectedAllies_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ActiveProtectionEffects_Inner = { "ActiveProtectionEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ActiveProtectionEffects = { "ActiveProtectionEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Aegis_Murallion, ActiveProtectionEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveProtectionEffects_MetaData), NewProp_ActiveProtectionEffects_MetaData) }; // 386907876
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_DamageReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierProtectionEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_BarrierCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ActiveBarrierVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ProtectedAllies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ProtectedAllies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ActiveProtectionEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::NewProp_ActiveProtectionEffects,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::ClassParams = {
	&USigilAbility_Aegis_Murallion::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAbility_Aegis_Murallion()
{
	if (!Z_Registration_Info_UClass_USigilAbility_Aegis_Murallion.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAbility_Aegis_Murallion.OuterSingleton, Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAbility_Aegis_Murallion.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAbility_Aegis_Murallion);
USigilAbility_Aegis_Murallion::~USigilAbility_Aegis_Murallion() {}
// ********** End Class USigilAbility_Aegis_Murallion **********************************************

// ********** Begin Class USigilAbility_Ruin_FracassoPrismal Function ApplyDamageBuff **************
struct Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ApplyDamageBuff_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplica o buff de dano */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica o buff de dano" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ApplyDamageBuff_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal, nullptr, "ApplyDamageBuff", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ApplyDamageBuff_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ApplyDamageBuff_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ApplyDamageBuff()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ApplyDamageBuff_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Ruin_FracassoPrismal::execApplyDamageBuff)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyDamageBuff();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Ruin_FracassoPrismal Function ApplyDamageBuff ****************

// ********** Begin Class USigilAbility_Ruin_FracassoPrismal Function CalculateEffectiveCooldownReduction 
struct Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics
{
	struct SigilAbility_Ruin_FracassoPrismal_eventCalculateEffectiveCooldownReduction_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Calculation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcula a redu\xc3\xa7\xc3\xa3o efetiva de cooldown baseada na raridade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcula a redu\xc3\xa7\xc3\xa3o efetiva de cooldown baseada na raridade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Ruin_FracassoPrismal_eventCalculateEffectiveCooldownReduction_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal, nullptr, "CalculateEffectiveCooldownReduction", Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::SigilAbility_Ruin_FracassoPrismal_eventCalculateEffectiveCooldownReduction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::SigilAbility_Ruin_FracassoPrismal_eventCalculateEffectiveCooldownReduction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Ruin_FracassoPrismal::execCalculateEffectiveCooldownReduction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateEffectiveCooldownReduction();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Ruin_FracassoPrismal Function CalculateEffectiveCooldownReduction 

// ********** Begin Class USigilAbility_Ruin_FracassoPrismal Function RemoveDamageBuff *************
struct Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_RemoveDamageBuff_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove o buff de dano */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove o buff de dano" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_RemoveDamageBuff_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal, nullptr, "RemoveDamageBuff", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_RemoveDamageBuff_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_RemoveDamageBuff_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_RemoveDamageBuff()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_RemoveDamageBuff_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Ruin_FracassoPrismal::execRemoveDamageBuff)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveDamageBuff();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Ruin_FracassoPrismal Function RemoveDamageBuff ***************

// ********** Begin Class USigilAbility_Ruin_FracassoPrismal Function ResetAbilityCooldowns ********
struct Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ResetAbilityCooldowns_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cooldown" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reseta os cooldowns das habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reseta os cooldowns das habilidades" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ResetAbilityCooldowns_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal, nullptr, "ResetAbilityCooldowns", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ResetAbilityCooldowns_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ResetAbilityCooldowns_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ResetAbilityCooldowns()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ResetAbilityCooldowns_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Ruin_FracassoPrismal::execResetAbilityCooldowns)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetAbilityCooldowns();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Ruin_FracassoPrismal Function ResetAbilityCooldowns **********

// ********** Begin Class USigilAbility_Ruin_FracassoPrismal ***************************************
void USigilAbility_Ruin_FracassoPrismal::StaticRegisterNativesUSigilAbility_Ruin_FracassoPrismal()
{
	UClass* Class = USigilAbility_Ruin_FracassoPrismal::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyDamageBuff", &USigilAbility_Ruin_FracassoPrismal::execApplyDamageBuff },
		{ "CalculateEffectiveCooldownReduction", &USigilAbility_Ruin_FracassoPrismal::execCalculateEffectiveCooldownReduction },
		{ "RemoveDamageBuff", &USigilAbility_Ruin_FracassoPrismal::execRemoveDamageBuff },
		{ "ResetAbilityCooldowns", &USigilAbility_Ruin_FracassoPrismal::execResetAbilityCooldowns },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAbility_Ruin_FracassoPrismal;
UClass* USigilAbility_Ruin_FracassoPrismal::GetPrivateStaticClass()
{
	using TClass = USigilAbility_Ruin_FracassoPrismal;
	if (!Z_Registration_Info_UClass_USigilAbility_Ruin_FracassoPrismal.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAbility_Ruin_FracassoPrismal"),
			Z_Registration_Info_UClass_USigilAbility_Ruin_FracassoPrismal.InnerSingleton,
			StaticRegisterNativesUSigilAbility_Ruin_FracassoPrismal,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAbility_Ruin_FracassoPrismal.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_NoRegister()
{
	return USigilAbility_Ruin_FracassoPrismal::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Habilidade exclusiva do Ruin: \"Fracasso Prismal\"\n * Reseta parcialmente os cooldowns das habilidades\n * Aumenta temporariamente o dano causado\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilities.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade exclusiva do Ruin: \"Fracasso Prismal\"\nReseta parcialmente os cooldowns das habilidades\nAumenta temporariamente o dano causado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReductionPercent_MetaData[] = {
		{ "Category", "Cooldown Reset" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Percentual de redu\xc3\xa7\xc3\xa3o dos cooldowns (0.0 a 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Percentual de redu\xc3\xa7\xc3\xa3o dos cooldowns (0.0 a 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageBuffDuration_MetaData[] = {
		{ "Category", "Damage Buff" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do buff de dano */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do buff de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageMultiplier_MetaData[] = {
		{ "Category", "Damage Buff" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de dano durante o buff */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de dano durante o buff" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageBuffEffect_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de aumento de dano */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de aumento de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownResetVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VFX do reset de cooldown */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX do reset de cooldown" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageBuffVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VFX do buff de dano */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX do buff de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveDamageBuffHandle_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Handle do efeito de buff de dano ativo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Handle do efeito de buff de dano ativo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownReductionPercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageBuffDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FClassPropertyParams NewProp_DamageBuffEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CooldownResetVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DamageBuffVFX;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveDamageBuffHandle;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ApplyDamageBuff, "ApplyDamageBuff" }, // 535464749
		{ &Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_CalculateEffectiveCooldownReduction, "CalculateEffectiveCooldownReduction" }, // 2630720436
		{ &Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_RemoveDamageBuff, "RemoveDamageBuff" }, // 2547573453
		{ &Z_Construct_UFunction_USigilAbility_Ruin_FracassoPrismal_ResetAbilityCooldowns, "ResetAbilityCooldowns" }, // 2836305352
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAbility_Ruin_FracassoPrismal>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_CooldownReductionPercent = { "CooldownReductionPercent", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Ruin_FracassoPrismal, CooldownReductionPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReductionPercent_MetaData), NewProp_CooldownReductionPercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_DamageBuffDuration = { "DamageBuffDuration", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Ruin_FracassoPrismal, DamageBuffDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageBuffDuration_MetaData), NewProp_DamageBuffDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Ruin_FracassoPrismal, DamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageMultiplier_MetaData), NewProp_DamageMultiplier_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_DamageBuffEffect = { "DamageBuffEffect", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Ruin_FracassoPrismal, DamageBuffEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageBuffEffect_MetaData), NewProp_DamageBuffEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_CooldownResetVFX = { "CooldownResetVFX", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Ruin_FracassoPrismal, CooldownResetVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownResetVFX_MetaData), NewProp_CooldownResetVFX_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_DamageBuffVFX = { "DamageBuffVFX", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Ruin_FracassoPrismal, DamageBuffVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageBuffVFX_MetaData), NewProp_DamageBuffVFX_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_ActiveDamageBuffHandle = { "ActiveDamageBuffHandle", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Ruin_FracassoPrismal, ActiveDamageBuffHandle), Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveDamageBuffHandle_MetaData), NewProp_ActiveDamageBuffHandle_MetaData) }; // 386907876
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_CooldownReductionPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_DamageBuffDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_DamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_DamageBuffEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_CooldownResetVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_DamageBuffVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::NewProp_ActiveDamageBuffHandle,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::ClassParams = {
	&USigilAbility_Ruin_FracassoPrismal::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal()
{
	if (!Z_Registration_Info_UClass_USigilAbility_Ruin_FracassoPrismal.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAbility_Ruin_FracassoPrismal.OuterSingleton, Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAbility_Ruin_FracassoPrismal.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAbility_Ruin_FracassoPrismal);
USigilAbility_Ruin_FracassoPrismal::~USigilAbility_Ruin_FracassoPrismal() {}
// ********** End Class USigilAbility_Ruin_FracassoPrismal *****************************************

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo Function ApplyShieldToAlly *************
struct Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics
{
	struct SigilAbility_Vesper_SoproDeFluxo_eventApplyShieldToAlly_Parms
	{
		AActor* Ally;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Shield" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplica o escudo ao aliado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica o escudo ao aliado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Ally;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::NewProp_Ally = { "Ally", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Vesper_SoproDeFluxo_eventApplyShieldToAlly_Parms, Ally), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::NewProp_Ally,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, nullptr, "ApplyShieldToAlly", Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::SigilAbility_Vesper_SoproDeFluxo_eventApplyShieldToAlly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::SigilAbility_Vesper_SoproDeFluxo_eventApplyShieldToAlly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Vesper_SoproDeFluxo::execApplyShieldToAlly)
{
	P_GET_OBJECT(AActor,Z_Param_Ally);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyShieldToAlly(Z_Param_Ally);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo Function ApplyShieldToAlly ***************

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo Function CalculateDashEndLocation ******
struct Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics
{
	struct SigilAbility_Vesper_SoproDeFluxo_eventCalculateDashEndLocation_Parms
	{
		AActor* Ally;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Calculation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcula a posi\xc3\xa7\xc3\xa3o final do dash pr\xc3\xb3xima ao aliado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcula a posi\xc3\xa7\xc3\xa3o final do dash pr\xc3\xb3xima ao aliado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Ally;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::NewProp_Ally = { "Ally", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Vesper_SoproDeFluxo_eventCalculateDashEndLocation_Parms, Ally), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Vesper_SoproDeFluxo_eventCalculateDashEndLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::NewProp_Ally,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, nullptr, "CalculateDashEndLocation", Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::SigilAbility_Vesper_SoproDeFluxo_eventCalculateDashEndLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54880401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::SigilAbility_Vesper_SoproDeFluxo_eventCalculateDashEndLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Vesper_SoproDeFluxo::execCalculateDashEndLocation)
{
	P_GET_OBJECT(AActor,Z_Param_Ally);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateDashEndLocation(Z_Param_Ally);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo Function CalculateDashEndLocation ********

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo Function CalculateEffectiveShieldAmount 
struct Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics
{
	struct SigilAbility_Vesper_SoproDeFluxo_eventCalculateEffectiveShieldAmount_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Calculation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcula o valor efetivo do escudo baseado na raridade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcula o valor efetivo do escudo baseado na raridade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Vesper_SoproDeFluxo_eventCalculateEffectiveShieldAmount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, nullptr, "CalculateEffectiveShieldAmount", Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::SigilAbility_Vesper_SoproDeFluxo_eventCalculateEffectiveShieldAmount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::SigilAbility_Vesper_SoproDeFluxo_eventCalculateEffectiveShieldAmount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Vesper_SoproDeFluxo::execCalculateEffectiveShieldAmount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateEffectiveShieldAmount();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo Function CalculateEffectiveShieldAmount **

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo Function CompleteDash ******************
struct Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CompleteDash_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Finaliza o dash e aplica o escudo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Finaliza o dash e aplica o escudo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CompleteDash_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, nullptr, "CompleteDash", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CompleteDash_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CompleteDash_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CompleteDash()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CompleteDash_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Vesper_SoproDeFluxo::execCompleteDash)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteDash();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo Function CompleteDash ********************

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo Function FindNearestAlly ***************
struct Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics
{
	struct SigilAbility_Vesper_SoproDeFluxo_eventFindNearestAlly_Parms
	{
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Encontra o aliado mais pr\xc3\xb3ximo para o dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Encontra o aliado mais pr\xc3\xb3ximo para o dash" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Vesper_SoproDeFluxo_eventFindNearestAlly_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, nullptr, "FindNearestAlly", Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::SigilAbility_Vesper_SoproDeFluxo_eventFindNearestAlly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::SigilAbility_Vesper_SoproDeFluxo_eventFindNearestAlly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Vesper_SoproDeFluxo::execFindNearestAlly)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->FindNearestAlly();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo Function FindNearestAlly *****************

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo Function IsValidAllyTarget *************
struct Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics
{
	struct SigilAbility_Vesper_SoproDeFluxo_eventIsValidAllyTarget_Parms
	{
		AActor* Actor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se um ator \xc3\xa9 um aliado v\xc3\xa1lido para o dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se um ator \xc3\xa9 um aliado v\xc3\xa1lido para o dash" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Vesper_SoproDeFluxo_eventIsValidAllyTarget_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilAbility_Vesper_SoproDeFluxo_eventIsValidAllyTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilAbility_Vesper_SoproDeFluxo_eventIsValidAllyTarget_Parms), &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, nullptr, "IsValidAllyTarget", Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::SigilAbility_Vesper_SoproDeFluxo_eventIsValidAllyTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::SigilAbility_Vesper_SoproDeFluxo_eventIsValidAllyTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Vesper_SoproDeFluxo::execIsValidAllyTarget)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidAllyTarget(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo Function IsValidAllyTarget ***************

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo Function StartDashToAlly ***************
struct Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics
{
	struct SigilAbility_Vesper_SoproDeFluxo_eventStartDashToAlly_Parms
	{
		AActor* Ally;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicia o dash em dire\xc3\xa7\xc3\xa3o ao aliado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicia o dash em dire\xc3\xa7\xc3\xa3o ao aliado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Ally;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::NewProp_Ally = { "Ally", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbility_Vesper_SoproDeFluxo_eventStartDashToAlly_Parms, Ally), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::NewProp_Ally,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, nullptr, "StartDashToAlly", Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::SigilAbility_Vesper_SoproDeFluxo_eventStartDashToAlly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::SigilAbility_Vesper_SoproDeFluxo_eventStartDashToAlly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Vesper_SoproDeFluxo::execStartDashToAlly)
{
	P_GET_OBJECT(AActor,Z_Param_Ally);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartDashToAlly(Z_Param_Ally);
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo Function StartDashToAlly *****************

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo Function UpdateDashMovement ************
struct Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_UpdateDashMovement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualiza a posi\xc3\xa7\xc3\xa3o durante o dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza a posi\xc3\xa7\xc3\xa3o durante o dash" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_UpdateDashMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, nullptr, "UpdateDashMovement", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_UpdateDashMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_UpdateDashMovement_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_UpdateDashMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_UpdateDashMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbility_Vesper_SoproDeFluxo::execUpdateDashMovement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDashMovement();
	P_NATIVE_END;
}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo Function UpdateDashMovement **************

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo ****************************************
void USigilAbility_Vesper_SoproDeFluxo::StaticRegisterNativesUSigilAbility_Vesper_SoproDeFluxo()
{
	UClass* Class = USigilAbility_Vesper_SoproDeFluxo::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyShieldToAlly", &USigilAbility_Vesper_SoproDeFluxo::execApplyShieldToAlly },
		{ "CalculateDashEndLocation", &USigilAbility_Vesper_SoproDeFluxo::execCalculateDashEndLocation },
		{ "CalculateEffectiveShieldAmount", &USigilAbility_Vesper_SoproDeFluxo::execCalculateEffectiveShieldAmount },
		{ "CompleteDash", &USigilAbility_Vesper_SoproDeFluxo::execCompleteDash },
		{ "FindNearestAlly", &USigilAbility_Vesper_SoproDeFluxo::execFindNearestAlly },
		{ "IsValidAllyTarget", &USigilAbility_Vesper_SoproDeFluxo::execIsValidAllyTarget },
		{ "StartDashToAlly", &USigilAbility_Vesper_SoproDeFluxo::execStartDashToAlly },
		{ "UpdateDashMovement", &USigilAbility_Vesper_SoproDeFluxo::execUpdateDashMovement },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAbility_Vesper_SoproDeFluxo;
UClass* USigilAbility_Vesper_SoproDeFluxo::GetPrivateStaticClass()
{
	using TClass = USigilAbility_Vesper_SoproDeFluxo;
	if (!Z_Registration_Info_UClass_USigilAbility_Vesper_SoproDeFluxo.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAbility_Vesper_SoproDeFluxo"),
			Z_Registration_Info_UClass_USigilAbility_Vesper_SoproDeFluxo.InnerSingleton,
			StaticRegisterNativesUSigilAbility_Vesper_SoproDeFluxo,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAbility_Vesper_SoproDeFluxo.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_NoRegister()
{
	return USigilAbility_Vesper_SoproDeFluxo::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Habilidade exclusiva do Vesper: \"Sopro de Fluxo\"\n * Realiza dash em dire\xc3\xa7\xc3\xa3o a um aliado e aplica escudo\n * Fornece mobilidade e suporte defensivo\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilities.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade exclusiva do Vesper: \"Sopro de Fluxo\"\nRealiza dash em dire\xc3\xa7\xc3\xa3o a um aliado e aplica escudo\nFornece mobilidade e suporte defensivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDashRange_MetaData[] = {
		{ "Category", "Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alcance m\xc3\xa1ximo do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alcance m\xc3\xa1ximo do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashSpeed_MetaData[] = {
		{ "Category", "Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldAmount_MetaData[] = {
		{ "Category", "Shield" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor do escudo aplicado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor do escudo aplicado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldDuration_MetaData[] = {
		{ "Category", "Shield" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do escudo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do escudo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllyDetectionRadius_MetaData[] = {
		{ "Category", "Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de detec\xc3\xa7\xc3\xa3o de aliados */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de detec\xc3\xa7\xc3\xa3o de aliados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldEffect_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de escudo aplicado ao aliado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de escudo aplicado ao aliado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VFX do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VFX do escudo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX do escudo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetAlly_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aliado alvo do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aliado alvo do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveShieldHandle_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Handle do efeito de escudo ativo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Handle do efeito de escudo ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDashing_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Est\xc3\xa1 executando o dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Est\xc3\xa1 executando o dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashStartLocation_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o inicial do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o inicial do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashTargetLocation_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o alvo do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o alvo do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashTimerHandle_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para o movimento do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilities.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para o movimento do dash" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDashRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ShieldAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ShieldDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AllyDetectionRadius;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ShieldEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DashVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ShieldVFX;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_TargetAlly;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveShieldHandle;
	static void NewProp_bIsDashing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDashing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DashStartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DashTargetLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DashTimerHandle;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_ApplyShieldToAlly, "ApplyShieldToAlly" }, // 83846970
		{ &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateDashEndLocation, "CalculateDashEndLocation" }, // 2935406994
		{ &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CalculateEffectiveShieldAmount, "CalculateEffectiveShieldAmount" }, // 4180601388
		{ &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_CompleteDash, "CompleteDash" }, // 3361524810
		{ &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_FindNearestAlly, "FindNearestAlly" }, // 907815364
		{ &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_IsValidAllyTarget, "IsValidAllyTarget" }, // 2961278176
		{ &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_StartDashToAlly, "StartDashToAlly" }, // 4088496389
		{ &Z_Construct_UFunction_USigilAbility_Vesper_SoproDeFluxo_UpdateDashMovement, "UpdateDashMovement" }, // 2243047573
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAbility_Vesper_SoproDeFluxo>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_MaxDashRange = { "MaxDashRange", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, MaxDashRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDashRange_MetaData), NewProp_MaxDashRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashSpeed = { "DashSpeed", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, DashSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashSpeed_MetaData), NewProp_DashSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ShieldAmount = { "ShieldAmount", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, ShieldAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldAmount_MetaData), NewProp_ShieldAmount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ShieldDuration = { "ShieldDuration", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, ShieldDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldDuration_MetaData), NewProp_ShieldDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_AllyDetectionRadius = { "AllyDetectionRadius", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, AllyDetectionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllyDetectionRadius_MetaData), NewProp_AllyDetectionRadius_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ShieldEffect = { "ShieldEffect", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, ShieldEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldEffect_MetaData), NewProp_ShieldEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashVFX = { "DashVFX", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, DashVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashVFX_MetaData), NewProp_DashVFX_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ShieldVFX = { "ShieldVFX", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, ShieldVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldVFX_MetaData), NewProp_ShieldVFX_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_TargetAlly = { "TargetAlly", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, TargetAlly), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetAlly_MetaData), NewProp_TargetAlly_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ActiveShieldHandle = { "ActiveShieldHandle", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, ActiveShieldHandle), Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveShieldHandle_MetaData), NewProp_ActiveShieldHandle_MetaData) }; // 386907876
void Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_bIsDashing_SetBit(void* Obj)
{
	((USigilAbility_Vesper_SoproDeFluxo*)Obj)->bIsDashing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_bIsDashing = { "bIsDashing", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilAbility_Vesper_SoproDeFluxo), &Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_bIsDashing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDashing_MetaData), NewProp_bIsDashing_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashStartLocation = { "DashStartLocation", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, DashStartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashStartLocation_MetaData), NewProp_DashStartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashTargetLocation = { "DashTargetLocation", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, DashTargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashTargetLocation_MetaData), NewProp_DashTargetLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashTimerHandle = { "DashTimerHandle", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbility_Vesper_SoproDeFluxo, DashTimerHandle), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashTimerHandle_MetaData), NewProp_DashTimerHandle_MetaData) }; // 3834150579
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_MaxDashRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ShieldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ShieldDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_AllyDetectionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ShieldEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ShieldVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_TargetAlly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_ActiveShieldHandle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_bIsDashing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashStartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashTargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::NewProp_DashTimerHandle,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::ClassParams = {
	&USigilAbility_Vesper_SoproDeFluxo::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo()
{
	if (!Z_Registration_Info_UClass_USigilAbility_Vesper_SoproDeFluxo.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAbility_Vesper_SoproDeFluxo.OuterSingleton, Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAbility_Vesper_SoproDeFluxo.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAbility_Vesper_SoproDeFluxo);
USigilAbility_Vesper_SoproDeFluxo::~USigilAbility_Vesper_SoproDeFluxo() {}
// ********** End Class USigilAbility_Vesper_SoproDeFluxo ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilAbilityBase, USigilAbilityBase::StaticClass, TEXT("USigilAbilityBase"), &Z_Registration_Info_UClass_USigilAbilityBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAbilityBase), 169088709U) },
		{ Z_Construct_UClass_USigilAbility_Aegis_Murallion, USigilAbility_Aegis_Murallion::StaticClass, TEXT("USigilAbility_Aegis_Murallion"), &Z_Registration_Info_UClass_USigilAbility_Aegis_Murallion, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAbility_Aegis_Murallion), 4261272367U) },
		{ Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal, USigilAbility_Ruin_FracassoPrismal::StaticClass, TEXT("USigilAbility_Ruin_FracassoPrismal"), &Z_Registration_Info_UClass_USigilAbility_Ruin_FracassoPrismal, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAbility_Ruin_FracassoPrismal), 2361059656U) },
		{ Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo, USigilAbility_Vesper_SoproDeFluxo::StaticClass, TEXT("USigilAbility_Vesper_SoproDeFluxo"), &Z_Registration_Info_UClass_USigilAbility_Vesper_SoproDeFluxo, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAbility_Vesper_SoproDeFluxo), 1962277685U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h__Script_AURACRON_1090554904(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
