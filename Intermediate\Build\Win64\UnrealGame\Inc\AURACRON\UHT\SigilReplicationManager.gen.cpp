// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Multiplayer/SigilReplicationManager.h"
#include "GameplayTagContainer.h"
#include "Net/Serialization/FastArraySerializerImplementation.h"
#include "UObject/CoreNet.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilReplicationManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilManagerComponent_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilReplicationManager();
AURACRON_API UClass* Z_Construct_UClass_USigilReplicationManager_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilType();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilActiveFusionsArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilActiveFusionsEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionReplicationData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerDataArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerDataEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerStatsArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerStatsEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationStats();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
NETCORE_API UScriptStruct* Z_Construct_UScriptStruct_FFastArraySerializer();
NETCORE_API UScriptStruct* Z_Construct_UScriptStruct_FFastArraySerializerItem();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FSigilReplicationData *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilReplicationData;
class UScriptStruct* FSigilReplicationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilReplicationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilReplicationData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilReplicationData"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilReplicationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para replica\xc3\xa7\xc3\xa3o de dados de s\xc3\xadgilo\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para replica\xc3\xa7\xc3\xa3o de dados de s\xc3\xadgilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilType_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rarity_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotIndex_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionProgress_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEquipped_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilTags_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionProgress;
	static void NewProp_bIsEquipped_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEquipped;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilReplicationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, SigilID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilID_MetaData), NewProp_SigilID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, SigilType), Z_Construct_UEnum_AURACRON_ESigilType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilType_MetaData), NewProp_SigilType_MetaData) }; // 3758400079
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, Rarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rarity_MetaData), NewProp_Rarity_MetaData) }; // 3544987888
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, SlotIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotIndex_MetaData), NewProp_SlotIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_FusionProgress = { "FusionProgress", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, FusionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionProgress_MetaData), NewProp_FusionProgress_MetaData) };
void Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_bIsEquipped_SetBit(void* Obj)
{
	((FSigilReplicationData*)Obj)->bIsEquipped = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_bIsEquipped = { "bIsEquipped", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilReplicationData), &Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_bIsEquipped_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEquipped_MetaData), NewProp_bIsEquipped_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilTags = { "SigilTags", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, SigilTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilTags_MetaData), NewProp_SigilTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilReplicationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_FusionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_bIsEquipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilReplicationData",
	Z_Construct_UScriptStruct_FSigilReplicationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationData_Statics::PropPointers),
	sizeof(FSigilReplicationData),
	alignof(FSigilReplicationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilReplicationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationData()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilReplicationData.InnerSingleton, Z_Construct_UScriptStruct_FSigilReplicationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationData.InnerSingleton;
}
// ********** End ScriptStruct FSigilReplicationData ***********************************************

// ********** Begin ScriptStruct FSigilReplicationStats ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilReplicationStats;
class UScriptStruct* FSigilReplicationStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilReplicationStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilReplicationStats, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilReplicationStats"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilReplicationStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para replica\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas do sistema\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para replica\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas do sistema" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalSigils_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedSigils_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedSlots_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastReforgeTimestamp_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSystemActive_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalSigils;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EquippedSigils;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnlockedSlots;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastReforgeTimestamp;
	static void NewProp_bSystemActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSystemActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilReplicationStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_TotalSigils = { "TotalSigils", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationStats, TotalSigils), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalSigils_MetaData), NewProp_TotalSigils_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_EquippedSigils = { "EquippedSigils", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationStats, EquippedSigils), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedSigils_MetaData), NewProp_EquippedSigils_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_UnlockedSlots = { "UnlockedSlots", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationStats, UnlockedSlots), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedSlots_MetaData), NewProp_UnlockedSlots_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_LastReforgeTimestamp = { "LastReforgeTimestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationStats, LastReforgeTimestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastReforgeTimestamp_MetaData), NewProp_LastReforgeTimestamp_MetaData) };
void Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_bSystemActive_SetBit(void* Obj)
{
	((FSigilReplicationStats*)Obj)->bSystemActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_bSystemActive = { "bSystemActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilReplicationStats), &Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_bSystemActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSystemActive_MetaData), NewProp_bSystemActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_TotalSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_EquippedSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_UnlockedSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_LastReforgeTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_bSystemActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilReplicationStats",
	Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::PropPointers),
	sizeof(FSigilReplicationStats),
	alignof(FSigilReplicationStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationStats()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilReplicationStats.InnerSingleton, Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationStats.InnerSingleton;
}
// ********** End ScriptStruct FSigilReplicationStats **********************************************

// ********** Begin ScriptStruct FSigilFusionReplicationData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData;
class UScriptStruct* FSigilFusionReplicationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilFusionReplicationData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilFusionReplicationData"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para dados de fus\xc3\xa3o replicados\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de fus\xc3\xa3o replicados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionProgress_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionStartTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFusing_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetRarity_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionStartTime;
	static void NewProp_bIsFusing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFusing;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetRarity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilFusionReplicationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionReplicationData, SigilID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilID_MetaData), NewProp_SigilID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_FusionProgress = { "FusionProgress", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionReplicationData, FusionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionProgress_MetaData), NewProp_FusionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_FusionStartTime = { "FusionStartTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionReplicationData, FusionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionStartTime_MetaData), NewProp_FusionStartTime_MetaData) };
void Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_bIsFusing_SetBit(void* Obj)
{
	((FSigilFusionReplicationData*)Obj)->bIsFusing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_bIsFusing = { "bIsFusing", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilFusionReplicationData), &Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_bIsFusing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFusing_MetaData), NewProp_bIsFusing_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_TargetRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_TargetRarity = { "TargetRarity", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionReplicationData, TargetRarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetRarity_MetaData), NewProp_TargetRarity_MetaData) }; // 3544987888
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_FusionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_FusionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_bIsFusing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_TargetRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_TargetRarity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilFusionReplicationData",
	Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::PropPointers),
	sizeof(FSigilFusionReplicationData),
	alignof(FSigilFusionReplicationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionReplicationData()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.InnerSingleton, Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.InnerSingleton;
}
// ********** End ScriptStruct FSigilFusionReplicationData *****************************************

// ********** Begin ScriptStruct FSigilPlayerDataEntry *********************************************
static_assert(std::is_polymorphic<FSigilPlayerDataEntry>() == std::is_polymorphic<FFastArraySerializerItem>(), "USTRUCT FSigilPlayerDataEntry cannot be polymorphic unless super FFastArraySerializerItem is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry;
class UScriptStruct* FSigilPlayerDataEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilPlayerDataEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilPlayerDataEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Item para replica\xc3\xa7\xc3\xa3o de dados de Sigil por jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Item para replica\xc3\xa7\xc3\xa3o de dados de Sigil por jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilData_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilPlayerDataEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerDataEntry, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_SigilData_Inner = { "SigilData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(0, nullptr) }; // 1135390972
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_SigilData = { "SigilData", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerDataEntry, SigilData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilData_MetaData), NewProp_SigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_SigilData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_SigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializerItem,
	&NewStructOps,
	"SigilPlayerDataEntry",
	Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::PropPointers),
	sizeof(FSigilPlayerDataEntry),
	alignof(FSigilPlayerDataEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerDataEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.InnerSingleton, Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.InnerSingleton;
}
// ********** End ScriptStruct FSigilPlayerDataEntry ***********************************************

// ********** Begin ScriptStruct FSigilPlayerDataArray *********************************************
static_assert(std::is_polymorphic<FSigilPlayerDataArray>() == std::is_polymorphic<FFastArraySerializer>(), "USTRUCT FSigilPlayerDataArray cannot be polymorphic unless super FFastArraySerializer is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray;
class UScriptStruct* FSigilPlayerDataArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilPlayerDataArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilPlayerDataArray"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.OuterSingleton;
}
#if defined(UE_NET_HAS_IRIS_FASTARRAY_BINDING) && UE_NET_HAS_IRIS_FASTARRAY_BINDING
UE_NET_IMPLEMENT_FASTARRAY(FSigilPlayerDataArray);
#else
UE_NET_IMPLEMENT_FASTARRAY_STUB(FSigilPlayerDataArray);
#endif
struct Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Serializer para dados de Sigil por jogador - UE 5.6 PRODUCTION READY\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Serializer para dados de Sigil por jogador - UE 5.6 PRODUCTION READY" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Items_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Items_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Items;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilPlayerDataArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewProp_Items_Inner = { "Items", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilPlayerDataEntry, METADATA_PARAMS(0, nullptr) }; // 2914415567
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewProp_Items = { "Items", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerDataArray, Items), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Items_MetaData), NewProp_Items_MetaData) }; // 2914415567
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewProp_Items_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewProp_Items,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializer,
	&NewStructOps,
	"SigilPlayerDataArray",
	Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::PropPointers),
	sizeof(FSigilPlayerDataArray),
	alignof(FSigilPlayerDataArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerDataArray()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.InnerSingleton, Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.InnerSingleton;
}
// ********** End ScriptStruct FSigilPlayerDataArray ***********************************************

// ********** Begin ScriptStruct FSigilPlayerStatsEntry ********************************************
static_assert(std::is_polymorphic<FSigilPlayerStatsEntry>() == std::is_polymorphic<FFastArraySerializerItem>(), "USTRUCT FSigilPlayerStatsEntry cannot be polymorphic unless super FFastArraySerializerItem is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry;
class UScriptStruct* FSigilPlayerStatsEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilPlayerStatsEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilPlayerStatsEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Item para replica\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas por jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Item para replica\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas por jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stats_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilPlayerStatsEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerStatsEntry, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewProp_Stats = { "Stats", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerStatsEntry, Stats), Z_Construct_UScriptStruct_FSigilReplicationStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stats_MetaData), NewProp_Stats_MetaData) }; // 2192231724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewProp_Stats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializerItem,
	&NewStructOps,
	"SigilPlayerStatsEntry",
	Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::PropPointers),
	sizeof(FSigilPlayerStatsEntry),
	alignof(FSigilPlayerStatsEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerStatsEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.InnerSingleton, Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.InnerSingleton;
}
// ********** End ScriptStruct FSigilPlayerStatsEntry **********************************************

// ********** Begin ScriptStruct FSigilPlayerStatsArray ********************************************
static_assert(std::is_polymorphic<FSigilPlayerStatsArray>() == std::is_polymorphic<FFastArraySerializer>(), "USTRUCT FSigilPlayerStatsArray cannot be polymorphic unless super FFastArraySerializer is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray;
class UScriptStruct* FSigilPlayerStatsArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilPlayerStatsArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilPlayerStatsArray"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.OuterSingleton;
}
#if defined(UE_NET_HAS_IRIS_FASTARRAY_BINDING) && UE_NET_HAS_IRIS_FASTARRAY_BINDING
UE_NET_IMPLEMENT_FASTARRAY(FSigilPlayerStatsArray);
#else
UE_NET_IMPLEMENT_FASTARRAY_STUB(FSigilPlayerStatsArray);
#endif
struct Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Serializer para estat\xc3\xadsticas por jogador - UE 5.6 PRODUCTION READY\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Serializer para estat\xc3\xadsticas por jogador - UE 5.6 PRODUCTION READY" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Items_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Items_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Items;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilPlayerStatsArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewProp_Items_Inner = { "Items", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilPlayerStatsEntry, METADATA_PARAMS(0, nullptr) }; // 3618960821
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewProp_Items = { "Items", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerStatsArray, Items), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Items_MetaData), NewProp_Items_MetaData) }; // 3618960821
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewProp_Items_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewProp_Items,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializer,
	&NewStructOps,
	"SigilPlayerStatsArray",
	Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::PropPointers),
	sizeof(FSigilPlayerStatsArray),
	alignof(FSigilPlayerStatsArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerStatsArray()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.InnerSingleton, Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.InnerSingleton;
}
// ********** End ScriptStruct FSigilPlayerStatsArray **********************************************

// ********** Begin ScriptStruct FSigilActiveFusionsEntry ******************************************
static_assert(std::is_polymorphic<FSigilActiveFusionsEntry>() == std::is_polymorphic<FFastArraySerializerItem>(), "USTRUCT FSigilActiveFusionsEntry cannot be polymorphic unless super FFastArraySerializerItem is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry;
class UScriptStruct* FSigilActiveFusionsEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilActiveFusionsEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilActiveFusionsEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Item para replica\xc3\xa7\xc3\xa3o de fus\xc3\xb5""es ativas por jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Item para replica\xc3\xa7\xc3\xa3o de fus\xc3\xb5""es ativas por jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionData_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FusionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FusionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilActiveFusionsEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilActiveFusionsEntry, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionID = { "FusionID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilActiveFusionsEntry, FusionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionID_MetaData), NewProp_FusionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionData_Inner = { "FusionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilFusionReplicationData, METADATA_PARAMS(0, nullptr) }; // 4074950016
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionData = { "FusionData", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilActiveFusionsEntry, FusionData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionData_MetaData), NewProp_FusionData_MetaData) }; // 4074950016
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializerItem,
	&NewStructOps,
	"SigilActiveFusionsEntry",
	Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::PropPointers),
	sizeof(FSigilActiveFusionsEntry),
	alignof(FSigilActiveFusionsEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilActiveFusionsEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.InnerSingleton, Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.InnerSingleton;
}
// ********** End ScriptStruct FSigilActiveFusionsEntry ********************************************

// ********** Begin ScriptStruct FSigilActiveFusionsArray ******************************************
static_assert(std::is_polymorphic<FSigilActiveFusionsArray>() == std::is_polymorphic<FFastArraySerializer>(), "USTRUCT FSigilActiveFusionsArray cannot be polymorphic unless super FFastArraySerializer is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray;
class UScriptStruct* FSigilActiveFusionsArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilActiveFusionsArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilActiveFusionsArray"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.OuterSingleton;
}
#if defined(UE_NET_HAS_IRIS_FASTARRAY_BINDING) && UE_NET_HAS_IRIS_FASTARRAY_BINDING
UE_NET_IMPLEMENT_FASTARRAY(FSigilActiveFusionsArray);
#else
UE_NET_IMPLEMENT_FASTARRAY_STUB(FSigilActiveFusionsArray);
#endif
struct Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Serializer para fus\xc3\xb5""es ativas por jogador - UE 5.6 PRODUCTION READY\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Serializer para fus\xc3\xb5""es ativas por jogador - UE 5.6 PRODUCTION READY" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Items_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Items_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Items;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilActiveFusionsArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewProp_Items_Inner = { "Items", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilActiveFusionsEntry, METADATA_PARAMS(0, nullptr) }; // 2854786014
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewProp_Items = { "Items", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilActiveFusionsArray, Items), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Items_MetaData), NewProp_Items_MetaData) }; // 2854786014
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewProp_Items_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewProp_Items,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializer,
	&NewStructOps,
	"SigilActiveFusionsArray",
	Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::PropPointers),
	sizeof(FSigilActiveFusionsArray),
	alignof(FSigilActiveFusionsArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilActiveFusionsArray()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.InnerSingleton, Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.InnerSingleton;
}
// ********** End ScriptStruct FSigilActiveFusionsArray ********************************************

// ********** Begin Delegate FOnSigilEquipped ******************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilEquipped_Parms
	{
		int32 PlayerID;
		FSigilReplicationData SigilData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegados para eventos de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegados para eventos de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilEquipped_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::NewProp_SigilData = { "SigilData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilEquipped_Parms, SigilData), Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilData_MetaData), NewProp_SigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::NewProp_SigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilEquipped__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilEquipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilEquipped, int32 PlayerID, FSigilReplicationData const& SigilData)
{
	struct _Script_AURACRON_eventOnSigilEquipped_Parms
	{
		int32 PlayerID;
		FSigilReplicationData SigilData;
	};
	_Script_AURACRON_eventOnSigilEquipped_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilData=SigilData;
	OnSigilEquipped.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilEquipped ********************************************************

// ********** Begin Delegate FOnSigilUnequipped ****************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilUnequipped_Parms
	{
		int32 PlayerID;
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilUnequipped_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilUnequipped_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilUnequipped__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilUnequipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilUnequipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilUnequipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilUnequipped, int32 PlayerID, int32 SlotIndex)
{
	struct _Script_AURACRON_eventOnSigilUnequipped_Parms
	{
		int32 PlayerID;
		int32 SlotIndex;
	};
	_Script_AURACRON_eventOnSigilUnequipped_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SlotIndex=SlotIndex;
	OnSigilUnequipped.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilUnequipped ******************************************************

// ********** Begin Delegate FOnSigilReplicationFusionStarted **************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms
	{
		int32 PlayerID;
		FSigilFusionReplicationData FusionData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::NewProp_FusionData = { "FusionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms, FusionData), Z_Construct_UScriptStruct_FSigilFusionReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionData_MetaData), NewProp_FusionData_MetaData) }; // 4074950016
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::NewProp_FusionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilReplicationFusionStarted__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilReplicationFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilReplicationFusionStarted, int32 PlayerID, FSigilFusionReplicationData const& FusionData)
{
	struct _Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms
	{
		int32 PlayerID;
		FSigilFusionReplicationData FusionData;
	};
	_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.FusionData=FusionData;
	OnSigilReplicationFusionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilReplicationFusionStarted ****************************************

// ********** Begin Delegate FOnSigilReplicationFusionCompleted ************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms
	{
		int32 PlayerID;
		FSigilReplicationData NewSigilData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSigilData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewSigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::NewProp_NewSigilData = { "NewSigilData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms, NewSigilData), Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSigilData_MetaData), NewProp_NewSigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::NewProp_NewSigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilReplicationFusionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilReplicationFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilReplicationFusionCompleted, int32 PlayerID, FSigilReplicationData const& NewSigilData)
{
	struct _Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms
	{
		int32 PlayerID;
		FSigilReplicationData NewSigilData;
	};
	_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.NewSigilData=NewSigilData;
	OnSigilReplicationFusionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilReplicationFusionCompleted **************************************

// ********** Begin Delegate FOnSigilSystemStatsUpdated ********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms
	{
		FSigilReplicationStats Stats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::NewProp_Stats = { "Stats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms, Stats), Z_Construct_UScriptStruct_FSigilReplicationStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stats_MetaData), NewProp_Stats_MetaData) }; // 2192231724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::NewProp_Stats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilSystemStatsUpdated__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilSystemStatsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnSigilSystemStatsUpdated, FSigilReplicationStats const& Stats)
{
	struct _Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms
	{
		FSigilReplicationStats Stats;
	};
	_Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms Parms;
	Parms.Stats=Stats;
	OnSigilSystemStatsUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilSystemStatsUpdated **********************************************

// ********** Begin Class USigilReplicationManager Function DebugForceFullReplication **************
struct Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "DebugForceFullReplication", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execDebugForceFullReplication)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugForceFullReplication();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function DebugForceFullReplication ****************

// ********** Begin Class USigilReplicationManager Function DebugPrintReplicationStats *************
struct Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "DebugPrintReplicationStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execDebugPrintReplicationStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugPrintReplicationStats();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function DebugPrintReplicationStats ***************

// ********** Begin Class USigilReplicationManager Function DebugSimulateNetworkLag ****************
struct Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics
{
	struct SigilReplicationManager_eventDebugSimulateNetworkLag_Parms
	{
		float LagSeconds;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LagSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::NewProp_LagSeconds = { "LagSeconds", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventDebugSimulateNetworkLag_Parms, LagSeconds), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::NewProp_LagSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "DebugSimulateNetworkLag", Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::SigilReplicationManager_eventDebugSimulateNetworkLag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::SigilReplicationManager_eventDebugSimulateNetworkLag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execDebugSimulateNetworkLag)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_LagSeconds);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugSimulateNetworkLag(Z_Param_LagSeconds);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function DebugSimulateNetworkLag ******************

// ********** Begin Class USigilReplicationManager Function EnableMOBAOptimizations ****************
struct Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics
{
	struct SigilReplicationManager_eventEnableMOBAOptimizations_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((SigilReplicationManager_eventEnableMOBAOptimizations_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilReplicationManager_eventEnableMOBAOptimizations_Parms), &Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "EnableMOBAOptimizations", Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::SigilReplicationManager_eventEnableMOBAOptimizations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::SigilReplicationManager_eventEnableMOBAOptimizations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execEnableMOBAOptimizations)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableMOBAOptimizations(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function EnableMOBAOptimizations ******************

// ********** Begin Class USigilReplicationManager Function GetPlayerActiveFusions *****************
struct Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics
{
	struct SigilReplicationManager_eventGetPlayerActiveFusions_Parms
	{
		int32 PlayerID;
		TArray<FSigilFusionReplicationData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerActiveFusions_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilFusionReplicationData, METADATA_PARAMS(0, nullptr) }; // 4074950016
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerActiveFusions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4074950016
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "GetPlayerActiveFusions", Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::SigilReplicationManager_eventGetPlayerActiveFusions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::SigilReplicationManager_eventGetPlayerActiveFusions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execGetPlayerActiveFusions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FSigilFusionReplicationData>*)Z_Param__Result=P_THIS->GetPlayerActiveFusions(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function GetPlayerActiveFusions *******************

// ********** Begin Class USigilReplicationManager Function GetPlayerSigils ************************
struct Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics
{
	struct SigilReplicationManager_eventGetPlayerSigils_Parms
	{
		int32 PlayerID;
		TArray<FSigilReplicationData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de consulta\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de consulta" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerSigils_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(0, nullptr) }; // 1135390972
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerSigils_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "GetPlayerSigils", Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::SigilReplicationManager_eventGetPlayerSigils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::SigilReplicationManager_eventGetPlayerSigils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execGetPlayerSigils)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FSigilReplicationData>*)Z_Param__Result=P_THIS->GetPlayerSigils(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function GetPlayerSigils **************************

// ********** Begin Class USigilReplicationManager Function GetPlayerStats *************************
struct Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics
{
	struct SigilReplicationManager_eventGetPlayerStats_Parms
	{
		int32 PlayerID;
		FSigilReplicationStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerStats_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilReplicationStats, METADATA_PARAMS(0, nullptr) }; // 2192231724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "GetPlayerStats", Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::SigilReplicationManager_eventGetPlayerStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::SigilReplicationManager_eventGetPlayerStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execGetPlayerStats)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilReplicationStats*)Z_Param__Result=P_THIS->GetPlayerStats(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function GetPlayerStats ***************************

// ********** Begin Class USigilReplicationManager Function GetRegisteredPlayers *******************
struct Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics
{
	struct SigilReplicationManager_eventGetRegisteredPlayers_Parms
	{
		TArray<int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetRegisteredPlayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "GetRegisteredPlayers", Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::SigilReplicationManager_eventGetRegisteredPlayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::SigilReplicationManager_eventGetRegisteredPlayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execGetRegisteredPlayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<int32>*)Z_Param__Result=P_THIS->GetRegisteredPlayers();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function GetRegisteredPlayers *********************

// ********** Begin Class USigilReplicationManager Function IsPlayerRegistered *********************
struct Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics
{
	struct SigilReplicationManager_eventIsPlayerRegistered_Parms
	{
		int32 PlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventIsPlayerRegistered_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilReplicationManager_eventIsPlayerRegistered_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilReplicationManager_eventIsPlayerRegistered_Parms), &Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "IsPlayerRegistered", Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::SigilReplicationManager_eventIsPlayerRegistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::SigilReplicationManager_eventIsPlayerRegistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execIsPlayerRegistered)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerRegistered(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function IsPlayerRegistered ***********************

// ********** Begin Class USigilReplicationManager Function MulticastNotifyEquip *******************
struct SigilReplicationManager_eventMulticastNotifyEquip_Parms
{
	int32 PlayerID;
	FSigilReplicationData SigilData;
};
static FName NAME_USigilReplicationManager_MulticastNotifyEquip = FName(TEXT("MulticastNotifyEquip"));
void USigilReplicationManager::MulticastNotifyEquip(int32 PlayerID, FSigilReplicationData const& SigilData)
{
	SigilReplicationManager_eventMulticastNotifyEquip_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilData=SigilData;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_MulticastNotifyEquip);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multicast RPCs para notifica\xc3\xa7\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multicast RPCs para notifica\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyEquip_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::NewProp_SigilData = { "SigilData", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyEquip_Parms, SigilData), Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilData_MetaData), NewProp_SigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::NewProp_SigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "MulticastNotifyEquip", Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::PropPointers), sizeof(SigilReplicationManager_eventMulticastNotifyEquip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00024CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventMulticastNotifyEquip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execMulticastNotifyEquip)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_STRUCT(FSigilReplicationData,Z_Param_SigilData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyEquip_Implementation(Z_Param_PlayerID,Z_Param_SigilData);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function MulticastNotifyEquip *********************

// ********** Begin Class USigilReplicationManager Function MulticastNotifyFusionComplete **********
struct SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms
{
	int32 PlayerID;
	FSigilReplicationData NewSigilData;
};
static FName NAME_USigilReplicationManager_MulticastNotifyFusionComplete = FName(TEXT("MulticastNotifyFusionComplete"));
void USigilReplicationManager::MulticastNotifyFusionComplete(int32 PlayerID, FSigilReplicationData const& NewSigilData)
{
	SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.NewSigilData=NewSigilData;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_MulticastNotifyFusionComplete);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSigilData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewSigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::NewProp_NewSigilData = { "NewSigilData", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms, NewSigilData), Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSigilData_MetaData), NewProp_NewSigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::NewProp_NewSigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "MulticastNotifyFusionComplete", Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::PropPointers), sizeof(SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00024CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execMulticastNotifyFusionComplete)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_STRUCT(FSigilReplicationData,Z_Param_NewSigilData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyFusionComplete_Implementation(Z_Param_PlayerID,Z_Param_NewSigilData);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function MulticastNotifyFusionComplete ************

// ********** Begin Class USigilReplicationManager Function MulticastNotifyFusionStart *************
struct SigilReplicationManager_eventMulticastNotifyFusionStart_Parms
{
	int32 PlayerID;
	FSigilFusionReplicationData FusionData;
};
static FName NAME_USigilReplicationManager_MulticastNotifyFusionStart = FName(TEXT("MulticastNotifyFusionStart"));
void USigilReplicationManager::MulticastNotifyFusionStart(int32 PlayerID, FSigilFusionReplicationData const& FusionData)
{
	SigilReplicationManager_eventMulticastNotifyFusionStart_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.FusionData=FusionData;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_MulticastNotifyFusionStart);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyFusionStart_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::NewProp_FusionData = { "FusionData", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyFusionStart_Parms, FusionData), Z_Construct_UScriptStruct_FSigilFusionReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionData_MetaData), NewProp_FusionData_MetaData) }; // 4074950016
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::NewProp_FusionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "MulticastNotifyFusionStart", Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::PropPointers), sizeof(SigilReplicationManager_eventMulticastNotifyFusionStart_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00024CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventMulticastNotifyFusionStart_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execMulticastNotifyFusionStart)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_STRUCT(FSigilFusionReplicationData,Z_Param_FusionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyFusionStart_Implementation(Z_Param_PlayerID,Z_Param_FusionData);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function MulticastNotifyFusionStart ***************

// ********** Begin Class USigilReplicationManager Function MulticastNotifyUnequip *****************
struct SigilReplicationManager_eventMulticastNotifyUnequip_Parms
{
	int32 PlayerID;
	int32 SlotIndex;
};
static FName NAME_USigilReplicationManager_MulticastNotifyUnequip = FName(TEXT("MulticastNotifyUnequip"));
void USigilReplicationManager::MulticastNotifyUnequip(int32 PlayerID, int32 SlotIndex)
{
	SigilReplicationManager_eventMulticastNotifyUnequip_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_MulticastNotifyUnequip);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyUnequip_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyUnequip_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "MulticastNotifyUnequip", Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::PropPointers), sizeof(SigilReplicationManager_eventMulticastNotifyUnequip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00024CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventMulticastNotifyUnequip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execMulticastNotifyUnequip)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyUnequip_Implementation(Z_Param_PlayerID,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function MulticastNotifyUnequip *******************

// ********** Begin Class USigilReplicationManager Function OnRep_ActiveFusions ********************
struct Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "OnRep_ActiveFusions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execOnRep_ActiveFusions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ActiveFusions();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function OnRep_ActiveFusions **********************

// ********** Begin Class USigilReplicationManager Function OnRep_PlayerSigilData ******************
struct Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de callback de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de callback de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "OnRep_PlayerSigilData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execOnRep_PlayerSigilData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_PlayerSigilData();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function OnRep_PlayerSigilData ********************

// ********** Begin Class USigilReplicationManager Function OnRep_PlayerSystemStats ****************
struct Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "OnRep_PlayerSystemStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execOnRep_PlayerSystemStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_PlayerSystemStats();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function OnRep_PlayerSystemStats ******************

// ********** Begin Class USigilReplicationManager Function OptimizeReplicationForDistance *********
struct Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics
{
	struct SigilReplicationManager_eventOptimizeReplicationForDistance_Parms
	{
		AActor* ViewerActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ViewerActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::NewProp_ViewerActor = { "ViewerActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventOptimizeReplicationForDistance_Parms, ViewerActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::NewProp_ViewerActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "OptimizeReplicationForDistance", Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::SigilReplicationManager_eventOptimizeReplicationForDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::SigilReplicationManager_eventOptimizeReplicationForDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execOptimizeReplicationForDistance)
{
	P_GET_OBJECT(AActor,Z_Param_ViewerActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeReplicationForDistance(Z_Param_ViewerActor);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function OptimizeReplicationForDistance ***********

// ********** Begin Class USigilReplicationManager Function RegisterPlayer *************************
struct Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics
{
	struct SigilReplicationManager_eventRegisterPlayer_Parms
	{
		int32 PlayerID;
		USigilManagerComponent* SigilManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es principais de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es principais de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilManager_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventRegisterPlayer_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::NewProp_SigilManager = { "SigilManager", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventRegisterPlayer_Parms, SigilManager), Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilManager_MetaData), NewProp_SigilManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::NewProp_SigilManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "RegisterPlayer", Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::SigilReplicationManager_eventRegisterPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::SigilReplicationManager_eventRegisterPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execRegisterPlayer)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_OBJECT(USigilManagerComponent,Z_Param_SigilManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterPlayer(Z_Param_PlayerID,Z_Param_SigilManager);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function RegisterPlayer ***************************

// ********** Begin Class USigilReplicationManager Function ReplicateFusionComplete ****************
struct Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics
{
	struct SigilReplicationManager_eventReplicateFusionComplete_Parms
	{
		int32 PlayerID;
		ASigilItem* OldSigil;
		ASigilItem* NewSigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OldSigil;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewSigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionComplete_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_OldSigil = { "OldSigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionComplete_Parms, OldSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_NewSigil = { "NewSigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionComplete_Parms, NewSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_OldSigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_NewSigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ReplicateFusionComplete", Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::SigilReplicationManager_eventReplicateFusionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::SigilReplicationManager_eventReplicateFusionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execReplicateFusionComplete)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_OBJECT(ASigilItem,Z_Param_OldSigil);
	P_GET_OBJECT(ASigilItem,Z_Param_NewSigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReplicateFusionComplete(Z_Param_PlayerID,Z_Param_OldSigil,Z_Param_NewSigil);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ReplicateFusionComplete ******************

// ********** Begin Class USigilReplicationManager Function ReplicateFusionStart *******************
struct Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics
{
	struct SigilReplicationManager_eventReplicateFusionStart_Parms
	{
		int32 PlayerID;
		ASigilItem* Sigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionStart_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionStart_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::NewProp_Sigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ReplicateFusionStart", Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::SigilReplicationManager_eventReplicateFusionStart_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::SigilReplicationManager_eventReplicateFusionStart_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execReplicateFusionStart)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReplicateFusionStart(Z_Param_PlayerID,Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ReplicateFusionStart *********************

// ********** Begin Class USigilReplicationManager Function ReplicateSigilEquip ********************
struct Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics
{
	struct SigilReplicationManager_eventReplicateSigilEquip_Parms
	{
		int32 PlayerID;
		ASigilItem* Sigil;
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilEquip_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilEquip_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilEquip_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ReplicateSigilEquip", Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::SigilReplicationManager_eventReplicateSigilEquip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::SigilReplicationManager_eventReplicateSigilEquip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execReplicateSigilEquip)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReplicateSigilEquip(Z_Param_PlayerID,Z_Param_Sigil,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ReplicateSigilEquip **********************

// ********** Begin Class USigilReplicationManager Function ReplicateSigilUnequip ******************
struct Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics
{
	struct SigilReplicationManager_eventReplicateSigilUnequip_Parms
	{
		int32 PlayerID;
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilUnequip_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilUnequip_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ReplicateSigilUnequip", Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::SigilReplicationManager_eventReplicateSigilUnequip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::SigilReplicationManager_eventReplicateSigilUnequip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execReplicateSigilUnequip)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReplicateSigilUnequip(Z_Param_PlayerID,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ReplicateSigilUnequip ********************

// ********** Begin Class USigilReplicationManager Function ServerEquipSigil ***********************
struct SigilReplicationManager_eventServerEquipSigil_Parms
{
	int32 PlayerID;
	int32 SigilID;
	int32 SlotIndex;
};
static FName NAME_USigilReplicationManager_ServerEquipSigil = FName(TEXT("ServerEquipSigil"));
void USigilReplicationManager::ServerEquipSigil(int32 PlayerID, int32 SigilID, int32 SlotIndex)
{
	SigilReplicationManager_eventServerEquipSigil_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilID=SigilID;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerEquipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// RPCs para comunica\xc3\xa7\xc3\xa3o cliente-servidor\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPCs para comunica\xc3\xa7\xc3\xa3o cliente-servidor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerEquipSigil_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerEquipSigil_Parms, SigilID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerEquipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerEquipSigil", Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerEquipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerEquipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerEquipSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SigilID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerEquipSigil_Validate(Z_Param_PlayerID,Z_Param_SigilID,Z_Param_SlotIndex))
	{
		RPC_ValidateFailed(TEXT("ServerEquipSigil_Validate"));
		return;
	}
	P_THIS->ServerEquipSigil_Implementation(Z_Param_PlayerID,Z_Param_SigilID,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerEquipSigil *************************

// ********** Begin Class USigilReplicationManager Function ServerForceFusion **********************
struct SigilReplicationManager_eventServerForceFusion_Parms
{
	int32 PlayerID;
	int32 SigilID;
};
static FName NAME_USigilReplicationManager_ServerForceFusion = FName(TEXT("ServerForceFusion"));
void USigilReplicationManager::ServerForceFusion(int32 PlayerID, int32 SigilID)
{
	SigilReplicationManager_eventServerForceFusion_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilID=SigilID;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerForceFusion);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerForceFusion_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerForceFusion_Parms, SigilID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::NewProp_SigilID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerForceFusion", Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerForceFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerForceFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerForceFusion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SigilID);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerForceFusion_Validate(Z_Param_PlayerID,Z_Param_SigilID))
	{
		RPC_ValidateFailed(TEXT("ServerForceFusion_Validate"));
		return;
	}
	P_THIS->ServerForceFusion_Implementation(Z_Param_PlayerID,Z_Param_SigilID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerForceFusion ************************

// ********** Begin Class USigilReplicationManager Function ServerReforge **************************
struct SigilReplicationManager_eventServerReforge_Parms
{
	int32 PlayerID;
};
static FName NAME_USigilReplicationManager_ServerReforge = FName(TEXT("ServerReforge"));
void USigilReplicationManager::ServerReforge(int32 PlayerID)
{
	SigilReplicationManager_eventServerReforge_Parms Parms;
	Parms.PlayerID=PlayerID;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerReforge);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerReforge_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerReforge", Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerReforge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerReforge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerReforge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerReforge)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerReforge_Validate(Z_Param_PlayerID))
	{
		RPC_ValidateFailed(TEXT("ServerReforge_Validate"));
		return;
	}
	P_THIS->ServerReforge_Implementation(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerReforge ****************************

// ********** Begin Class USigilReplicationManager Function ServerStartFusion **********************
struct SigilReplicationManager_eventServerStartFusion_Parms
{
	int32 PlayerID;
	int32 SigilID;
};
static FName NAME_USigilReplicationManager_ServerStartFusion = FName(TEXT("ServerStartFusion"));
void USigilReplicationManager::ServerStartFusion(int32 PlayerID, int32 SigilID)
{
	SigilReplicationManager_eventServerStartFusion_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilID=SigilID;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerStartFusion);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerStartFusion_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerStartFusion_Parms, SigilID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::NewProp_SigilID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerStartFusion", Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerStartFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerStartFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerStartFusion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SigilID);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerStartFusion_Validate(Z_Param_PlayerID,Z_Param_SigilID))
	{
		RPC_ValidateFailed(TEXT("ServerStartFusion_Validate"));
		return;
	}
	P_THIS->ServerStartFusion_Implementation(Z_Param_PlayerID,Z_Param_SigilID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerStartFusion ************************

// ********** Begin Class USigilReplicationManager Function ServerUnequipSigil *********************
struct SigilReplicationManager_eventServerUnequipSigil_Parms
{
	int32 PlayerID;
	int32 SlotIndex;
};
static FName NAME_USigilReplicationManager_ServerUnequipSigil = FName(TEXT("ServerUnequipSigil"));
void USigilReplicationManager::ServerUnequipSigil(int32 PlayerID, int32 SlotIndex)
{
	SigilReplicationManager_eventServerUnequipSigil_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerUnequipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerUnequipSigil_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerUnequipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerUnequipSigil", Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerUnequipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerUnequipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerUnequipSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerUnequipSigil_Validate(Z_Param_PlayerID,Z_Param_SlotIndex))
	{
		RPC_ValidateFailed(TEXT("ServerUnequipSigil_Validate"));
		return;
	}
	P_THIS->ServerUnequipSigil_Implementation(Z_Param_PlayerID,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerUnequipSigil ***********************

// ********** Begin Class USigilReplicationManager Function SetReplicationPriority *****************
struct Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics
{
	struct SigilReplicationManager_eventSetReplicationPriority_Parms
	{
		int32 PlayerID;
		float Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Otimiza\xc3\xa7\xc3\xb5""es de rede\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimiza\xc3\xa7\xc3\xb5""es de rede" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventSetReplicationPriority_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventSetReplicationPriority_Parms, Priority), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "SetReplicationPriority", Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::SigilReplicationManager_eventSetReplicationPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::SigilReplicationManager_eventSetReplicationPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execSetReplicationPriority)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetReplicationPriority(Z_Param_PlayerID,Z_Param_Priority);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function SetReplicationPriority *******************

// ********** Begin Class USigilReplicationManager Function UnregisterPlayer ***********************
struct Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics
{
	struct SigilReplicationManager_eventUnregisterPlayer_Parms
	{
		int32 PlayerID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventUnregisterPlayer_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "UnregisterPlayer", Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::SigilReplicationManager_eventUnregisterPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::SigilReplicationManager_eventUnregisterPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execUnregisterPlayer)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterPlayer(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function UnregisterPlayer *************************

// ********** Begin Class USigilReplicationManager Function UpdatePlayerStats **********************
struct Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics
{
	struct SigilReplicationManager_eventUpdatePlayerStats_Parms
	{
		int32 PlayerID;
		FSigilReplicationStats Stats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventUpdatePlayerStats_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::NewProp_Stats = { "Stats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventUpdatePlayerStats_Parms, Stats), Z_Construct_UScriptStruct_FSigilReplicationStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stats_MetaData), NewProp_Stats_MetaData) }; // 2192231724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::NewProp_Stats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "UpdatePlayerStats", Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::SigilReplicationManager_eventUpdatePlayerStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::SigilReplicationManager_eventUpdatePlayerStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execUpdatePlayerStats)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FSigilReplicationStats,Z_Param_Out_Stats);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePlayerStats(Z_Param_PlayerID,Z_Param_Out_Stats);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function UpdatePlayerStats ************************

// ********** Begin Class USigilReplicationManager *************************************************
void USigilReplicationManager::StaticRegisterNativesUSigilReplicationManager()
{
	UClass* Class = USigilReplicationManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "DebugForceFullReplication", &USigilReplicationManager::execDebugForceFullReplication },
		{ "DebugPrintReplicationStats", &USigilReplicationManager::execDebugPrintReplicationStats },
		{ "DebugSimulateNetworkLag", &USigilReplicationManager::execDebugSimulateNetworkLag },
		{ "EnableMOBAOptimizations", &USigilReplicationManager::execEnableMOBAOptimizations },
		{ "GetPlayerActiveFusions", &USigilReplicationManager::execGetPlayerActiveFusions },
		{ "GetPlayerSigils", &USigilReplicationManager::execGetPlayerSigils },
		{ "GetPlayerStats", &USigilReplicationManager::execGetPlayerStats },
		{ "GetRegisteredPlayers", &USigilReplicationManager::execGetRegisteredPlayers },
		{ "IsPlayerRegistered", &USigilReplicationManager::execIsPlayerRegistered },
		{ "MulticastNotifyEquip", &USigilReplicationManager::execMulticastNotifyEquip },
		{ "MulticastNotifyFusionComplete", &USigilReplicationManager::execMulticastNotifyFusionComplete },
		{ "MulticastNotifyFusionStart", &USigilReplicationManager::execMulticastNotifyFusionStart },
		{ "MulticastNotifyUnequip", &USigilReplicationManager::execMulticastNotifyUnequip },
		{ "OnRep_ActiveFusions", &USigilReplicationManager::execOnRep_ActiveFusions },
		{ "OnRep_PlayerSigilData", &USigilReplicationManager::execOnRep_PlayerSigilData },
		{ "OnRep_PlayerSystemStats", &USigilReplicationManager::execOnRep_PlayerSystemStats },
		{ "OptimizeReplicationForDistance", &USigilReplicationManager::execOptimizeReplicationForDistance },
		{ "RegisterPlayer", &USigilReplicationManager::execRegisterPlayer },
		{ "ReplicateFusionComplete", &USigilReplicationManager::execReplicateFusionComplete },
		{ "ReplicateFusionStart", &USigilReplicationManager::execReplicateFusionStart },
		{ "ReplicateSigilEquip", &USigilReplicationManager::execReplicateSigilEquip },
		{ "ReplicateSigilUnequip", &USigilReplicationManager::execReplicateSigilUnequip },
		{ "ServerEquipSigil", &USigilReplicationManager::execServerEquipSigil },
		{ "ServerForceFusion", &USigilReplicationManager::execServerForceFusion },
		{ "ServerReforge", &USigilReplicationManager::execServerReforge },
		{ "ServerStartFusion", &USigilReplicationManager::execServerStartFusion },
		{ "ServerUnequipSigil", &USigilReplicationManager::execServerUnequipSigil },
		{ "SetReplicationPriority", &USigilReplicationManager::execSetReplicationPriority },
		{ "UnregisterPlayer", &USigilReplicationManager::execUnregisterPlayer },
		{ "UpdatePlayerStats", &USigilReplicationManager::execUpdatePlayerStats },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilReplicationManager;
UClass* USigilReplicationManager::GetPrivateStaticClass()
{
	using TClass = USigilReplicationManager;
	if (!Z_Registration_Info_UClass_USigilReplicationManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilReplicationManager"),
			Z_Registration_Info_UClass_USigilReplicationManager.InnerSingleton,
			StaticRegisterNativesUSigilReplicationManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilReplicationManager.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilReplicationManager_NoRegister()
{
	return USigilReplicationManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilReplicationManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Gerenciador de replica\xc3\xa7\xc3\xa3o para o sistema de s\xc3\xadgilos em ambiente MOBA 5x5\n * Suporta at\xc3\xa9 10 jogadores simult\xc3\xa2neos com otimiza\xc3\xa7\xc3\xb5""es de rede\n */" },
#endif
		{ "IncludePath", "Multiplayer/SigilReplicationManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciador de replica\xc3\xa7\xc3\xa3o para o sistema de s\xc3\xadgilos em ambiente MOBA 5x5\nSuporta at\xc3\xa9 10 jogadores simult\xc3\xa2neos com otimiza\xc3\xa7\xc3\xb5""es de rede" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayers_MetaData[] = {
		{ "Category", "Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xa3o de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationFrequency_MetaData[] = {
		{ "Category", "Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeForMOBA_MetaData[] = {
		{ "Category", "Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Hz\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Hz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxReplicationDistance_MetaData[] = {
		{ "Category", "Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerSigilDataArray_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados replicados usando FFastArraySerializer\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados replicados usando FFastArraySerializer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerSystemStatsArray_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveFusionsArray_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilEquipped_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilUnequipped_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilFusionStarted_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilFusionCompleted_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilSystemStatsUpdated_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredManagers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados internos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados internos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerReplicationPriorities_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PendingReplications_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalReplicationsSent_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estat\xc3\xadsticas de rede\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas de rede" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalReplicationsReceived_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageReplicationSize_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkBandwidthUsed_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReplicationFrequency;
	static void NewProp_bOptimizeForMOBA_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeForMOBA;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxReplicationDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerSigilDataArray;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerSystemStatsArray;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveFusionsArray;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilEquipped;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilUnequipped;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilFusionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilFusionCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilSystemStatsUpdated;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RegisteredManagers_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RegisteredManagers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredManagers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerReplicationPriorities_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerReplicationPriorities_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerReplicationPriorities;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PendingReplications_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PendingReplications;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalReplicationsSent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalReplicationsReceived;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageReplicationSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkBandwidthUsed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication, "DebugForceFullReplication" }, // 2980608089
		{ &Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats, "DebugPrintReplicationStats" }, // 236304037
		{ &Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag, "DebugSimulateNetworkLag" }, // 1343363922
		{ &Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations, "EnableMOBAOptimizations" }, // 2955673951
		{ &Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions, "GetPlayerActiveFusions" }, // 548125989
		{ &Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils, "GetPlayerSigils" }, // 2447439334
		{ &Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats, "GetPlayerStats" }, // 1709379116
		{ &Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers, "GetRegisteredPlayers" }, // 4086896788
		{ &Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered, "IsPlayerRegistered" }, // 969777628
		{ &Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip, "MulticastNotifyEquip" }, // 984738762
		{ &Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete, "MulticastNotifyFusionComplete" }, // 3525060545
		{ &Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart, "MulticastNotifyFusionStart" }, // 3391583346
		{ &Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip, "MulticastNotifyUnequip" }, // 129305045
		{ &Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions, "OnRep_ActiveFusions" }, // 1289251947
		{ &Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData, "OnRep_PlayerSigilData" }, // 3838066646
		{ &Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats, "OnRep_PlayerSystemStats" }, // 3124009867
		{ &Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance, "OptimizeReplicationForDistance" }, // 2758966223
		{ &Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer, "RegisterPlayer" }, // 414353555
		{ &Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete, "ReplicateFusionComplete" }, // 1898802866
		{ &Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart, "ReplicateFusionStart" }, // 2503874245
		{ &Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip, "ReplicateSigilEquip" }, // 436117084
		{ &Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip, "ReplicateSigilUnequip" }, // 4098669783
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil, "ServerEquipSigil" }, // 969334517
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion, "ServerForceFusion" }, // 1209176791
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerReforge, "ServerReforge" }, // 3936180501
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion, "ServerStartFusion" }, // 2356067191
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil, "ServerUnequipSigil" }, // 3804628561
		{ &Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority, "SetReplicationPriority" }, // 3409658185
		{ &Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer, "UnregisterPlayer" }, // 1492838579
		{ &Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats, "UpdatePlayerStats" }, // 3703334491
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilReplicationManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_MaxPlayers = { "MaxPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, MaxPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayers_MetaData), NewProp_MaxPlayers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ReplicationFrequency = { "ReplicationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, ReplicationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationFrequency_MetaData), NewProp_ReplicationFrequency_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bOptimizeForMOBA_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bOptimizeForMOBA = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bOptimizeForMOBA = { "bOptimizeForMOBA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bOptimizeForMOBA_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeForMOBA_MetaData), NewProp_bOptimizeForMOBA_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_MaxReplicationDistance = { "MaxReplicationDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, MaxReplicationDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxReplicationDistance_MetaData), NewProp_MaxReplicationDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerSigilDataArray = { "PlayerSigilDataArray", nullptr, (EPropertyFlags)0x0010000000000020, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, PlayerSigilDataArray), Z_Construct_UScriptStruct_FSigilPlayerDataArray, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerSigilDataArray_MetaData), NewProp_PlayerSigilDataArray_MetaData) }; // 180768312
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerSystemStatsArray = { "PlayerSystemStatsArray", nullptr, (EPropertyFlags)0x0010000000000020, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, PlayerSystemStatsArray), Z_Construct_UScriptStruct_FSigilPlayerStatsArray, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerSystemStatsArray_MetaData), NewProp_PlayerSystemStatsArray_MetaData) }; // 1239708595
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ActiveFusionsArray = { "ActiveFusionsArray", nullptr, (EPropertyFlags)0x0010000000000020, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, ActiveFusionsArray), Z_Construct_UScriptStruct_FSigilActiveFusionsArray, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveFusionsArray_MetaData), NewProp_ActiveFusionsArray_MetaData) }; // 1547376004
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilEquipped = { "OnSigilEquipped", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilEquipped), Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilEquipped_MetaData), NewProp_OnSigilEquipped_MetaData) }; // 2670181724
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilUnequipped = { "OnSigilUnequipped", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilUnequipped), Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilUnequipped_MetaData), NewProp_OnSigilUnequipped_MetaData) }; // 3950600408
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilFusionStarted = { "OnSigilFusionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilFusionStarted), Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilFusionStarted_MetaData), NewProp_OnSigilFusionStarted_MetaData) }; // 3381686676
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilFusionCompleted = { "OnSigilFusionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilFusionCompleted), Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilFusionCompleted_MetaData), NewProp_OnSigilFusionCompleted_MetaData) }; // 2230623330
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilSystemStatsUpdated = { "OnSigilSystemStatsUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilSystemStatsUpdated), Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilSystemStatsUpdated_MetaData), NewProp_OnSigilSystemStatsUpdated_MetaData) }; // 3107523442
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers_ValueProp = { "RegisteredManagers", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers_Key_KeyProp = { "RegisteredManagers_Key", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers = { "RegisteredManagers", nullptr, (EPropertyFlags)0x0020088000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, RegisteredManagers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredManagers_MetaData), NewProp_RegisteredManagers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities_ValueProp = { "PlayerReplicationPriorities", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities_Key_KeyProp = { "PlayerReplicationPriorities_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities = { "PlayerReplicationPriorities", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, PlayerReplicationPriorities), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerReplicationPriorities_MetaData), NewProp_PlayerReplicationPriorities_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PendingReplications_Inner = { "PendingReplications", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(0, nullptr) }; // 1135390972
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PendingReplications = { "PendingReplications", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, PendingReplications), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PendingReplications_MetaData), NewProp_PendingReplications_MetaData) }; // 1135390972
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_TotalReplicationsSent = { "TotalReplicationsSent", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, TotalReplicationsSent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalReplicationsSent_MetaData), NewProp_TotalReplicationsSent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_TotalReplicationsReceived = { "TotalReplicationsReceived", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, TotalReplicationsReceived), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalReplicationsReceived_MetaData), NewProp_TotalReplicationsReceived_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_AverageReplicationSize = { "AverageReplicationSize", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, AverageReplicationSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageReplicationSize_MetaData), NewProp_AverageReplicationSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_NetworkBandwidthUsed = { "NetworkBandwidthUsed", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, NetworkBandwidthUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkBandwidthUsed_MetaData), NewProp_NetworkBandwidthUsed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilReplicationManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_MaxPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ReplicationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bOptimizeForMOBA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_MaxReplicationDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerSigilDataArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerSystemStatsArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ActiveFusionsArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilEquipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilUnequipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilFusionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilFusionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilSystemStatsUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PendingReplications_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PendingReplications,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_TotalReplicationsSent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_TotalReplicationsReceived,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_AverageReplicationSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_NetworkBandwidthUsed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReplicationManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilReplicationManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReplicationManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilReplicationManager_Statics::ClassParams = {
	&USigilReplicationManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilReplicationManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilReplicationManager_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReplicationManager_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilReplicationManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilReplicationManager()
{
	if (!Z_Registration_Info_UClass_USigilReplicationManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilReplicationManager.OuterSingleton, Z_Construct_UClass_USigilReplicationManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilReplicationManager.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void USigilReplicationManager::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_PlayerSigilDataArray(TEXT("PlayerSigilDataArray"));
	static FName Name_PlayerSystemStatsArray(TEXT("PlayerSystemStatsArray"));
	static FName Name_ActiveFusionsArray(TEXT("ActiveFusionsArray"));
	const bool bIsValid = true
		&& Name_PlayerSigilDataArray == ClassReps[(int32)ENetFields_Private::PlayerSigilDataArray].Property->GetFName()
		&& Name_PlayerSystemStatsArray == ClassReps[(int32)ENetFields_Private::PlayerSystemStatsArray].Property->GetFName()
		&& Name_ActiveFusionsArray == ClassReps[(int32)ENetFields_Private::ActiveFusionsArray].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in USigilReplicationManager"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilReplicationManager);
USigilReplicationManager::~USigilReplicationManager() {}
// ********** End Class USigilReplicationManager ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilReplicationData::StaticStruct, Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewStructOps, TEXT("SigilReplicationData"), &Z_Registration_Info_UScriptStruct_FSigilReplicationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilReplicationData), 1135390972U) },
		{ FSigilReplicationStats::StaticStruct, Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewStructOps, TEXT("SigilReplicationStats"), &Z_Registration_Info_UScriptStruct_FSigilReplicationStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilReplicationStats), 2192231724U) },
		{ FSigilFusionReplicationData::StaticStruct, Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewStructOps, TEXT("SigilFusionReplicationData"), &Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilFusionReplicationData), 4074950016U) },
		{ FSigilPlayerDataEntry::StaticStruct, Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewStructOps, TEXT("SigilPlayerDataEntry"), &Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilPlayerDataEntry), 2914415567U) },
		{ FSigilPlayerDataArray::StaticStruct, Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewStructOps, TEXT("SigilPlayerDataArray"), &Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilPlayerDataArray), 180768312U) },
		{ FSigilPlayerStatsEntry::StaticStruct, Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewStructOps, TEXT("SigilPlayerStatsEntry"), &Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilPlayerStatsEntry), 3618960821U) },
		{ FSigilPlayerStatsArray::StaticStruct, Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewStructOps, TEXT("SigilPlayerStatsArray"), &Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilPlayerStatsArray), 1239708595U) },
		{ FSigilActiveFusionsEntry::StaticStruct, Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewStructOps, TEXT("SigilActiveFusionsEntry"), &Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilActiveFusionsEntry), 2854786014U) },
		{ FSigilActiveFusionsArray::StaticStruct, Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewStructOps, TEXT("SigilActiveFusionsArray"), &Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilActiveFusionsArray), 1547376004U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilReplicationManager, USigilReplicationManager::StaticClass, TEXT("USigilReplicationManager"), &Z_Registration_Info_UClass_USigilReplicationManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilReplicationManager), 789363630U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_2804418242(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
