// SigilAbilities.h
// AURACRON - Sistema de Habilidades Exclusivas dos Sígilos
// Implementa as habilidades Murallion, Fracasso Prismal e Sopro de Fluxo
// Baseado nas APIs modernas do UE5.6 GameplayAbilities

#pragma once

#include "CoreMinimal.h"
#include "Abilities/GameplayAbility.h"
#include "GameplayTagContainer.h"
#include "GameplayEffect.h"
#include "AbilitySystemComponent.h"
#include "Abilities/Tasks/AbilityTask.h"
#include "Abilities/Tasks/AbilityTask_WaitGameplayEvent.h"
#include "Abilities/Tasks/AbilityTask_WaitTargetData.h"
#include "Abilities/Tasks/AbilityTask_PlayMontageAndWait.h"
#include "Engine/World.h"
#include "Components/SphereComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Sigils/SigilItem.h"
#include "Sigils/SigilAbilityEffects.h"
#include "SigilAbilities.generated.h"

// Forward Declarations
class ASigilItem;
class UNiagaraSystem;
class UGameplayEffect;
class AActor;
class APawn;

// ========================================
// GAMEPLAY TAGS PARA HABILIDADES
// ========================================
// Tags são definidas em SigilAbilityEffects.h/cpp para evitar redefinições
// Use SigilAbilityTags:: para acessar as tags

// ========================================
// CLASSE BASE PARA HABILIDADES DE SIGILO
// ========================================

/**
 * Classe base para todas as habilidades exclusivas de sígilos
 * Implementa funcionalidades comuns e integração com o sistema de sígilos
 */
UCLASS(BlueprintType, Blueprintable, Abstract)
class AURACRON_API USigilAbilityBase : public UGameplayAbility
{
    GENERATED_BODY()

public:
    USigilAbilityBase();

protected:
    /** Referência ao sigilo que possui esta habilidade */
    UPROPERTY(BlueprintReadOnly, Category = "Sigil")
    TWeakObjectPtr<ASigilItem> OwnerSigil;

    /** Subtipo de sigilo requerido para esta habilidade */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Requirements")
    ESigilSubType RequiredSubType = ESigilSubType::None;

    /** Raridade mínima do sigilo para usar esta habilidade */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Requirements")
    ESigilRarity MinimumRarity = ESigilRarity::Common;

    /** Sistema de partículas para a habilidade */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> AbilityVFX;

    /** Efeito de gameplay aplicado durante a habilidade */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    TSubclassOf<UGameplayEffect> AbilityEffect;

    /** Duração base da habilidade */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Timing")
    float BaseDuration = 3.0f;

    /** Cooldown base da habilidade */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Timing")
    float BaseCooldown = 30.0f;

    /** Custo de mana base */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Cost")
    float BaseManaeCost = 50.0f;

public:
    // Overrides de UGameplayAbility
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;
    virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const override;
    virtual void GetCooldownTimeRemainingAndDuration(FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, float& TimeRemaining, float& CooldownDuration) const override;

protected:
    /** Inicializa a habilidade com o sigilo proprietário */
    UFUNCTION(BlueprintCallable, Category = "Sigil")
    virtual void InitializeWithSigil(ASigilItem* Sigil);

    /** Verifica se o sigilo atende aos requisitos para esta habilidade */
    UFUNCTION(BlueprintPure, Category = "Validation")
    virtual bool ValidateSigilRequirements(ASigilItem* Sigil) const;

    /** Calcula a duração efetiva baseada na raridade do sigilo */
    UFUNCTION(BlueprintPure, Category = "Calculation")
    virtual float CalculateEffectiveDuration() const;

    /** Calcula o cooldown efetivo baseado na raridade do sigilo */
    UFUNCTION(BlueprintPure, Category = "Calculation")
    virtual float CalculateEffectiveCooldown() const;

    /** Spawna efeitos visuais da habilidade */
    UFUNCTION(BlueprintCallable, Category = "VFX")
    virtual void SpawnAbilityVFX(const FVector& Location, const FRotator& Rotation = FRotator::ZeroRotator);

    /** Aplica o efeito de gameplay da habilidade */
    UFUNCTION(BlueprintCallable, Category = "Effects")
    virtual FActiveGameplayEffectHandle ApplyAbilityEffect(AActor* Target, float Duration = -1.0f);
};

// ========================================
// HABILIDADE AEGIS - MURALLION
// ========================================

/**
 * Habilidade exclusiva do Aegis: "Murallion"
 * Cria uma barreira circular que dura 3 segundos
 * Fornece proteção para aliados dentro da área
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilAbility_Aegis_Murallion : public USigilAbilityBase
{
    GENERATED_BODY()

public:
    USigilAbility_Aegis_Murallion();

protected:
    /** Raio da barreira circular */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Barrier")
    float BarrierRadius = 500.0f;

    /** Altura da barreira */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Barrier")
    float BarrierHeight = 300.0f;

    /** Redução de dano fornecida pela barreira */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Barrier")
    float DamageReduction = 0.5f; // 50% de redução

    /** Efeito aplicado aos aliados dentro da barreira */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    TSubclassOf<UGameplayEffect> BarrierProtectionEffect;

    /** VFX da barreira */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> BarrierVFX;

private:
    /** Componente de colisão da barreira */
    UPROPERTY()
    TObjectPtr<USphereComponent> BarrierCollision;

    /** Componente VFX ativo da barreira */
    UPROPERTY()
    TObjectPtr<UNiagaraComponent> ActiveBarrierVFX;

    /** Aliados atualmente protegidos pela barreira */
    UPROPERTY()
    TArray<TWeakObjectPtr<AActor>> ProtectedAllies;

    /** Handles dos efeitos ativos */
    UPROPERTY()
    TArray<FActiveGameplayEffectHandle> ActiveProtectionEffects;

public:
    // Override da ativação
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;

protected:
    /** Cria a barreira física */
    UFUNCTION(BlueprintCallable, Category = "Barrier")
    void CreateBarrier(const FVector& Location);

    /** Remove a barreira */
    UFUNCTION(BlueprintCallable, Category = "Barrier")
    void RemoveBarrier();

    /** Detecta aliados que entram na barreira */
    UFUNCTION()
    void OnActorEnterBarrier(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    /** Detecta aliados que saem da barreira */
    UFUNCTION()
    void OnActorExitBarrier(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

    /** Aplica proteção a um aliado */
    UFUNCTION(BlueprintCallable, Category = "Protection")
    void ApplyProtectionToAlly(AActor* Ally);

    /** Remove proteção de um aliado */
    UFUNCTION(BlueprintCallable, Category = "Protection")
    void RemoveProtectionFromAlly(AActor* Ally);

    /** Verifica se um ator é aliado */
    UFUNCTION(BlueprintPure, Category = "Validation")
    bool IsAlly(AActor* Actor) const;
};

// ========================================
// HABILIDADE RUIN - FRACASSO PRISMAL
// ========================================

/**
 * Habilidade exclusiva do Ruin: "Fracasso Prismal"
 * Reseta parcialmente os cooldowns das habilidades
 * Aumenta temporariamente o dano causado
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilAbility_Ruin_FracassoPrismal : public USigilAbilityBase
{
    GENERATED_BODY()

public:
    USigilAbility_Ruin_FracassoPrismal();

protected:
    /** Percentual de redução dos cooldowns (0.0 a 1.0) */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Cooldown Reset")
    float CooldownReductionPercent = 0.5f; // 50% de redução

    /** Duração do buff de dano */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Damage Buff")
    float DamageBuffDuration = 8.0f;

    /** Multiplicador de dano durante o buff */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Damage Buff")
    float DamageMultiplier = 1.25f; // 25% de aumento

    /** Efeito de aumento de dano */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    TSubclassOf<UGameplayEffect> DamageBuffEffect;

    /** VFX do reset de cooldown */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> CooldownResetVFX;

    /** VFX do buff de dano */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> DamageBuffVFX;

private:
    /** Handle do efeito de buff de dano ativo */
    UPROPERTY()
    FActiveGameplayEffectHandle ActiveDamageBuffHandle;

public:
    // Override da ativação
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;

protected:
    /** Reseta os cooldowns das habilidades */
    UFUNCTION(BlueprintCallable, Category = "Cooldown")
    void ResetAbilityCooldowns();

    /** Aplica o buff de dano */
    UFUNCTION(BlueprintCallable, Category = "Damage")
    void ApplyDamageBuff();

    /** Remove o buff de dano */
    UFUNCTION(BlueprintCallable, Category = "Damage")
    void RemoveDamageBuff();

    /** Obtém todas as habilidades do jogador */
    TArray<FGameplayAbilitySpec*> GetPlayerAbilities() const;

    /** Calcula a redução efetiva de cooldown baseada na raridade */
    UFUNCTION(BlueprintPure, Category = "Calculation")
    float CalculateEffectiveCooldownReduction() const;
};

// ========================================
// HABILIDADE VESPER - SOPRO DE FLUXO
// ========================================

/**
 * Habilidade exclusiva do Vesper: "Sopro de Fluxo"
 * Realiza dash em direção a um aliado e aplica escudo
 * Fornece mobilidade e suporte defensivo
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilAbility_Vesper_SoproDeFluxo : public USigilAbilityBase
{
    GENERATED_BODY()

public:
    USigilAbility_Vesper_SoproDeFluxo();

protected:
    /** Alcance máximo do dash */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash")
    float MaxDashRange = 800.0f;

    /** Velocidade do dash */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash")
    float DashSpeed = 2000.0f;

    /** Valor do escudo aplicado */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Shield")
    float ShieldAmount = 200.0f;

    /** Duração do escudo */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Shield")
    float ShieldDuration = 5.0f;

    /** Raio de detecção de aliados */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Targeting")
    float AllyDetectionRadius = 100.0f;

    /** Efeito de escudo aplicado ao aliado */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    TSubclassOf<UGameplayEffect> ShieldEffect;

    /** VFX do dash */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> DashVFX;

    /** VFX do escudo */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> ShieldVFX;

private:
    /** Aliado alvo do dash */
    UPROPERTY()
    TWeakObjectPtr<AActor> TargetAlly;

    /** Handle do efeito de escudo ativo */
    UPROPERTY()
    FActiveGameplayEffectHandle ActiveShieldHandle;

    /** Está executando o dash */
    UPROPERTY()
    bool bIsDashing = false;

    /** Posição inicial do dash */
    UPROPERTY()
    FVector DashStartLocation;

    /** Posição alvo do dash */
    UPROPERTY()
    FVector DashTargetLocation;

    /** Timer para o movimento do dash */
    UPROPERTY()
    FTimerHandle DashTimerHandle;

public:
    // Override da ativação
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;

protected:
    /** Encontra o aliado mais próximo para o dash */
    UFUNCTION(BlueprintCallable, Category = "Targeting")
    AActor* FindNearestAlly();

    /** Inicia o dash em direção ao aliado */
    UFUNCTION(BlueprintCallable, Category = "Dash")
    void StartDashToAlly(AActor* Ally);

    /** Atualiza a posição durante o dash */
    UFUNCTION()
    void UpdateDashMovement();

    /** Finaliza o dash e aplica o escudo */
    UFUNCTION(BlueprintCallable, Category = "Dash")
    void CompleteDash();

    /** Aplica o escudo ao aliado */
    UFUNCTION(BlueprintCallable, Category = "Shield")
    void ApplyShieldToAlly(AActor* Ally);

    /** Calcula o valor efetivo do escudo baseado na raridade */
    UFUNCTION(BlueprintPure, Category = "Calculation")
    float CalculateEffectiveShieldAmount() const;

    /** Verifica se um ator é um aliado válido para o dash */
    UFUNCTION(BlueprintPure, Category = "Validation")
    bool IsValidAllyTarget(AActor* Actor) const;

    /** Calcula a posição final do dash próxima ao aliado */
    UFUNCTION(BlueprintPure, Category = "Calculation")
    FVector CalculateDashEndLocation(AActor* Ally) const;
};