// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Sigils/SigilItem.h"

#ifdef AURACRON_SigilItem_generated_h
#error "SigilItem.generated.h already included, missing '#pragma once' in SigilItem.h"
#endif
#define AURACRON_SigilItem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class ESigilRarity : uint8;
enum class ESigilState : uint8;
enum class ESigilType : uint8;
struct FGameplayTag;
struct FSigilData;

// ********** Begin ScriptStruct FSigilProperty ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_103_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilProperty_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilProperty;
// ********** End ScriptStruct FSigilProperty ******************************************************

// ********** Begin ScriptStruct FSigilSpectralBonus ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_133_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilSpectralBonus;
// ********** End ScriptStruct FSigilSpectralBonus *************************************************

// ********** Begin ScriptStruct FSigilData ********************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_161_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilData;
// ********** End ScriptStruct FSigilData **********************************************************

// ********** Begin Class ASigilItem ***************************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void MulticastUpdateRarityGlow_Implementation(); \
	virtual void MulticastPlayFusionVFX_Implementation(); \
	virtual void MulticastPlayEquipVFX_Implementation(); \
	virtual void ServerReforgeProperties_Implementation(); \
	virtual void ServerTriggerFusion_Implementation(); \
	virtual void ServerUnequipFromActor_Implementation(); \
	virtual void ServerEquipToActor_Implementation(AActor* TargetActor, int32 TargetSlotIndex); \
	DECLARE_FUNCTION(execOnRep_SigilData); \
	DECLARE_FUNCTION(execMulticastUpdateRarityGlow); \
	DECLARE_FUNCTION(execMulticastPlayFusionVFX); \
	DECLARE_FUNCTION(execMulticastPlayEquipVFX); \
	DECLARE_FUNCTION(execServerReforgeProperties); \
	DECLARE_FUNCTION(execServerTriggerFusion); \
	DECLARE_FUNCTION(execServerUnequipFromActor); \
	DECLARE_FUNCTION(execServerEquipToActor); \
	DECLARE_FUNCTION(execUpdateRarityGlow); \
	DECLARE_FUNCTION(execPlayAuraVFX); \
	DECLARE_FUNCTION(execPlayFusionVFX); \
	DECLARE_FUNCTION(execPlayEquipVFX); \
	DECLARE_FUNCTION(execGetExperienceToNextLevel); \
	DECLARE_FUNCTION(execLevelUp); \
	DECLARE_FUNCTION(execCanLevelUp); \
	DECLARE_FUNCTION(execAddExperience); \
	DECLARE_FUNCTION(execSetSigilState); \
	DECLARE_FUNCTION(execRegenerateSigilData); \
	DECLARE_FUNCTION(execSetOwningSlot); \
	DECLARE_FUNCTION(execCalculateSigilPower); \
	DECLARE_FUNCTION(execGetEffectiveBonus); \
	DECLARE_FUNCTION(execGetTotalSpectralPower); \
	DECLARE_FUNCTION(execIsFused); \
	DECLARE_FUNCTION(execIsEquipped); \
	DECLARE_FUNCTION(execGetSigilData); \
	DECLARE_FUNCTION(execGetSigilID); \
	DECLARE_FUNCTION(execGetSigilTag); \
	DECLARE_FUNCTION(execGetSigilState); \
	DECLARE_FUNCTION(execGetSigilRarity); \
	DECLARE_FUNCTION(execGetSigilType); \
	DECLARE_FUNCTION(execCanReforge); \
	DECLARE_FUNCTION(execReforgeProperties); \
	DECLARE_FUNCTION(execRemoveFusionEffects); \
	DECLARE_FUNCTION(execApplyFusionEffects); \
	DECLARE_FUNCTION(execCanFuse); \
	DECLARE_FUNCTION(execTriggerFusion); \
	DECLARE_FUNCTION(execCanUnequip); \
	DECLARE_FUNCTION(execCanEquipToActor); \
	DECLARE_FUNCTION(execUnequipFromActor); \
	DECLARE_FUNCTION(execEquipToActor);


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesASigilItem(); \
	friend struct Z_Construct_UClass_ASigilItem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister(); \
public: \
	DECLARE_CLASS2(ASigilItem, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_ASigilItem_NoRegister) \
	DECLARE_SERIALIZER(ASigilItem) \
	virtual UObject* _getUObject() const override { return const_cast<ASigilItem*>(this); } \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		SigilData=NETFIELD_REP_START, \
		SigilLevel, \
		ExperiencePoints, \
		EquippedOwner, \
		SlotIndex, \
		bIsEquipped, \
		bIsFused, \
		LastReforgeTime, \
		NETFIELD_REP_END=LastReforgeTime	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ASigilItem(ASigilItem&&) = delete; \
	ASigilItem(const ASigilItem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ASigilItem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ASigilItem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ASigilItem) \
	NO_API virtual ~ASigilItem();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_337_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_340_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ASigilItem;

// ********** End Class ASigilItem *****************************************************************

// ********** Begin Class USigilDataAsset **********************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilDataAsset_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_610_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilDataAsset(); \
	friend struct Z_Construct_UClass_USigilDataAsset_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilDataAsset_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilDataAsset, UDataAsset, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilDataAsset_NoRegister) \
	DECLARE_SERIALIZER(USigilDataAsset)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_610_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API USigilDataAsset(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilDataAsset(USigilDataAsset&&) = delete; \
	USigilDataAsset(const USigilDataAsset&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilDataAsset); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilDataAsset); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(USigilDataAsset) \
	NO_API virtual ~USigilDataAsset();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_607_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_610_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_610_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h_610_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilDataAsset;

// ********** End Class USigilDataAsset ************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h

// ********** Begin Enum ESigilType ****************************************************************
#define FOREACH_ENUM_ESIGILTYPE(op) \
	op(ESigilType::None) \
	op(ESigilType::Tank) \
	op(ESigilType::Damage) \
	op(ESigilType::Utility) 

enum class ESigilType : uint8;
template<> struct TIsUEnumClass<ESigilType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilType>();
// ********** End Enum ESigilType ******************************************************************

// ********** Begin Enum ESigilSubType *************************************************************
#define FOREACH_ENUM_ESIGILSUBTYPE(op) \
	op(ESigilSubType::None) \
	op(ESigilSubType::Aegis) \
	op(ESigilSubType::Ruin) \
	op(ESigilSubType::Vesper) 

enum class ESigilSubType : uint8;
template<> struct TIsUEnumClass<ESigilSubType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilSubType>();
// ********** End Enum ESigilSubType ***************************************************************

// ********** Begin Enum ESigilRarity **************************************************************
#define FOREACH_ENUM_ESIGILRARITY(op) \
	op(ESigilRarity::Common) \
	op(ESigilRarity::Rare) \
	op(ESigilRarity::Epic) \
	op(ESigilRarity::Legendary) 

enum class ESigilRarity : uint8;
template<> struct TIsUEnumClass<ESigilRarity> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilRarity>();
// ********** End Enum ESigilRarity ****************************************************************

// ********** Begin Enum ESigilState ***************************************************************
#define FOREACH_ENUM_ESIGILSTATE(op) \
	op(ESigilState::Unequipped) \
	op(ESigilState::Equipped) \
	op(ESigilState::Fused) \
	op(ESigilState::Locked) 

enum class ESigilState : uint8;
template<> struct TIsUEnumClass<ESigilState> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilState>();
// ********** End Enum ESigilState *****************************************************************

// ********** Begin Enum ESigilPropertyType ********************************************************
#define FOREACH_ENUM_ESIGILPROPERTYTYPE(op) \
	op(ESigilPropertyType::None) \
	op(ESigilPropertyType::Additive) \
	op(ESigilPropertyType::Multiplicative) \
	op(ESigilPropertyType::Override) 

enum class ESigilPropertyType : uint8;
template<> struct TIsUEnumClass<ESigilPropertyType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilPropertyType>();
// ********** End Enum ESigilPropertyType **********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
