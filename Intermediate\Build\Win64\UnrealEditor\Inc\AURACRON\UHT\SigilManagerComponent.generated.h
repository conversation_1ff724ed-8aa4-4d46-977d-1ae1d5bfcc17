// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Sigils/SigilManagerComponent.h"

#ifdef AURACRON_SigilManagerComponent_generated_h
#error "SigilManagerComponent.generated.h already included, missing '#pragma once' in SigilManagerComponent.h"
#endif
#define AURACRON_SigilManagerComponent_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ASigilItem;
enum class ESigilRarity : uint8;
enum class ESigilSubType : uint8;
enum class ESigilType : uint8;
struct FGameplayTag;
struct FSigilSystemStats;

// ********** Begin ScriptStruct FSigilSlotData ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilSlotData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilSlotData;
// ********** End ScriptStruct FSigilSlotData ******************************************************

// ********** Begin ScriptStruct FSigilFusionConfig ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_81_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilFusionConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilFusionConfig;
// ********** End ScriptStruct FSigilFusionConfig **************************************************

// ********** Begin ScriptStruct FSigilSystemStats *************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_129_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilSystemStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilSystemStats;
// ********** End ScriptStruct FSigilSystemStats ***************************************************

// ********** Begin Delegate FOnSigilEvent *********************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_170_DELEGATE \
AURACRON_API void FOnSigilEvent_DelegateWrapper(const FMulticastScriptDelegate& OnSigilEvent, ASigilItem* Sigil, int32 SlotIndex, FGameplayTag EventTag);


// ********** End Delegate FOnSigilEvent ***********************************************************

// ********** Begin Delegate FOnSigilFusion ********************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_171_DELEGATE \
AURACRON_API void FOnSigilFusion_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusion, ASigilItem* Sigil, float Multiplier);


// ********** End Delegate FOnSigilFusion **********************************************************

// ********** Begin Delegate FOnSigilSlotUnlocked **************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_172_DELEGATE \
AURACRON_API void FOnSigilSlotUnlocked_DelegateWrapper(const FMulticastScriptDelegate& OnSigilSlotUnlocked, int32 SlotIndex);


// ********** End Delegate FOnSigilSlotUnlocked ****************************************************

// ********** Begin Delegate FOnSigilStatsChanged **************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_173_DELEGATE \
AURACRON_API void FOnSigilStatsChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSigilStatsChanged, FSigilSystemStats const& NewStats);


// ********** End Delegate FOnSigilStatsChanged ****************************************************

// ********** Begin Delegate FOnExclusiveAbilityActivated ******************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_174_DELEGATE \
AURACRON_API void FOnExclusiveAbilityActivated_DelegateWrapper(const FMulticastScriptDelegate& OnExclusiveAbilityActivated, ESigilSubType SubType, float CooldownDuration);


// ********** End Delegate FOnExclusiveAbilityActivated ********************************************

// ********** Begin Delegate FOnExclusiveAbilityCooldownChanged ************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_175_DELEGATE \
AURACRON_API void FOnExclusiveAbilityCooldownChanged_DelegateWrapper(const FMulticastScriptDelegate& OnExclusiveAbilityCooldownChanged, ESigilSubType SubType);


// ********** End Delegate FOnExclusiveAbilityCooldownChanged **************************************

// ********** Begin Class USigilManagerComponent ***************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void MulticastPlayVFX_Implementation(int32 SlotIndex, FGameplayTag VFXTag); \
	virtual void MulticastNotifyFusion_Implementation(int32 SlotIndex, float Multiplier); \
	virtual bool ServerUnequipSigil_Validate(int32 ); \
	virtual void ServerUnequipSigil_Implementation(int32 SlotIndex); \
	virtual bool ServerEquipSigil_Validate(ASigilItem* , int32 ); \
	virtual void ServerEquipSigil_Implementation(ASigilItem* Sigil, int32 SlotIndex); \
	virtual bool ServerForceFusion_Validate(int32 ); \
	virtual void ServerForceFusion_Implementation(int32 SlotIndex); \
	virtual void MulticastPlayExclusiveAbilityVFX_Implementation(ESigilSubType SubType, FVector Location); \
	virtual bool ServerActivateExclusiveAbility_Validate(ESigilSubType ); \
	virtual void ServerActivateExclusiveAbility_Implementation(ESigilSubType SubType); \
	virtual bool ServerReforge_Validate(int32 ); \
	virtual void ServerReforge_Implementation(int32 SlotIndex); \
	DECLARE_FUNCTION(execMulticastPlayVFX); \
	DECLARE_FUNCTION(execMulticastNotifyFusion); \
	DECLARE_FUNCTION(execOnRep_SystemStats); \
	DECLARE_FUNCTION(execOnRep_SigilSlots); \
	DECLARE_FUNCTION(execServerUnequipSigil); \
	DECLARE_FUNCTION(execServerEquipSigil); \
	DECLARE_FUNCTION(execServerForceFusion); \
	DECLARE_FUNCTION(execDEBUG_EquipRandomSigil); \
	DECLARE_FUNCTION(execDEBUG_PrintSystemInfo); \
	DECLARE_FUNCTION(execDEBUG_ResetSystem); \
	DECLARE_FUNCTION(execDEBUG_ForceAllFusions); \
	DECLARE_FUNCTION(execDEBUG_UnlockAllSlots); \
	DECLARE_FUNCTION(execIsSystemActive); \
	DECLARE_FUNCTION(execIsValidSlotIndex); \
	DECLARE_FUNCTION(execCanEquipSigil); \
	DECLARE_FUNCTION(execMulticastPlayExclusiveAbilityVFX); \
	DECLARE_FUNCTION(execServerActivateExclusiveAbility); \
	DECLARE_FUNCTION(execDEBUG_ForceActivateExclusiveAbility); \
	DECLARE_FUNCTION(execGetAvailableExclusiveAbilities); \
	DECLARE_FUNCTION(execGetExclusiveAbilityCooldown); \
	DECLARE_FUNCTION(execCanActivateExclusiveAbility); \
	DECLARE_FUNCTION(execActivateExclusiveAbility); \
	DECLARE_FUNCTION(execGetSigilByID); \
	DECLARE_FUNCTION(execGetSystemStatistics); \
	DECLARE_FUNCTION(execConsumeReforgeResources); \
	DECLARE_FUNCTION(execCanAffordReforge); \
	DECLARE_FUNCTION(execHasCompatibleSigilsForFusion); \
	DECLARE_FUNCTION(execTriggerFusionForSigil); \
	DECLARE_FUNCTION(execIsSlotAvailable); \
	DECLARE_FUNCTION(execHasAvailableSlots); \
	DECLARE_FUNCTION(execCalculateTotalSigilPower); \
	DECLARE_FUNCTION(execGetSigilsByRarity); \
	DECLARE_FUNCTION(execGetSigilsByType); \
	DECLARE_FUNCTION(execGetAllEquippedSigils); \
	DECLARE_FUNCTION(execGetReforgeTimeRemaining); \
	DECLARE_FUNCTION(execCanReforge); \
	DECLARE_FUNCTION(execGetFusionProgress); \
	DECLARE_FUNCTION(execGetTimeToFusion); \
	DECLARE_FUNCTION(execIsSigilReadyForFusion); \
	DECLARE_FUNCTION(execIsSlotUnlocked); \
	DECLARE_FUNCTION(execGetEquippedSigil); \
	DECLARE_FUNCTION(execUnlockSigilSlot); \
	DECLARE_FUNCTION(execReforgeSigil); \
	DECLARE_FUNCTION(execForceFuseSigil); \
	DECLARE_FUNCTION(execSwapSigils); \
	DECLARE_FUNCTION(execServerReforge); \
	DECLARE_FUNCTION(execUnequipSigil); \
	DECLARE_FUNCTION(execEquipSigil);


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_USigilManagerComponent_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilManagerComponent(); \
	friend struct Z_Construct_UClass_USigilManagerComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilManagerComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilManagerComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilManagerComponent_NoRegister) \
	DECLARE_SERIALIZER(USigilManagerComponent) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		SigilSlots=NETFIELD_REP_START, \
		SystemStats, \
		LastReforgeTimestamp, \
		bSystemActive, \
		NETFIELD_REP_END=bSystemActive	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilManagerComponent(USigilManagerComponent&&) = delete; \
	USigilManagerComponent(const USigilManagerComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilManagerComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilManagerComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilManagerComponent) \
	NO_API virtual ~USigilManagerComponent();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_182_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h_185_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilManagerComponent;

// ********** End Class USigilManagerComponent *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
