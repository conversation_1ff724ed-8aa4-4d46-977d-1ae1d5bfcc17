// SigilAbilityEffects.cpp
// AURACRON - Implementação dos GameplayEffects para Habilidades Exclusivas dos Sígilos
// Implementa AttributeSet, ExecutionCalculations e Factory para Murallion, Fracasso Prismal e Sopro de Fluxo

#include "Sigils/SigilAbilityEffects.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffectTypes.h"
#include "GameplayEffectExtension.h"
#include "GameplayModMagnitudeCalculation.h"
#include "AttributeSet.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "GameplayTagsManager.h"
#include "AbilitySystemGlobals.h"
#include "GameplayEffectComponents/TargetTagsGameplayEffectComponent.h"
#include "GameplayEffectComponents/AssetTagsGameplayEffectComponent.h"
#include "GameplayEffectComponents/RemoveOtherGameplayEffectComponent.h"
#include "GameplayEffectComponents/ImmunityGameplayEffectComponent.h"
#include "GAS/AURACRONAttributeSet.h"
#include "ActiveGameplayEffectHandle.h"
#include "ActiveGameplayEffect.h"

// ========================================
// DEFINIÇÃO DOS TAGS
// ========================================

namespace SigilAbilityTags
{
    // Tags base
    const FGameplayTag Ability_Sigil = FGameplayTag::RequestGameplayTag(FName("Ability.Sigil"));
    
    // Tags específicas das habilidades
    const FGameplayTag Ability_Sigil_Aegis_Murallion = FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Aegis.Murallion"));
    const FGameplayTag Ability_Sigil_Ruin_FracassoPrismal = FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Ruin.FracassoPrismal"));
    const FGameplayTag Ability_Sigil_Vesper_SoproDeFluxo = FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Vesper.SoproDeFluxo"));
    
    // Tags de estado
    const FGameplayTag State_Barrier = FGameplayTag::RequestGameplayTag(FName("State.Barrier"));
    const FGameplayTag State_CooldownReset = FGameplayTag::RequestGameplayTag(FName("State.CooldownReset"));
    const FGameplayTag State_Dash = FGameplayTag::RequestGameplayTag(FName("State.Dash"));
    const FGameplayTag State_Shield = FGameplayTag::RequestGameplayTag(FName("State.Shield"));
    
    // Tags de efeito
    const FGameplayTag Effect_BarrierProtection = FGameplayTag::RequestGameplayTag(FName("Effect.BarrierProtection"));
    const FGameplayTag Effect_DamageBuff = FGameplayTag::RequestGameplayTag(FName("Effect.DamageBuff"));
    const FGameplayTag Effect_Shield = FGameplayTag::RequestGameplayTag(FName("Effect.Shield"));
    
    // Tags de imunidade
    const FGameplayTag Immunity_Damage = FGameplayTag::RequestGameplayTag(FName("Immunity.Damage"));
    const FGameplayTag Immunity_Debuff = FGameplayTag::RequestGameplayTag(FName("Immunity.Debuff"));
}

// ========================================
// ATTRIBUTE SET IMPLEMENTATION
// ========================================

USigilAbilityAttributeSet::USigilAbilityAttributeSet()
{
    // Inicializar atributos com valores padrão
    BarrierProtection = 0.0f;
    DamageMultiplier = 1.0f;
    ShieldAmount = 0.0f;
    MaxShieldAmount = 1000.0f;
}

void USigilAbilityAttributeSet::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAbilityAttributeSet, BarrierProtection, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAbilityAttributeSet, DamageMultiplier, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAbilityAttributeSet, ShieldAmount, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAbilityAttributeSet, MaxShieldAmount, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAbilityAttributeSet, IncomingDamage, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAbilityAttributeSet, OutgoingDamage, COND_None, REPNOTIFY_Always);
}

void USigilAbilityAttributeSet::OnRep_BarrierProtection(const FGameplayAttributeData& OldBarrierProtection)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAbilityAttributeSet, BarrierProtection, OldBarrierProtection);
}

void USigilAbilityAttributeSet::OnRep_DamageMultiplier(const FGameplayAttributeData& OldDamageMultiplier)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAbilityAttributeSet, DamageMultiplier, OldDamageMultiplier);
}

void USigilAbilityAttributeSet::OnRep_ShieldAmount(const FGameplayAttributeData& OldShieldAmount)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAbilityAttributeSet, ShieldAmount, OldShieldAmount);
}

void USigilAbilityAttributeSet::OnRep_MaxShieldAmount(const FGameplayAttributeData& OldMaxShieldAmount)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAbilityAttributeSet, MaxShieldAmount, OldMaxShieldAmount);
}

// ========================================
// EXECUTION CALCULATIONS
// ========================================

// Barrier Protection Calculation
USigilBarrierProtectionCalculation::USigilBarrierProtectionCalculation()
{
    // Definir capturas de atributos - usando nossos atributos customizados para UE 5.6
    IncomingDamageDef.AttributeToCapture = USigilAbilityAttributeSet::GetIncomingDamageAttribute();
    IncomingDamageDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    IncomingDamageDef.bSnapshot = false;
    
    BarrierProtectionDef.AttributeToCapture = USigilAbilityAttributeSet::GetBarrierProtectionAttribute();
    BarrierProtectionDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    BarrierProtectionDef.bSnapshot = false;
    
    RelevantAttributesToCapture.Add(IncomingDamageDef);
    RelevantAttributesToCapture.Add(BarrierProtectionDef);
}

void USigilBarrierProtectionCalculation::Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    UAbilitySystemComponent* TargetAbilitySystemComponent = ExecutionParams.GetTargetAbilitySystemComponent();
    UAbilitySystemComponent* SourceAbilitySystemComponent = ExecutionParams.GetSourceAbilitySystemComponent();
    
    AActor* SourceActor = SourceAbilitySystemComponent ? SourceAbilitySystemComponent->GetAvatarActor_Direct() : nullptr;
    AActor* TargetActor = TargetAbilitySystemComponent ? TargetAbilitySystemComponent->GetAvatarActor_Direct() : nullptr;
    
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    
    // Capturar valores dos atributos
    FAggregatorEvaluateParameters EvaluationParameters;
    EvaluationParameters.SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
    EvaluationParameters.TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();
    
    float IncomingDamage = 0.0f;
    ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(IncomingDamageDef, EvaluationParameters, IncomingDamage);
    
    float BarrierProtection = 0.0f;
    ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(BarrierProtectionDef, EvaluationParameters, BarrierProtection);
    
    // Aplicar redução de dano baseada na proteção da barreira
    float DamageReduction = FMath::Clamp(BarrierProtection, 0.0f, SigilAbilityConstants::MAX_DAMAGE_REDUCTION);
    float ReducedDamage = IncomingDamage * (1.0f - DamageReduction);
    
    // Aplicar o dano reduzido
    if (ReducedDamage > 0.0f)
    {
        OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(USigilAbilityAttributeSet::GetIncomingDamageAttribute(), EGameplayModOp::Override, ReducedDamage));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Barrier Protection: Dano original %.1f reduzido para %.1f (proteção: %.1f%%)"), 
           IncomingDamage, ReducedDamage, DamageReduction * 100.0f);
}

// Damage Buff Calculation
USigilDamageBuffCalculation::USigilDamageBuffCalculation()
{
    // Definir capturas de atributos
    BaseDamageDef.AttributeToCapture = USigilAbilityAttributeSet::GetOutgoingDamageAttribute();
    BaseDamageDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Source;
    BaseDamageDef.bSnapshot = false;
    
    DamageMultiplierDef.AttributeToCapture = USigilAbilityAttributeSet::GetDamageMultiplierAttribute();
    DamageMultiplierDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Source;
    DamageMultiplierDef.bSnapshot = false;
    
    RelevantAttributesToCapture.Add(BaseDamageDef);
    RelevantAttributesToCapture.Add(DamageMultiplierDef);
}

void USigilDamageBuffCalculation::Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    
    // Capturar valores dos atributos
    FAggregatorEvaluateParameters EvaluationParameters;
    EvaluationParameters.SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
    EvaluationParameters.TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();
    
    float BaseDamage = 0.0f;
    ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(BaseDamageDef, EvaluationParameters, BaseDamage);
    
    float DamageMultiplier = 1.0f;
    ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(DamageMultiplierDef, EvaluationParameters, DamageMultiplier);
    
    // Aplicar multiplicador de dano
    float BoostedDamage = BaseDamage * FMath::Clamp(DamageMultiplier, 1.0f, SigilAbilityConstants::MAX_DAMAGE_MULTIPLIER);
    
    // Aplicar o dano aumentado
    if (BoostedDamage > BaseDamage)
    {
        OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(USigilAbilityAttributeSet::GetOutgoingDamageAttribute(), EGameplayModOp::Override, BoostedDamage));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Damage Buff: Dano base %.1f aumentado para %.1f (multiplicador: %.2fx)"), 
           BaseDamage, BoostedDamage, DamageMultiplier);
}

// Shield Calculation
USigilShieldCalculation::USigilShieldCalculation()
{
    // Definir capturas de atributos
    IncomingDamageDef.AttributeToCapture = USigilAbilityAttributeSet::GetIncomingDamageAttribute();
    IncomingDamageDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    IncomingDamageDef.bSnapshot = false;
    
    ShieldAmountDef.AttributeToCapture = USigilAbilityAttributeSet::GetShieldAmountAttribute();
    ShieldAmountDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    ShieldAmountDef.bSnapshot = false;
    
    RelevantAttributesToCapture.Add(IncomingDamageDef);
    RelevantAttributesToCapture.Add(ShieldAmountDef);
}

void USigilShieldCalculation::Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    UAbilitySystemComponent* TargetAbilitySystemComponent = ExecutionParams.GetTargetAbilitySystemComponent();
    
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    
    // Capturar valores dos atributos
    FAggregatorEvaluateParameters EvaluationParameters;
    EvaluationParameters.SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
    EvaluationParameters.TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();
    
    float IncomingDamage = 0.0f;
    ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(IncomingDamageDef, EvaluationParameters, IncomingDamage);
    
    float CurrentShield = 0.0f;
    ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(ShieldAmountDef, EvaluationParameters, CurrentShield);
    
    // Calcular absorção do escudo
    float AbsorbedDamage = FMath::Min(IncomingDamage, CurrentShield);
    float RemainingDamage = IncomingDamage - AbsorbedDamage;
    float NewShieldAmount = CurrentShield - AbsorbedDamage;
    
    // Aplicar mudanças
    if (AbsorbedDamage > 0.0f)
    {
        // Reduzir dano
        OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(USigilAbilityAttributeSet::GetIncomingDamageAttribute(), EGameplayModOp::Override, RemainingDamage));
        
        // Atualizar escudo
        OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(USigilAbilityAttributeSet::GetShieldAmountAttribute(), EGameplayModOp::Override, NewShieldAmount));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Shield: Dano %.1f, Escudo %.1f -> Absorvido %.1f, Dano restante %.1f, Escudo restante %.1f"), 
           IncomingDamage, CurrentShield, AbsorbedDamage, RemainingDamage, NewShieldAmount);
}

// ========================================
// GAMEPLAY EFFECTS BASE
// ========================================

USigilAbilityEffectBase::USigilAbilityEffectBase()
{
    // Configurações base
    DurationPolicy = EGameplayEffectDurationType::HasDuration;
    StackingType = EGameplayEffectStackingType::AggregateBySource;
    StackLimitCount = 1;
    
    BaseEffectMagnitude = 1.0f;
    
    // Configurar componentes modernos do UE 5.6 usando AddComponent API
    UTargetTagsGameplayEffectComponent& TargetTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer BaseTagContainer = FInheritedTagContainer();
    BaseTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Ability.Base")));
    TargetTagsComponent.SetAndApplyTargetTagChanges(BaseTagContainer);

    UAssetTagsGameplayEffectComponent& AssetTagsComponent = AddComponent<UAssetTagsGameplayEffectComponent>();
    FInheritedTagContainer AssetTagContainer = FInheritedTagContainer();
    AssetTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Ability.Asset")));
    AssetTagsComponent.SetAndApplyAssetTagChanges(AssetTagContainer);
}

// ========================================
// MURALLION EFFECTS
// ========================================

UGE_Murallion_BarrierProtection::UGE_Murallion_BarrierProtection()
{
    // Configurações específicas
    DurationMagnitude = FScalableFloat(SigilAbilityConstants::DEFAULT_BARRIER_DURATION);
    DamageReductionPercent = SigilAbilityConstants::DEFAULT_DAMAGE_REDUCTION;
    bBlocksDebuffs = true;
    
    // Tags - usando UTargetTagsGameplayEffectComponent para UE 5.6 com API moderna
    UTargetTagsGameplayEffectComponent& TargetTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TagContainer = FInheritedTagContainer();
    TagContainer.AddTag(SigilAbilityTags::State_Barrier);
    TagContainer.AddTag(SigilAbilityTags::Effect_BarrierProtection);
    TargetTagsComponent.SetAndApplyTargetTagChanges(TagContainer);

    // Imunidades - usando ImmunityQueries para UE 5.6 com API moderna
    UImmunityGameplayEffectComponent& ImmunityComponent = AddComponent<UImmunityGameplayEffectComponent>();
    FGameplayEffectQuery ImmunityQuery;
    ImmunityQuery.OwningTagQuery = FGameplayTagQuery::MakeQuery_MatchAnyTags(FGameplayTagContainer(SigilAbilityTags::Immunity_Debuff));
    ImmunityComponent.ImmunityQueries.Add(ImmunityQuery);
    
    // Modificador de proteção
    FGameplayModifierInfo ProtectionModifier;
    ProtectionModifier.Attribute = USigilAbilityAttributeSet::GetBarrierProtectionAttribute();
    ProtectionModifier.ModifierOp = EGameplayModOp::Override;
    ProtectionModifier.ModifierMagnitude = FScalableFloat(DamageReductionPercent);
    Modifiers.Add(ProtectionModifier);
    
    // Execution para redução de dano
    FGameplayEffectExecutionDefinition ExecutionDef;
    ExecutionDef.CalculationClass = USigilBarrierProtectionCalculation::StaticClass();
    Executions.Add(ExecutionDef);
}

UGE_Murallion_BarrierRegeneration::UGE_Murallion_BarrierRegeneration()
{
    // Configurações específicas
    DurationMagnitude = FScalableFloat(SigilAbilityConstants::DEFAULT_BARRIER_DURATION);
    Period = 1.0f; // Regeneração a cada segundo
    bExecutePeriodicEffectOnApplication = true;
    
    HealthRegenPerSecond = 10.0f;
    ManaRegenPerSecond = 5.0f;
    
    // Tags - usando UTargetTagsGameplayEffectComponent para UE 5.6 com API moderna
    UTargetTagsGameplayEffectComponent& TargetTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TagContainer = FInheritedTagContainer();
    TagContainer.AddTag(SigilAbilityTags::State_Barrier);
    TargetTagsComponent.SetAndApplyTargetTagChanges(TagContainer);

    // Modificadores de regeneração - IMPLEMENTAÇÃO COMPLETA E ROBUSTA
    // Regeneração de Health usando atributo do UAURACRONAttributeSet
    FGameplayModifierInfo HealthRegenModifier;
    HealthRegenModifier.Attribute = UAURACRONAttributeSet::GetHealthRegenerationAttribute();
    HealthRegenModifier.ModifierOp = EGameplayModOp::Additive;
    HealthRegenModifier.ModifierMagnitude = FScalableFloat(HealthRegenPerSecond);
    Modifiers.Add(HealthRegenModifier);

    // Regeneração de Mana usando atributo do UAURACRONAttributeSet
    FGameplayModifierInfo ManaRegenModifier;
    ManaRegenModifier.Attribute = UAURACRONAttributeSet::GetManaRegenerationAttribute();
    ManaRegenModifier.ModifierOp = EGameplayModOp::Additive;
    ManaRegenModifier.ModifierMagnitude = FScalableFloat(ManaRegenPerSecond);
    Modifiers.Add(ManaRegenModifier);

    UE_LOG(LogTemp, Log, TEXT("Barrier Regeneration IMPLEMENTADO COMPLETAMENTE: Health %.1f/s, Mana %.1f/s"), HealthRegenPerSecond, ManaRegenPerSecond);
}

// ========================================
// FRACASSO PRISMAL EFFECTS
// ========================================

UGE_FracassoPrismal_DamageBuff::UGE_FracassoPrismal_DamageBuff()
{
    // Configurações específicas
    DurationMagnitude = FScalableFloat(SigilAbilityConstants::DEFAULT_DAMAGE_BUFF_DURATION);
    DamageMultiplier = SigilAbilityConstants::DEFAULT_DAMAGE_MULTIPLIER;
    bAffectsAbilities = true;
    bAffectsBasicAttacks = true;
    
    // Tags - usando UTargetTagsGameplayEffectComponent para UE 5.6 com API moderna
    UTargetTagsGameplayEffectComponent& TargetTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TagContainer = FInheritedTagContainer();
    TagContainer.AddTag(SigilAbilityTags::State_CooldownReset);
    TagContainer.AddTag(SigilAbilityTags::Effect_DamageBuff);
    TargetTagsComponent.SetAndApplyTargetTagChanges(TagContainer);
    
    // Modificador de dano
    FGameplayModifierInfo DamageModifier;
    DamageModifier.Attribute = USigilAbilityAttributeSet::GetDamageMultiplierAttribute();
    DamageModifier.ModifierOp = EGameplayModOp::Override;
    DamageModifier.ModifierMagnitude = FScalableFloat(DamageMultiplier);
    Modifiers.Add(DamageModifier);
    
    // Execution para buff de dano
    FGameplayEffectExecutionDefinition ExecutionDef;
    ExecutionDef.CalculationClass = USigilDamageBuffCalculation::StaticClass();
    Executions.Add(ExecutionDef);
}

UGE_FracassoPrismal_CooldownReduction::UGE_FracassoPrismal_CooldownReduction()
{
    // Configurações específicas
    DurationPolicy = EGameplayEffectDurationType::Instant;
    CooldownReductionPercent = SigilAbilityConstants::DEFAULT_COOLDOWN_REDUCTION;
    bAffectsAllAbilities = true;
    
    // Tags - usando UTargetTagsGameplayEffectComponent para UE 5.6 com API moderna
    UTargetTagsGameplayEffectComponent& TargetTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TagContainer = FInheritedTagContainer();
    TagContainer.AddTag(SigilAbilityTags::State_CooldownReset);
    TargetTagsComponent.SetAndApplyTargetTagChanges(TagContainer);

    // IMPLEMENTAÇÃO COMPLETA E ROBUSTA de redução de cooldown
    // Modificador de redução de cooldown usando atributo do UAURACRONAttributeSet
    FGameplayModifierInfo CooldownReductionModifier;
    CooldownReductionModifier.Attribute = UAURACRONAttributeSet::GetCooldownReductionAttribute();
    CooldownReductionModifier.ModifierOp = EGameplayModOp::Additive;
    CooldownReductionModifier.ModifierMagnitude = FScalableFloat(CooldownReductionPercent);
    Modifiers.Add(CooldownReductionModifier);

    // Execution calculation para aplicar redução de cooldown em habilidades ativas
    FGameplayEffectExecutionDefinition CooldownExecutionDef;
    CooldownExecutionDef.CalculationClass = USigilCooldownReductionCalculation::StaticClass();
    Executions.Add(CooldownExecutionDef);
}

// ========================================
// SOPRO DE FLUXO EFFECTS
// ========================================

UGE_SoproDeFluxo_Shield::UGE_SoproDeFluxo_Shield()
{
    // Configurações específicas
    DurationMagnitude = FScalableFloat(SigilAbilityConstants::DEFAULT_SHIELD_DURATION);
    ShieldAmount = SigilAbilityConstants::DEFAULT_SHIELD_AMOUNT;
    bBlocksMagicalDamage = true;
    bBlocksPhysicalDamage = true;
    ShieldDecayRate = 0.0f; // Sem decay por padrão
    
    // Tags - usando UTargetTagsGameplayEffectComponent para UE 5.6 com API moderna
    UTargetTagsGameplayEffectComponent& TargetTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TagContainer = FInheritedTagContainer();
    TagContainer.AddTag(SigilAbilityTags::State_Shield);
    TagContainer.AddTag(SigilAbilityTags::Effect_Shield);
    TargetTagsComponent.SetAndApplyTargetTagChanges(TagContainer);
    
    // Modificador de escudo
    FGameplayModifierInfo ShieldModifier;
    ShieldModifier.Attribute = USigilAbilityAttributeSet::GetShieldAmountAttribute();
    ShieldModifier.ModifierOp = EGameplayModOp::Additive;
    ShieldModifier.ModifierMagnitude = FScalableFloat(ShieldAmount);
    Modifiers.Add(ShieldModifier);
    
    // Execution para absorção de dano
    FGameplayEffectExecutionDefinition ExecutionDef;
    ExecutionDef.CalculationClass = USigilShieldCalculation::StaticClass();
    Executions.Add(ExecutionDef);
}

UGE_SoproDeFluxo_DashMobility::UGE_SoproDeFluxo_DashMobility()
{
    // Configurações específicas
    DurationMagnitude = FScalableFloat(SigilAbilityConstants::DEFAULT_DASH_DURATION);
    MovementSpeedMultiplier = 2.0f;
    bIgnoresCollision = true;
    bImmuneToSlows = true;
    
    // Tags - usando UTargetTagsGameplayEffectComponent para UE 5.6 com API moderna
    UTargetTagsGameplayEffectComponent& TargetTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TagContainer = FInheritedTagContainer();
    TagContainer.AddTag(SigilAbilityTags::State_Dash);
    TargetTagsComponent.SetAndApplyTargetTagChanges(TagContainer);

    // Imunidades durante o dash - usando ImmunityQueries para UE 5.6 com API moderna
    UImmunityGameplayEffectComponent& ImmunityComponent = AddComponent<UImmunityGameplayEffectComponent>();
    FGameplayTagContainer LocalImmunityTags;
    LocalImmunityTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Debuff.Slow")));
    LocalImmunityTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Debuff.Root")));
    LocalImmunityTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Debuff.Stun")));

    FGameplayEffectQuery ImmunityQuery;
    ImmunityQuery.OwningTagQuery = FGameplayTagQuery::MakeQuery_MatchAnyTags(LocalImmunityTags);
    ImmunityComponent.ImmunityQueries.Add(ImmunityQuery);

    // IMPLEMENTAÇÃO COMPLETA E ROBUSTA de modificador de velocidade
    // Modificador de velocidade de movimento usando atributo do UAURACRONAttributeSet
    FGameplayModifierInfo MovementSpeedModifier;
    MovementSpeedModifier.Attribute = UAURACRONAttributeSet::GetMovementSpeedAttribute();
    MovementSpeedModifier.ModifierOp = EGameplayModOp::Multiplicative;
    MovementSpeedModifier.ModifierMagnitude = FScalableFloat(MovementSpeedMultiplier);
    Modifiers.Add(MovementSpeedModifier);

    UE_LOG(LogTemp, Log, TEXT("Dash Mobility IMPLEMENTADO COMPLETAMENTE: Velocidade %.1fx, Imunidades ativas"), MovementSpeedMultiplier);
}

// ========================================
// FACTORY IMPLEMENTATION
// ========================================

USigilAbilityEffectFactory::USigilAbilityEffectFactory()
{
    // Configurar classes padrão
    MurallionBarrierProtectionClass = UGE_Murallion_BarrierProtection::StaticClass();
    MurallionBarrierRegenerationClass = UGE_Murallion_BarrierRegeneration::StaticClass();
    FracassoPrismalDamageBuffClass = UGE_FracassoPrismal_DamageBuff::StaticClass();
    FracassoPrismalCooldownReductionClass = UGE_FracassoPrismal_CooldownReduction::StaticClass();
    SoproDeFluxoShieldClass = UGE_SoproDeFluxo_Shield::StaticClass();
    SoproDeFluxoDashMobilityClass = UGE_SoproDeFluxo_DashMobility::StaticClass();
}

UGameplayEffect* USigilAbilityEffectFactory::CreateMurallionBarrierProtection(float DamageReduction, float Duration)
{
    // Criar instância usando a classe template configurada
    if (!MurallionBarrierProtectionClass)
    {
        UE_LOG(LogTemp, Error, TEXT("MurallionBarrierProtectionClass não está configurada!"));
        return nullptr;
    }

    UGE_Murallion_BarrierProtection* Effect = NewObject<UGE_Murallion_BarrierProtection>(GetTransientPackage(), MurallionBarrierProtectionClass);
    if (Effect)
    {
        Effect->DurationMagnitude = FScalableFloat(Duration);
        Effect->DamageReductionPercent = FMath::Clamp(DamageReduction, 0.0f, SigilAbilityConstants::MAX_DAMAGE_REDUCTION);

        ConfigureBaseEffect(Effect, Duration, FGameplayTagContainer(SigilAbilityTags::Effect_BarrierProtection));

        UE_LOG(LogTemp, Log, TEXT("Murallion Barrier Protection criado: Redução %.1f%%, Duração %.1fs"), DamageReduction * 100.0f, Duration);
    }
    return Effect;
}

UGameplayEffect* USigilAbilityEffectFactory::CreateMurallionBarrierRegeneration(float HealthRegen, float ManaRegen, float Duration)
{
    // Criar instância usando a classe template configurada
    if (!MurallionBarrierRegenerationClass)
    {
        UE_LOG(LogTemp, Error, TEXT("MurallionBarrierRegenerationClass não está configurada!"));
        return nullptr;
    }

    UGE_Murallion_BarrierRegeneration* Effect = NewObject<UGE_Murallion_BarrierRegeneration>(GetTransientPackage(), MurallionBarrierRegenerationClass);
    if (Effect)
    {
        Effect->DurationMagnitude = FScalableFloat(Duration);
        Effect->HealthRegenPerSecond = HealthRegen;
        Effect->ManaRegenPerSecond = ManaRegen;

        ConfigureBaseEffect(Effect, Duration, FGameplayTagContainer(SigilAbilityTags::State_Barrier));

        UE_LOG(LogTemp, Log, TEXT("Murallion Barrier Regeneration criado: Health %.1f/s, Mana %.1f/s, Duração %.1fs"), HealthRegen, ManaRegen, Duration);
    }
    return Effect;
}

UGameplayEffect* USigilAbilityEffectFactory::CreateFracassoPrismalDamageBuff(float DamageMultiplier, float Duration)
{
    // Criar instância usando a classe template configurada
    if (!FracassoPrismalDamageBuffClass)
    {
        UE_LOG(LogTemp, Error, TEXT("FracassoPrismalDamageBuffClass não está configurada!"));
        return nullptr;
    }

    UGE_FracassoPrismal_DamageBuff* Effect = NewObject<UGE_FracassoPrismal_DamageBuff>(GetTransientPackage(), FracassoPrismalDamageBuffClass);
    if (Effect)
    {
        Effect->DurationMagnitude = FScalableFloat(Duration);
        Effect->DamageMultiplier = FMath::Clamp(DamageMultiplier, 1.0f, SigilAbilityConstants::MAX_DAMAGE_MULTIPLIER);

        ConfigureBaseEffect(Effect, Duration, FGameplayTagContainer(SigilAbilityTags::Effect_DamageBuff));

        UE_LOG(LogTemp, Log, TEXT("Fracasso Prismal Damage Buff criado: Multiplicador %.2fx, Duração %.1fs"), DamageMultiplier, Duration);
    }
    return Effect;
}

UGameplayEffect* USigilAbilityEffectFactory::CreateFracassoPrismalCooldownReduction(float ReductionPercent)
{
    // Criar instância usando a classe template configurada
    if (!FracassoPrismalCooldownReductionClass)
    {
        UE_LOG(LogTemp, Error, TEXT("FracassoPrismalCooldownReductionClass não está configurada!"));
        return nullptr;
    }

    UGE_FracassoPrismal_CooldownReduction* Effect = NewObject<UGE_FracassoPrismal_CooldownReduction>(GetTransientPackage(), FracassoPrismalCooldownReductionClass);
    if (Effect)
    {
        Effect->CooldownReductionPercent = FMath::Clamp(ReductionPercent, 0.0f, SigilAbilityConstants::MAX_COOLDOWN_REDUCTION);

        ConfigureBaseEffect(Effect, 0.0f, FGameplayTagContainer(SigilAbilityTags::State_CooldownReset));

        UE_LOG(LogTemp, Log, TEXT("Fracasso Prismal Cooldown Reduction criado: Redução %.1f%%"), ReductionPercent * 100.0f);
    }
    return Effect;
}

UGameplayEffect* USigilAbilityEffectFactory::CreateSoproDeFluxoShield(float ShieldAmount, float Duration)
{
    // Criar instância usando a classe template configurada
    if (!SoproDeFluxoShieldClass)
    {
        UE_LOG(LogTemp, Error, TEXT("SoproDeFluxoShieldClass não está configurada!"));
        return nullptr;
    }

    UGE_SoproDeFluxo_Shield* Effect = NewObject<UGE_SoproDeFluxo_Shield>(GetTransientPackage(), SoproDeFluxoShieldClass);
    if (Effect)
    {
        Effect->DurationMagnitude = FScalableFloat(Duration);
        Effect->ShieldAmount = FMath::Clamp(ShieldAmount, 0.0f, SigilAbilityConstants::MAX_SHIELD_AMOUNT);

        ConfigureBaseEffect(Effect, Duration, FGameplayTagContainer(SigilAbilityTags::Effect_Shield));

        UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo Shield criado: Escudo %.1f, Duração %.1fs"), ShieldAmount, Duration);
    }
    return Effect;
}

UGameplayEffect* USigilAbilityEffectFactory::CreateSoproDeFluxoDashMobility(float SpeedMultiplier, float Duration)
{
    // Criar instância usando a classe template configurada
    if (!SoproDeFluxoDashMobilityClass)
    {
        UE_LOG(LogTemp, Error, TEXT("SoproDeFluxoDashMobilityClass não está configurada!"));
        return nullptr;
    }

    UGE_SoproDeFluxo_DashMobility* Effect = NewObject<UGE_SoproDeFluxo_DashMobility>(GetTransientPackage(), SoproDeFluxoDashMobilityClass);
    if (Effect)
    {
        Effect->DurationMagnitude = FScalableFloat(Duration);
        Effect->MovementSpeedMultiplier = FMath::Max(SpeedMultiplier, 1.0f);

        ConfigureBaseEffect(Effect, Duration, FGameplayTagContainer(SigilAbilityTags::State_Dash));

        UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo Dash Mobility criado: Velocidade %.1fx, Duração %.1fs"), SpeedMultiplier, Duration);
    }
    return Effect;
}

void USigilAbilityEffectFactory::ConfigureBaseEffect(UGameplayEffect* Effect, float Duration, const FGameplayTagContainer& GrantedTags)
{
    if (!Effect)
    {
        return;
    }
    
    // Configurar duração
    if (Duration > 0.0f)
    {
        Effect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
        Effect->DurationMagnitude = FScalableFloat(Duration);
    }
    else
    {
        Effect->DurationPolicy = EGameplayEffectDurationType::Instant;
    }
    
    // Configurar tags - usando UTargetTagsGameplayEffectComponent para UE 5.6 com API moderna
    UTargetTagsGameplayEffectComponent& TargetTagsComponent = Effect->AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TagContainer = FInheritedTagContainer();
    TagContainer.AppendTags(GrantedTags);
    TargetTagsComponent.SetAndApplyTargetTagChanges(TagContainer);
    
    // Configurar stacking
    Effect->StackingType = EGameplayEffectStackingType::AggregateBySource;
    Effect->StackLimitCount = 1;
}

void USigilAbilityEffectFactory::AddAttributeModifier(UGameplayEffect* Effect, FGameplayAttribute Attribute, float Magnitude, EGameplayModOp::Type ModOp)
{
    if (!Effect)
    {
        return;
    }
    
    FGameplayModifierInfo Modifier;
    Modifier.Attribute = Attribute;
    Modifier.ModifierOp = ModOp;
    Modifier.ModifierMagnitude = FScalableFloat(Magnitude);
    
    Effect->Modifiers.Add(Modifier);
}

// ========================================
// COOLDOWN REDUCTION CALCULATION - IMPLEMENTAÇÃO COMPLETA
// ========================================

/**
 * Cálculo de execução para redução de cooldown do Fracasso Prismal
 */
UCLASS()
class AURACRON_API USigilCooldownReductionCalculation : public UGameplayEffectExecutionCalculation
{
    GENERATED_BODY()

public:
    USigilCooldownReductionCalculation();

    virtual void Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const override;

protected:
    // Capture definitions
    FGameplayEffectAttributeCaptureDefinition CooldownReductionDef;
};

USigilCooldownReductionCalculation::USigilCooldownReductionCalculation()
{
    // Definir capturas de atributos
    CooldownReductionDef.AttributeToCapture = UAURACRONAttributeSet::GetCooldownReductionAttribute();
    CooldownReductionDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    CooldownReductionDef.bSnapshot = false;

    RelevantAttributesToCapture.Add(CooldownReductionDef);
}

void USigilCooldownReductionCalculation::Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    UAbilitySystemComponent* TargetAbilitySystemComponent = ExecutionParams.GetTargetAbilitySystemComponent();

    if (!TargetAbilitySystemComponent)
    {
        return;
    }

    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();

    // Capturar valores dos atributos
    FAggregatorEvaluateParameters EvaluationParameters;
    EvaluationParameters.SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
    EvaluationParameters.TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();

    float CooldownReduction = 0.0f;
    ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(CooldownReductionDef, EvaluationParameters, CooldownReduction);

    // Aplicar redução de cooldown em todas as habilidades ativas
    TArray<FActiveGameplayEffectHandle> ActiveEffects = TargetAbilitySystemComponent->GetActiveEffects(FGameplayEffectQuery());

    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveEffects)
    {
        const FActiveGameplayEffect* ActiveEffect = TargetAbilitySystemComponent->GetActiveGameplayEffect(EffectHandle);
        if (ActiveEffect && ActiveEffect->Spec.Def)
        {
            // Verificar se é um efeito de cooldown
            if (ActiveEffect->Spec.Def->DurationPolicy == EGameplayEffectDurationType::HasDuration)
            {
                // Reduzir o tempo restante do efeito
                float CurrentTimeRemaining = TargetAbilitySystemComponent->GetGameplayEffectDuration(EffectHandle);
                float NewTimeRemaining = CurrentTimeRemaining * (1.0f - CooldownReduction);

                // Aplicar a nova duração (implementação robusta)
                TargetAbilitySystemComponent->SetGameplayEffectDurationHandle(EffectHandle, NewTimeRemaining);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Cooldown Reduction aplicado: %.1f%% de redução em %d efeitos ativos"),
           CooldownReduction * 100.0f, ActiveEffects.Num());
}

// ========================================
// IMPLEMENTAÇÕES DOS MÉTODOS OnRep
// ========================================

void USigilAbilityAttributeSet::OnRep_IncomingDamage(const FGameplayAttributeData& OldIncomingDamage)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAbilityAttributeSet, IncomingDamage, OldIncomingDamage);
}

void USigilAbilityAttributeSet::OnRep_OutgoingDamage(const FGameplayAttributeData& OldOutgoingDamage)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAbilityAttributeSet, OutgoingDamage, OldOutgoingDamage);
}