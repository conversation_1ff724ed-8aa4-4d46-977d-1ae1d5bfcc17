// AURACRONPCGChaosIsland.cpp
// Implementação da classe AChaosIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGChaosIsland.h"
#include "GAS/AURACRONAttributeSet.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Components/PointLightComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"
#include "Kismet/KismetMathLibrary.h"

AChaosIsland::AChaosIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    ActivityLevel = 0.5f; // Nível médio por padrão

    // Inicializar propriedades específicas da Chaos Island
    EnvironmentalHazardIntensity = 1.0f;
    EnvironmentalHazardDuration = 10.0f;
    TerrainInstabilityIntensity = 1.0f;
    TerrainInstabilityDuration = 15.0f;
    HighRiskRewardMultiplier = 1.5f;
    HighRiskRewardDuration = 30.0f;
    VortexRotationSpeed = 45.0f;
    RunePulseIntensity = 1.0f;

    // Inicializar ambientes de transição
    TransitionEnvironments.Add(EAURACRONEnvironmentType::PurgatoryRealm);

    // Inicializar componentes de efeitos
    ChaosEnergyEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("ChaosEnergyEffect"));
    ChaosEnergyEffect->SetupAttachment(RootComponent);

    ChaosLight = CreateDefaultSubobject<UPointLightComponent>(TEXT("ChaosLight"));
    ChaosLight->SetupAttachment(RootComponent);
    ChaosLight->SetIntensity(1000.0f);
    ChaosLight->SetLightColor(FLinearColor::Red);
    
    // Configurar componentes específicos da Chaos Island
    
    // Espiral central do caos
    ChaosSpire = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ChaosSpire"));
    ChaosSpire->SetupAttachment(RootComponent);
    ChaosSpire->SetRelativeLocation(FVector(0.0f, 0.0f, 250.0f));
    ChaosSpire->SetRelativeScale3D(FVector(1.0f, 1.0f, 5.0f));
    ChaosSpire->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Vórtices de energia
    for (int32 i = 0; i < 3; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("EnergyVortex_%d"), i);
        UNiagaraComponent* EnergyVortex = CreateDefaultSubobject<UNiagaraComponent>(*ComponentName);
        EnergyVortex->SetupAttachment(RootComponent);
        
        // Posicionar em torno da espiral central
        float Angle = (float)i / 3.0f * 2.0f * PI;
        float Distance = 150.0f;
        FVector Position;
        Position.X = Distance * FMath::Cos(Angle);
        Position.Y = Distance * FMath::Sin(Angle);
        Position.Z = 100.0f + 50.0f * i; // Diferentes alturas
        
        EnergyVortex->SetRelativeLocation(Position);
        EnergyVortexes.Add(EnergyVortex);
    }
    
    // Runas antigas
    for (int32 i = 0; i < 5; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("AncientRune_%d"), i);
        UStaticMeshComponent* AncientRune = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
        AncientRune->SetupAttachment(RootComponent);
        
        // Posicionar em um padrão circular
        float Angle = (float)i / 5.0f * 2.0f * PI;
        float Distance = 250.0f;
        FVector Position;
        Position.X = Distance * FMath::Cos(Angle);
        Position.Y = Distance * FMath::Sin(Angle);
        Position.Z = 50.0f;
        
        AncientRune->SetRelativeLocation(Position);
        AncientRune->SetRelativeScale3D(FVector(0.5f, 0.5f, 2.0f));
        AncientRune->SetRelativeRotation(FRotator(0.0f, Angle * 180.0f / PI, 0.0f));
        AncientRune->SetCollisionProfileName(TEXT("BlockAll"));
        
        AncientRunes.Add(AncientRune);
    }
    
    // Definir o tipo de ilha como Chaos
    IslandType = EPrismalFlowIslandType::Chaos;

    // Sincronizar EnvironmentalHazards com HazardZones para compatibilidade
    EnvironmentalHazards = HazardZones;
}

void AChaosIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Implementar efeitos caóticos
    if (bIsActive)
    {
        // Rotação da espiral central
        if (ChaosSpire)
        {
            FRotator CurrentRotation = ChaosSpire->GetRelativeRotation();
            CurrentRotation.Yaw += DeltaTime * 20.0f; // 20 graus por segundo
            CurrentRotation.Roll += DeltaTime * 5.0f; // Leve inclinação
            ChaosSpire->SetRelativeRotation(CurrentRotation);
        }
        
        // Pulsar vórtices de energia
        float Time = GetGameTimeSinceCreation();
        for (int32 i = 0; i < EnergyVortexes.Num(); ++i)
        {
            if (EnergyVortexes[i])
            {
                // Cada vórtice pulsa em uma frequência ligeiramente diferente
                float PulseValue = 0.5f + 0.5f * FMath::Sin(Time * (1.5f + 0.5f * i));
                EnergyVortexes[i]->SetFloatParameter(FName("Intensity"), PulseValue * 3.0f);
                
                // Movimento caótico dos vórtices
                float OffsetX = 20.0f * FMath::Sin(Time * (0.7f + 0.3f * i));
                float OffsetY = 20.0f * FMath::Cos(Time * (0.5f + 0.4f * i));
                float OffsetZ = 10.0f * FMath::Sin(Time * (0.3f + 0.2f * i));
                
                FVector BaseLocation = EnergyVortexes[i]->GetRelativeLocation();
                FVector NewLocation = BaseLocation + FVector(OffsetX, OffsetY, OffsetZ);
                EnergyVortexes[i]->SetRelativeLocation(NewLocation);
            }
        }
        
        // Brilho das runas antigas
        for (int32 i = 0; i < AncientRunes.Num(); ++i)
        {
            if (AncientRunes[i])
            {
                // Obter material dinâmico
                UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(AncientRunes[i]->GetMaterial(0));
                if (!DynMaterial)
                {
                    DynMaterial = AncientRunes[i]->CreateAndSetMaterialInstanceDynamic(0);
                }
                
                if (DynMaterial)
                {
                    // Cada runa brilha em um padrão diferente
                    float GlowValue = 0.5f + 0.5f * FMath::Sin(Time * (0.2f + 0.1f * i));
                    DynMaterial->SetScalarParameterValue(FName("Glow"), GlowValue);
                }
            }
        }
    }
}

void AChaosIsland::ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OtherActor)
    {
        return;
    }
    
    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito visual de feedback
    for (UNiagaraComponent* Vortex : EnergyVortexes)
    {
        if (Vortex)
        {
            Vortex->SetFloatParameter(FName("EffectIntensity"), 5.0f); // Intensificar efeito
        }
    }
    
    // Retornar à intensidade normal após um curto período
    FTimerHandle TimerHandle;
    GetWorldTimerManager().SetTimer(TimerHandle, [this]()
    {
        for (UNiagaraComponent* Vortex : EnergyVortexes)
        {
            if (Vortex)
            {
                Vortex->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }
    }, 0.5f, false);
    
    // Aplicar efeito caótico ao jogador
    ApplyChaosEffect(OtherActor);
    
    // Agendar remoção do efeito após um tempo
    FTimerHandle RemoveEffectTimerHandle;
    GetWorldTimerManager().SetTimer(RemoveEffectTimerHandle, FTimerDelegate::CreateUObject(this, &AChaosIsland::RemoveChaosEffects, OtherActor), 10.0f, false);
}

void AChaosIsland::ApplyChaosEffect(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito caótico
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para efeito caótico
    UGameplayEffect* ChaosEffect = NewObject<UGameplayEffect>(this, FName("GE_ChaosIslandEffect"));
    ChaosEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
    ChaosEffect->DurationMagnitude = FScalableFloat(15.0f); // 15 segundos
    
    // Modificador para velocidade de movimento aleatória (50% - 150%)
    FGameplayModifierInfo SpeedModifier;
    SpeedModifier.ModifierMagnitude = FScalableFloat(FMath::RandRange(0.5f, 1.5f));
    SpeedModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
    // Buscar o atributo de velocidade de movimento do AttributeSet
    SpeedModifier.Attribute = UAURACRONAttributeSet::GetMovementSpeedAttribute();
    ChaosEffect->Modifiers.Add(SpeedModifier);
    
    // Modificador para dano crítico aleatório (75% - 200%)
    FGameplayModifierInfo CritModifier;
    CritModifier.ModifierMagnitude = FScalableFloat(FMath::RandRange(0.75f, 2.0f));
    CritModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
    // Buscar o atributo de chance crítica do AttributeSet
    CritModifier.Attribute = UAURACRONAttributeSet::GetCriticalChanceAttribute();
    ChaosEffect->Modifiers.Add(CritModifier);
    
    // Modificador para resistência caótica (0% - 50%)
    FGameplayModifierInfo ResistanceModifier;
    ResistanceModifier.ModifierMagnitude = FScalableFloat(FMath::RandRange(0.0f, 0.5f));
    ResistanceModifier.ModifierOp = EGameplayModOp::Additive;
    // Buscar o atributo de resistência mágica do AttributeSet
    ResistanceModifier.Attribute = UAURACRONAttributeSet::GetMagicResistanceAttribute();
    ChaosEffect->Modifiers.Add(ResistanceModifier);
    
    // Aplicar o efeito caótico
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(ChaosEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            // Obter valores das magnitudes usando a API correta
            float SpeedValue = 0.0f, CritValue = 0.0f, ResistanceValue = 0.0f;
            SpeedModifier.ModifierMagnitude.GetStaticMagnitudeIfPossible(1.0f, SpeedValue);
            CritModifier.ModifierMagnitude.GetStaticMagnitudeIfPossible(1.0f, CritValue);
            ResistanceModifier.ModifierMagnitude.GetStaticMagnitudeIfPossible(1.0f, ResistanceValue);

            UE_LOG(LogTemp, Log, TEXT("Chaos Island: Efeito caótico aplicado em %s (Velocidade: %.0f%%, Crítico: %.0f%%, Resistência: +%.0f%%)"),
                *TargetActor->GetName(), SpeedValue * 100, CritValue * 100, ResistanceValue * 100);
        }
    }
    
    // Criar feedback visual adicional
    UNiagaraSystem* ChaosVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ChaosIslandEffect"));
    if (ChaosVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            ChaosVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void AChaosIsland::RemoveChaosEffects(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Remover efeitos caóticos por tag
    FGameplayTagContainer ChaosEffectTags;
    ChaosEffectTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.ChaosIsland")));
    
    // Remover todos os efeitos com a tag de caos
    int32 RemovedEffects = AbilityComponent->RemoveActiveEffectsWithTags(ChaosEffectTags);
    
    if (RemovedEffects > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("Chaos Island: %d efeito(s) caótico(s) removido(s) de %s"), RemovedEffects, *TargetActor->GetName());
        
        // Criar feedback visual de remoção
        UNiagaraSystem* RemovalVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ChaosIslandRemoval"));
        if (RemovalVFX)
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                RemovalVFX,
                TargetActor->GetActorLocation(),
                FRotator::ZeroRotator,
                FVector(0.8f),
                true,
                true,
                ENCPoolMethod::AutoRelease
            );
        }
    }
    else
    {
        UE_LOG(LogTemp, Verbose, TEXT("Chaos Island: Nenhum efeito caótico encontrado para remover de %s"), *TargetActor->GetName());
    }
}

void AChaosIsland::SetActivityLevel(float NewActivityLevel)
{
    // Clampar o valor entre 0.0 e 1.0
    ActivityLevel = FMath::Clamp(NewActivityLevel, 0.0f, 1.0f);

    // Atualizar efeitos visuais baseados no nível de atividade
    if (ChaosSpire)
    {
        // Ajustar a escala da espiral baseada na atividade
        float ScaleMultiplier = 0.8f + (ActivityLevel * 0.4f); // Entre 0.8 e 1.2
        ChaosSpire->SetRelativeScale3D(FVector(ScaleMultiplier));
    }

    // Atualizar efeitos de partículas
    if (ChaosEnergyEffect)
    {
        ChaosEnergyEffect->SetFloatParameter(FName("ActivityLevel"), ActivityLevel);
        ChaosEnergyEffect->SetFloatParameter(FName("IntensityMultiplier"), ActivityLevel * 2.0f);
    }

    // Atualizar iluminação
    if (ChaosLight)
    {
        float LightIntensity = 500.0f + (ActivityLevel * 1500.0f); // Entre 500 e 2000
        ChaosLight->SetIntensity(LightIntensity);

        // Mudar cor baseada na atividade (vermelho mais intenso = mais ativo)
        FLinearColor LightColor = FLinearColor::Red;
        LightColor.R = 0.5f + (ActivityLevel * 0.5f); // Entre 0.5 e 1.0
        LightColor.G = 0.1f * (1.0f - ActivityLevel); // Menos verde quando mais ativo
        ChaosLight->SetLightColor(LightColor);
    }

    UE_LOG(LogTemp, Log, TEXT("Chaos Island: Nível de atividade definido para %.2f"), ActivityLevel);
}

// Implementação das funções ausentes usando APIs modernas do UE 5.6

void AChaosIsland::ApplyEnvironmentalHazardEffect(AActor* TargetActor)
{
    if (!TargetActor || !HasAuthority())
    {
        return;
    }

    // Buscar componente de sistema de habilidades usando APIs modernas do UE 5.6
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
    {
        if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
        {
            // Criar contexto do efeito
            FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);
            EffectContext.AddInstigator(this, this);

            // Aplicar efeito de perigo ambiental se disponível
            if (EnvironmentalHazardEffect)
            {
                FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(
                    EnvironmentalHazardEffect,
                    EnvironmentalHazardIntensity,
                    EffectContext
                );

                if (SpecHandle.IsValid())
                {
                    FActiveGameplayEffectHandle ActiveHandle = ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());

                    if (ActiveHandle.IsValid())
                    {
                        // Armazenar handle para remoção posterior
                        TWeakObjectPtr<AActor> ActorPtr(TargetActor);
                        if (!ActiveEnvironmentalHazardEffects.Contains(ActorPtr))
                        {
                            ActiveEnvironmentalHazardEffects.Add(ActorPtr, TArray<FActiveGameplayEffectHandle>());
                        }
                        ActiveEnvironmentalHazardEffects[ActorPtr].Add(ActiveHandle);

                        UE_LOG(LogTemp, Log, TEXT("AChaosIsland: Aplicado efeito de perigo ambiental em %s"), *TargetActor->GetName());
                    }
                }
            }

            // Aplicar dano por perigo ambiental usando GameplayEffect
            UGameplayEffect* HazardDamageEffect = NewObject<UGameplayEffect>(this, FName("GE_ChaosIslandHazardDamage"));
            HazardDamageEffect->DurationPolicy = EGameplayEffectDurationType::Instant;

            // Modificador de dano
            FGameplayModifierInfo DamageModifier;
            DamageModifier.ModifierMagnitude = FScalableFloat(EnvironmentalHazardIntensity * 25.0f); // Dano baseado na intensidade
            DamageModifier.ModifierOp = EGameplayModOp::Additive;
            DamageModifier.Attribute = UAURACRONAttributeSet::GetHealthAttribute();
            HazardDamageEffect->Modifiers.Add(DamageModifier);

            // Aplicar dano
            FGameplayEffectSpecHandle DamageSpecHandle = ASC->MakeOutgoingSpec(HazardDamageEffect->GetClass(), 1.0f, EffectContext);
            if (DamageSpecHandle.IsValid())
            {
                ASC->ApplyGameplayEffectSpecToSelf(*DamageSpecHandle.Data.Get());
            }

            // Efeito visual de perigo ambiental
            if (UNiagaraSystem* HazardVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_EnvironmentalHazard")))
            {
                UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    HazardVFX,
                    TargetActor->GetActorLocation(),
                    FRotator::ZeroRotator,
                    FVector(1.0f),
                    true,
                    true,
                    ENCPoolMethod::AutoRelease
                );
            }
        }
    }
}

void AChaosIsland::RemoveEnvironmentalHazardEffects(AActor* TargetActor)
{
    if (!TargetActor)
    {
        return;
    }

    TWeakObjectPtr<AActor> ActorPtr(TargetActor);

    // Remover efeitos ativos armazenados
    if (ActiveEnvironmentalHazardEffects.Contains(ActorPtr))
    {
        if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
        {
            if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
            {
                for (const FActiveGameplayEffectHandle& Handle : ActiveEnvironmentalHazardEffects[ActorPtr])
                {
                    if (Handle.IsValid())
                    {
                        ASC->RemoveActiveGameplayEffect(Handle);
                    }
                }
            }
        }

        ActiveEnvironmentalHazardEffects.Remove(ActorPtr);
        UE_LOG(LogTemp, Log, TEXT("AChaosIsland: Removidos efeitos de perigo ambiental de %s"), *TargetActor->GetName());
    }

    // Remover também por tags como fallback
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
    {
        if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
        {
            FGameplayTagContainer HazardTags;
            HazardTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.ChaosIsland.EnvironmentalHazard")));
            ASC->RemoveActiveEffectsWithTags(HazardTags);
        }
    }
}

void AChaosIsland::ApplyUnstableTerrainEffect(AActor* TargetActor)
{
    if (!TargetActor || !HasAuthority())
    {
        return;
    }

    // Buscar componente de sistema de habilidades usando APIs modernas do UE 5.6
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
    {
        if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
        {
            // Criar contexto do efeito
            FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);
            EffectContext.AddInstigator(this, this);

            // Aplicar efeito de terreno instável se disponível
            if (UnstableTerrainEffect)
            {
                FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(
                    UnstableTerrainEffect,
                    TerrainInstabilityIntensity,
                    EffectContext
                );

                if (SpecHandle.IsValid())
                {
                    FActiveGameplayEffectHandle ActiveHandle = ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());

                    if (ActiveHandle.IsValid())
                    {
                        // Armazenar handle para remoção posterior
                        TWeakObjectPtr<AActor> ActorPtr(TargetActor);
                        if (!ActiveUnstableTerrainEffects.Contains(ActorPtr))
                        {
                            ActiveUnstableTerrainEffects.Add(ActorPtr, TArray<FActiveGameplayEffectHandle>());
                        }
                        ActiveUnstableTerrainEffects[ActorPtr].Add(ActiveHandle);

                        UE_LOG(LogTemp, Log, TEXT("AChaosIsland: Aplicado efeito de terreno instável em %s"), *TargetActor->GetName());
                    }
                }
            }

            // Criar efeito de instabilidade de movimento
            UGameplayEffect* InstabilityEffect = NewObject<UGameplayEffect>(this, FName("GE_ChaosIslandTerrainInstability"));
            InstabilityEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
            InstabilityEffect->DurationMagnitude = FScalableFloat(8.0f); // 8 segundos

            // Modificador de velocidade de movimento (redução)
            FGameplayModifierInfo MovementModifier;
            MovementModifier.ModifierMagnitude = FScalableFloat(0.7f - (TerrainInstabilityIntensity * 0.3f)); // 70% a 40% da velocidade
            MovementModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            MovementModifier.Attribute = UAURACRONAttributeSet::GetMovementSpeedAttribute();
            InstabilityEffect->Modifiers.Add(MovementModifier);

            // Modificador de precisão (redução)
            FGameplayModifierInfo AccuracyModifier;
            AccuracyModifier.ModifierMagnitude = FScalableFloat(-TerrainInstabilityIntensity * 0.2f); // Redução de até 20%
            AccuracyModifier.ModifierOp = EGameplayModOp::Additive;
            AccuracyModifier.Attribute = UAURACRONAttributeSet::GetAccuracyAttribute();
            InstabilityEffect->Modifiers.Add(AccuracyModifier);

            // Aplicar efeito
            FGameplayEffectSpecHandle InstabilitySpecHandle = ASC->MakeOutgoingSpec(InstabilityEffect->GetClass(), 1.0f, EffectContext);
            if (InstabilitySpecHandle.IsValid())
            {
                ASC->ApplyGameplayEffectSpecToSelf(*InstabilitySpecHandle.Data.Get());
            }

            // Efeito visual de terreno instável
            if (UNiagaraSystem* TerrainVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_UnstableTerrain")))
            {
                UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    TerrainVFX,
                    TargetActor->GetActorLocation(),
                    FRotator::ZeroRotator,
                    FVector(1.5f),
                    true,
                    true,
                    ENCPoolMethod::AutoRelease
                );
            }
        }
    }
}

void AChaosIsland::RemoveUnstableTerrainEffects(AActor* TargetActor)
{
    if (!TargetActor)
    {
        return;
    }

    TWeakObjectPtr<AActor> ActorPtr(TargetActor);

    // Remover efeitos ativos armazenados
    if (ActiveUnstableTerrainEffects.Contains(ActorPtr))
    {
        if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
        {
            if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
            {
                for (const FActiveGameplayEffectHandle& Handle : ActiveUnstableTerrainEffects[ActorPtr])
                {
                    if (Handle.IsValid())
                    {
                        ASC->RemoveActiveGameplayEffect(Handle);
                    }
                }
            }
        }

        ActiveUnstableTerrainEffects.Remove(ActorPtr);
        UE_LOG(LogTemp, Log, TEXT("AChaosIsland: Removidos efeitos de terreno instável de %s"), *TargetActor->GetName());
    }

    // Remover também por tags como fallback
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
    {
        if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
        {
            FGameplayTagContainer TerrainTags;
            TerrainTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.ChaosIsland.UnstableTerrain")));
            ASC->RemoveActiveEffectsWithTags(TerrainTags);
        }
    }
}

void AChaosIsland::GrantHighRiskReward(AActor* TargetActor)
{
    if (!TargetActor || !HasAuthority())
    {
        return;
    }

    // Buscar componente de sistema de habilidades usando APIs modernas do UE 5.6
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
    {
        if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
        {
            // Criar contexto do efeito
            FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);
            EffectContext.AddInstigator(this, this);

            // Criar efeito de recompensa de alto risco
            UGameplayEffect* HighRiskRewardEffect = NewObject<UGameplayEffect>(this, FName("GE_ChaosIslandHighRiskReward"));
            HighRiskRewardEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
            HighRiskRewardEffect->DurationMagnitude = FScalableFloat(60.0f); // 60 segundos

            // Bônus de dano crítico significativo
            FGameplayModifierInfo CritDamageModifier;
            CritDamageModifier.ModifierMagnitude = FScalableFloat(HighRiskRewardMultiplier * 0.5f); // Até 50% mais dano crítico
            CritDamageModifier.ModifierOp = EGameplayModOp::Additive;
            CritDamageModifier.Attribute = UAURACRONAttributeSet::GetCriticalDamageAttribute();
            HighRiskRewardEffect->Modifiers.Add(CritDamageModifier);

            // Bônus de experiência
            FGameplayModifierInfo XPModifier;
            XPModifier.ModifierMagnitude = FScalableFloat(HighRiskRewardMultiplier); // Multiplicador de XP
            XPModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            XPModifier.Attribute = UAURACRONAttributeSet::GetExperienceMultiplierAttribute();
            HighRiskRewardEffect->Modifiers.Add(XPModifier);

            // Bônus de regeneração de mana
            FGameplayModifierInfo ManaRegenModifier;
            ManaRegenModifier.ModifierMagnitude = FScalableFloat(HighRiskRewardMultiplier * 2.0f); // Até 2x regeneração de mana
            ManaRegenModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            ManaRegenModifier.Attribute = UAURACRONAttributeSet::GetManaRegenerationAttribute();
            HighRiskRewardEffect->Modifiers.Add(ManaRegenModifier);

            // Aplicar efeito
            FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(HighRiskRewardEffect->GetClass(), 1.0f, EffectContext);
            if (SpecHandle.IsValid())
            {
                FActiveGameplayEffectHandle ActiveHandle = ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());

                if (ActiveHandle.IsValid())
                {
                    UE_LOG(LogTemp, Log, TEXT("AChaosIsland: Concedida recompensa de alto risco para %s (Multiplicador: %.2f)"),
                        *TargetActor->GetName(), HighRiskRewardMultiplier);

                    // Efeito visual de recompensa
                    if (UNiagaraSystem* RewardVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_HighRiskReward")))
                    {
                        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                            GetWorld(),
                            RewardVFX,
                            TargetActor->GetActorLocation(),
                            FRotator::ZeroRotator,
                            FVector(2.0f),
                            true,
                            true,
                            ENCPoolMethod::AutoRelease
                        );
                    }

                    // Som de recompensa
                    if (USoundBase* RewardSound = LoadObject<USoundBase>(nullptr, TEXT("/Game/Audio/Islands/SFX_HighRiskReward")))
                    {
                        UGameplayStatics::PlaySoundAtLocation(
                            GetWorld(),
                            RewardSound,
                            TargetActor->GetActorLocation(),
                            1.0f,
                            1.0f,
                            0.0f
                        );
                    }
                }
            }
        }
    }
}

void AChaosIsland::UpdateUnstableTerrain(float DeltaTime)
{
    if (!bIsActive)
    {
        return;
    }

    AccumulatedTime += DeltaTime;

    // Atualizar zonas de terreno instável
    for (int32 i = 0; i < UnstableTerrainZones.Num(); ++i)
    {
        if (UStaticMeshComponent* TerrainZone = UnstableTerrainZones[i])
        {
            // Movimento oscilatório do terreno
            float OscillationFreq = 0.5f + (TerrainInstabilityIntensity * 1.5f); // Frequência baseada na intensidade
            float OscillationAmplitude = 20.0f * TerrainInstabilityIntensity; // Amplitude baseada na intensidade

            float VerticalOffset = OscillationAmplitude * FMath::Sin(AccumulatedTime * OscillationFreq + i * 0.5f);

            FVector BaseLocation = TerrainZone->GetRelativeLocation();
            BaseLocation.Z = 50.0f + VerticalOffset; // Posição base + oscilação
            TerrainZone->SetRelativeLocation(BaseLocation);

            // Rotação instável
            FRotator CurrentRotation = TerrainZone->GetRelativeRotation();
            CurrentRotation.Roll = 10.0f * FMath::Sin(AccumulatedTime * (OscillationFreq * 0.7f) + i * 0.3f);
            CurrentRotation.Pitch = 5.0f * FMath::Cos(AccumulatedTime * (OscillationFreq * 0.9f) + i * 0.7f);
            TerrainZone->SetRelativeRotation(CurrentRotation);

            // Atualizar material dinâmico para mostrar instabilidade
            if (UMaterialInterface* BaseMaterial = TerrainZone->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = TerrainZone->CreateDynamicMaterialInstance(0, BaseMaterial);
                if (DynamicMaterial)
                {
                    float InstabilityValue = 0.5f + 0.5f * FMath::Sin(AccumulatedTime * 2.0f + i);
                    DynamicMaterial->SetScalarParameterValue(FName("InstabilityLevel"), InstabilityValue);
                    DynamicMaterial->SetScalarParameterValue(FName("CrackIntensity"), TerrainInstabilityIntensity);

                    // Cor baseada na intensidade (vermelho = mais instável)
                    FLinearColor InstabilityColor = UKismetMathLibrary::LinearColorLerp(
                        FLinearColor::Gray,
                        FLinearColor::Red,
                        TerrainInstabilityIntensity
                    );
                    DynamicMaterial->SetVectorParameterValue(FName("InstabilityColor"), InstabilityColor);
                }
            }
        }
    }

    // Verificar se deve aplicar efeitos de terreno instável em atores próximos
    if (FMath::Fmod(AccumulatedTime, 2.0f) < DeltaTime) // A cada 2 segundos
    {
        TArray<AActor*> OverlappingActors;
        GetOverlappingActors(OverlappingActors, ACharacter::StaticClass());

        for (AActor* Actor : OverlappingActors)
        {
            if (Actor && IsValid(Actor))
            {
                ApplyUnstableTerrainEffect(Actor);
            }
        }
    }
}

void AChaosIsland::UpdateEnvironmentalHazards(float DeltaTime)
{
    if (!bIsActive)
    {
        return;
    }

    AccumulatedTime += DeltaTime;

    // Atualizar perigos ambientais
    for (int32 i = 0; i < EnvironmentalHazards.Num(); ++i)
    {
        if (UStaticMeshComponent* Hazard = EnvironmentalHazards[i])
        {
            // Pulsação dos perigos baseada na intensidade
            float PulseFreq = 1.0f + (EnvironmentalHazardIntensity * 2.0f);
            float PulseValue = 0.7f + 0.3f * FMath::Sin(AccumulatedTime * PulseFreq + i * 0.8f);

            // Escala pulsante
            FVector BaseScale = FVector(1.0f);
            FVector PulseScale = BaseScale * PulseValue;
            Hazard->SetRelativeScale3D(PulseScale);

            // Atualizar material dinâmico
            if (UMaterialInterface* BaseMaterial = Hazard->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = Hazard->CreateDynamicMaterialInstance(0, BaseMaterial);
                if (DynamicMaterial)
                {
                    DynamicMaterial->SetScalarParameterValue(FName("HazardIntensity"), EnvironmentalHazardIntensity);
                    DynamicMaterial->SetScalarParameterValue(FName("PulseValue"), PulseValue);

                    // Cor baseada no tipo de perigo
                    FLinearColor HazardColor;
                    switch (i % 3)
                    {
                        case 0: // Fogo
                            HazardColor = FLinearColor(1.0f, 0.3f, 0.0f);
                            break;
                        case 1: // Gelo
                            HazardColor = FLinearColor(0.0f, 0.7f, 1.0f);
                            break;
                        case 2: // Veneno
                            HazardColor = FLinearColor(0.3f, 1.0f, 0.0f);
                            break;
                        default:
                            HazardColor = FLinearColor::Red;
                            break;
                    }

                    DynamicMaterial->SetVectorParameterValue(FName("HazardColor"), HazardColor);
                }
            }
        }
    }

    // Verificar se deve aplicar efeitos de perigo ambiental em atores próximos
    if (FMath::Fmod(AccumulatedTime, 3.0f) < DeltaTime) // A cada 3 segundos
    {
        TArray<AActor*> OverlappingActors;
        GetOverlappingActors(OverlappingActors, ACharacter::StaticClass());

        for (AActor* Actor : OverlappingActors)
        {
            if (Actor && IsValid(Actor))
            {
                ApplyEnvironmentalHazardEffect(Actor);
            }
        }
    }

    // Spawnar efeitos visuais periódicos
    if (FMath::Fmod(AccumulatedTime, 5.0f) < DeltaTime) // A cada 5 segundos
    {
        for (UStaticMeshComponent* Hazard : EnvironmentalHazards)
        {
            if (Hazard && IsValid(Hazard))
            {
                if (UNiagaraSystem* HazardVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_EnvironmentalHazardPeriodic")))
                {
                    UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                        GetWorld(),
                        HazardVFX,
                        Hazard->GetComponentLocation(),
                        FRotator::ZeroRotator,
                        FVector(EnvironmentalHazardIntensity),
                        true,
                        true,
                        ENCPoolMethod::AutoRelease
                    );
                }
            }
        }
    }
}

void AChaosIsland::UpdateIslandVisuals()
{
    // Implementação robusta da atualização visual usando APIs modernas do UE 5.6
    Super::UpdateIslandVisuals();

    if (!IsValid(this))
    {
        return;
    }

    // Atualizar efeitos baseado no estado da ilha
    // Configurar cor baseada no estado da ilha
    FLinearColor ChaosColor = FLinearColor::Red;

    if (ChaosEnergyEffect && IsValid(ChaosEnergyEffect))
    {

        // Configurar cor baseada no tipo de ambiente
        if (TransitionEnvironments.Num() > 0)
        {
            switch (TransitionEnvironments[0])
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    ChaosColor = FLinearColor(1.0f, 0.5f, 0.0f); // Laranja caótico
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    ChaosColor = FLinearColor(0.8f, 0.2f, 1.0f); // Roxo caótico
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    ChaosColor = FLinearColor(0.2f, 0.8f, 0.2f); // Verde espectral caótico
                    break;
                default:
                    ChaosColor = FLinearColor::Red;
                    break;
            }
        }
        else
        {
            // Cor varia baseada na atividade da ilha como fallback
            if (ActivityLevel > 0.7f)
            {
                ChaosColor = FLinearColor(1.0f, 0.2f, 0.2f); // Vermelho intenso
            }
            else if (ActivityLevel > 0.4f)
            {
                ChaosColor = FLinearColor(1.0f, 0.5f, 0.0f); // Laranja caótico
            }
            else
            {
                ChaosColor = FLinearColor(0.8f, 0.2f, 1.0f); // Roxo caótico
            }
        }

        // Aplicar cor usando APIs modernas
        ChaosEnergyEffect->SetColorParameter(FName("ChaosColor"), ChaosColor);

        // Configurar intensidade baseada na atividade
        ChaosEnergyEffect->SetFloatParameter(FName("ActivityIntensity"), ActivityLevel);
        ChaosEnergyEffect->SetFloatParameter(FName("ChaosMultiplier"), ActivityLevel * 3.0f);

        // Configurar velocidade de rotação dos vórtices
        ChaosEnergyEffect->SetFloatParameter(FName("VortexSpeed"), VortexRotationSpeed * ActivityLevel);

        // Configurar pulsação das runas
        ChaosEnergyEffect->SetFloatParameter(FName("RunePulse"), RunePulseIntensity * ActivityLevel);
    }

    // Atualizar luz caótica
    if (ChaosLight && IsValid(ChaosLight))
    {
        float LightIntensity = 1000.0f + (ActivityLevel * 2000.0f);
        ChaosLight->SetIntensity(LightIntensity);

        // Flickering caótico
        float FlickerValue = 0.8f + 0.2f * FMath::Sin(GetGameTimeSinceCreation() * 10.0f);
        ChaosLight->SetIntensity(LightIntensity * FlickerValue);
    }

    // Atualizar materiais dinâmicos da espiral central
    if (ChaosSpire && IsValid(ChaosSpire))
    {
        if (UMaterialInterface* BaseMaterial = ChaosSpire->GetMaterial(0))
        {
            UMaterialInstanceDynamic* DynamicMaterial = ChaosSpire->CreateDynamicMaterialInstance(0, BaseMaterial);
            if (DynamicMaterial)
            {
                DynamicMaterial->SetScalarParameterValue(FName("ChaosIntensity"), ActivityLevel);
                DynamicMaterial->SetScalarParameterValue(FName("RotationSpeed"), VortexRotationSpeed);
                DynamicMaterial->SetVectorParameterValue(FName("ChaosColor"), FVector(ChaosColor.R, ChaosColor.G, ChaosColor.B));
                DynamicMaterial->SetScalarParameterValue(FName("Metallic"), 0.9f);
                DynamicMaterial->SetScalarParameterValue(FName("Roughness"), 0.1f);
            }
        }
    }

    // Atualizar runas antigas
    for (UStaticMeshComponent* Rune : AncientRunes)
    {
        if (Rune && IsValid(Rune))
        {
            if (UMaterialInterface* BaseMaterial = Rune->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = Rune->CreateDynamicMaterialInstance(0, BaseMaterial);
                if (DynamicMaterial)
                {
                    DynamicMaterial->SetScalarParameterValue(FName("RuneGlow"), RunePulseIntensity * ActivityLevel);
                    DynamicMaterial->SetVectorParameterValue(FName("RuneColor"), FVector(ChaosColor.R, ChaosColor.G, ChaosColor.B));
                    DynamicMaterial->SetScalarParameterValue(FName("PulseSpeed"), 2.0f * ActivityLevel);
                }
            }
        }
    }
}

void AChaosIsland::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades específicas da Chaos Island usando APIs modernas do UE 5.6
    DOREPLIFETIME(AChaosIsland, EnvironmentalHazardIntensity);
    DOREPLIFETIME(AChaosIsland, EnvironmentalHazardDuration);
    DOREPLIFETIME(AChaosIsland, TerrainInstabilityIntensity);
    DOREPLIFETIME(AChaosIsland, TerrainInstabilityDuration);
    DOREPLIFETIME(AChaosIsland, HighRiskRewardMultiplier);
    DOREPLIFETIME(AChaosIsland, HighRiskRewardDuration);
    DOREPLIFETIME(AChaosIsland, VortexRotationSpeed);
    DOREPLIFETIME(AChaosIsland, RunePulseIntensity);
    DOREPLIFETIME(AChaosIsland, TransitionEnvironments);
}