// AURACRONPCGChaosIsland.h
// Definição da classe AChaosIsland para o sistema Prismal Flow

#pragma once

#include "CoreMinimal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "GameplayEffect.h"
#include "GameplayEffectTypes.h"
#include "AURACRONPCGChaosIsland.generated.h"

/**
 * Implementação específica da Chaos Island
 * Ilha com elementos caóticos, vórtices de energia e runas antigas
 * 
 * Características conforme GDD:
 * - Localização: Em pontos de interseção do Fluxo
 * - Características: Perigos ambientais, recompensas de alto risco, terreno instável
 * - Valor Estratégico: Itens que mudam o jogo com risco significativo
 */
UCLASS()
class AURACRON_API AChaosIsland : public APrismalFlowIsland
{
    GENERATED_BODY()
    
public:
    AChaosIsland();
    
    virtual void Tick(float DeltaTime) override;
    virtual void ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult) override;
    
    // Aplica efeito caótico ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Chaos Island")
    void ApplyChaosEffect(AActor* TargetActor);

    /** Definir nível de atividade da ilha (0.0 - 1.0) */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Chaos Island")
    void SetActivityLevel(float NewActivityLevel);

    // Intensidade dos perigos ambientais
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    float EnvironmentalHazardIntensity;

    // Duração dos perigos ambientais
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    float EnvironmentalHazardDuration;

    // Multiplicador de recompensas de alto risco
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    float HighRiskRewardMultiplier;

    // Duração das recompensas de alto risco
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    float HighRiskRewardDuration;

    // Intensidade da instabilidade do terreno
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    float TerrainInstabilityIntensity;

    // Duração da instabilidade do terreno
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    float TerrainInstabilityDuration;

    // Velocidade de rotação dos vórtices
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Chaos Island")
    float VortexRotationSpeed;

    // Intensidade de pulsação das runas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Chaos Island")
    float RunePulseIntensity;

    // Ambientes de transição para efeitos visuais adaptativos
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    TArray<EAURACRONEnvironmentType> TransitionEnvironments;

    virtual void UpdateIslandVisuals() override;

protected:
    // Remove os efeitos caóticos
    UFUNCTION()
    void RemoveChaosEffects(AActor* TargetActor);
    
    // Espiral central do caos
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island")
    UStaticMeshComponent* ChaosSpire;
    
    // Vórtices de energia
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island")
    TArray<UNiagaraComponent*> EnergyVortexes;
    
    // Runas antigas
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island")
    TArray<UStaticMeshComponent*> AncientRunes;
    
    // Zonas de perigo ambiental
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island")
    TArray<UStaticMeshComponent*> HazardZones;

    // Perigos ambientais (alias para compatibilidade)
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island")
    TArray<UStaticMeshComponent*> EnvironmentalHazards;
    
    // Componentes de terreno instável
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island")
    TArray<UStaticMeshComponent*> UnstableTerrainZones;
    
    // Componentes de recompensas de alto risco
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island")
    TArray<UStaticMeshComponent*> HighRiskRewardZones;
    
    // Intensidade do efeito caótico
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    float ChaosIntensity;
    
    // Duração do efeito caótico
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    float ChaosDuration;
    

    
    // Efeito visual do caos
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Chaos Island")
    UNiagaraSystem* ChaosVisualEffect;
    
    // Efeito de gameplay para caos
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    TSubclassOf<UGameplayEffect> ChaosGameplayEffect;
    
    // Efeito de gameplay para perigos ambientais
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    TSubclassOf<UGameplayEffect> EnvironmentalHazardEffect;
    
    // Efeito de gameplay para terreno instável
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Chaos Island")
    TSubclassOf<UGameplayEffect> UnstableTerrainEffect;
    
    // Tabela de recompensas de alto risco
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Chaos Island")
    UDataTable* HighRiskRewardsTable;
    
    // Mapa de efeitos ativos por ator (não serializado devido a limitações do FActiveGameplayEffectHandle)
    // Usado para rastrear efeitos de caos aplicados aos jogadores na ilha
    TMap<TWeakObjectPtr<AActor>, TArray<FActiveGameplayEffectHandle>> ActiveChaosEffects;

    // Mapa de efeitos de perigos ambientais ativos por ator (não serializado devido a limitações do FActiveGameplayEffectHandle)
    // Usado para rastrear efeitos de perigos ambientais aplicados aos jogadores
    TMap<TWeakObjectPtr<AActor>, TArray<FActiveGameplayEffectHandle>> ActiveEnvironmentalHazardEffects;

    // Mapa de efeitos de terreno instável ativos por ator (não serializado devido a limitações do FActiveGameplayEffectHandle)
    // Usado para rastrear efeitos de terreno instável aplicados aos jogadores
    TMap<TWeakObjectPtr<AActor>, TArray<FActiveGameplayEffectHandle>> ActiveUnstableTerrainEffects;
    
    // Tempo acumulado para efeitos visuais
    UPROPERTY()
    float AccumulatedTime;
    

    

    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    
    // Aplica efeitos de perigos ambientais
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Chaos Island")
    void ApplyEnvironmentalHazardEffect(AActor* TargetActor);
    
    // Remove efeitos de perigos ambientais
    UFUNCTION()
    void RemoveEnvironmentalHazardEffects(AActor* TargetActor);
    
    // Aplica efeitos de terreno instável
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Chaos Island")
    void ApplyUnstableTerrainEffect(AActor* TargetActor);
    
    // Remove efeitos de terreno instável
    UFUNCTION()
    void RemoveUnstableTerrainEffects(AActor* TargetActor);
    
    // Concede recompensa de alto risco
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Chaos Island")
    void GrantHighRiskReward(AActor* TargetActor);
    
    // Atualiza o estado do terreno instável
    UFUNCTION()
    void UpdateUnstableTerrain(float DeltaTime);

private:
    /** Nível de atividade atual da ilha (0.0 - 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Chaos Island", meta = (AllowPrivateAccess = "true"))
    float ActivityLevel;

    /** Componente de efeito de energia caótica */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island", meta = (AllowPrivateAccess = "true"))
    class UNiagaraComponent* ChaosEnergyEffect;

    /** Componente de luz caótica */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Chaos Island", meta = (AllowPrivateAccess = "true"))
    class UPointLightComponent* ChaosLight;
    
    // Atualiza os perigos ambientais
    UFUNCTION()
    void UpdateEnvironmentalHazards(float DeltaTime);
};