// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Multiplayer/SigilNetworkConfig.h"

#ifdef AURACRON_SigilNetworkConfig_generated_h
#error "SigilNetworkConfig.generated.h already included, missing '#pragma once' in SigilNetworkConfig.h"
#endif
#define AURACRON_SigilNetworkConfig_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class USigilNetworkConfig;
enum class ESigilNetworkOptimization : uint8;
struct FGameplayTag;
struct FSigilNetworkStats;

// ********** Begin ScriptStruct FSigilReplicationPriority *****************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_28_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilReplicationPriority;
// ********** End ScriptStruct FSigilReplicationPriority *******************************************

// ********** Begin ScriptStruct FSigilDistanceConfig **********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_69_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilDistanceConfig;
// ********** End ScriptStruct FSigilDistanceConfig ************************************************

// ********** Begin ScriptStruct FSigilFrequencyConfig *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_100_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilFrequencyConfig;
// ********** End ScriptStruct FSigilFrequencyConfig ***********************************************

// ********** Begin ScriptStruct FSigilBandwidthConfig *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_131_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilBandwidthConfig;
// ********** End ScriptStruct FSigilBandwidthConfig ***********************************************

// ********** Begin Class USigilNetworkConfig ******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_170_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSigilNetworkConfig); \
	DECLARE_FUNCTION(execGetEstimatedReplicationsPerSecond); \
	DECLARE_FUNCTION(execGetEstimatedBandwidthUsage); \
	DECLARE_FUNCTION(execGetConfigurationWarnings); \
	DECLARE_FUNCTION(execValidateConfiguration); \
	DECLARE_FUNCTION(execShouldReplicateAtDistance); \
	DECLARE_FUNCTION(execGetFrequencyForPriority); \
	DECLARE_FUNCTION(execGetDistanceMultiplier); \
	DECLARE_FUNCTION(execGetPriorityForTag); \
	DECLARE_FUNCTION(execResetToDefaults); \
	DECLARE_FUNCTION(execSetMaxPlayers); \
	DECLARE_FUNCTION(execSetMOBAOptimizations); \
	DECLARE_FUNCTION(execApplyOptimizationType);


AURACRON_API UClass* Z_Construct_UClass_USigilNetworkConfig_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_170_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilNetworkConfig(); \
	friend struct Z_Construct_UClass_USigilNetworkConfig_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilNetworkConfig_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilNetworkConfig, UDeveloperSettings, COMPILED_IN_FLAGS(0 | CLASS_DefaultConfig | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilNetworkConfig_NoRegister) \
	DECLARE_SERIALIZER(USigilNetworkConfig) \
	static const TCHAR* StaticConfigName() {return TEXT("Game");} \



#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_170_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilNetworkConfig(USigilNetworkConfig&&) = delete; \
	USigilNetworkConfig(const USigilNetworkConfig&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilNetworkConfig); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilNetworkConfig); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilNetworkConfig) \
	NO_API virtual ~USigilNetworkConfig();


#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_167_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_170_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_170_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_170_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_170_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilNetworkConfig;

// ********** End Class USigilNetworkConfig ********************************************************

// ********** Begin ScriptStruct FSigilNetworkStats ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_298_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilNetworkStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilNetworkStats;
// ********** End ScriptStruct FSigilNetworkStats **************************************************

// ********** Begin Delegate FOnNetworkOptimizationChanged *****************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_334_DELEGATE \
AURACRON_API void FOnNetworkOptimizationChanged_DelegateWrapper(const FMulticastScriptDelegate& OnNetworkOptimizationChanged, ESigilNetworkOptimization NewOptimization);


// ********** End Delegate FOnNetworkOptimizationChanged *******************************************

// ********** Begin Delegate FOnBandwidthLimitReached **********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_335_DELEGATE \
AURACRON_API void FOnBandwidthLimitReached_DelegateWrapper(const FMulticastScriptDelegate& OnBandwidthLimitReached, float CurrentUsage);


// ********** End Delegate FOnBandwidthLimitReached ************************************************

// ********** Begin Delegate FOnNetworkStatsUpdated ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h_336_DELEGATE \
AURACRON_API void FOnNetworkStatsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnNetworkStatsUpdated, FSigilNetworkStats const& Stats);


// ********** End Delegate FOnNetworkStatsUpdated **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h

// ********** Begin Enum ESigilNetworkOptimization *************************************************
#define FOREACH_ENUM_ESIGILNETWORKOPTIMIZATION(op) \
	op(ESigilNetworkOptimization::None) \
	op(ESigilNetworkOptimization::Basic) \
	op(ESigilNetworkOptimization::MOBA) \
	op(ESigilNetworkOptimization::Competitive) \
	op(ESigilNetworkOptimization::Custom) 

enum class ESigilNetworkOptimization : uint8;
template<> struct TIsUEnumClass<ESigilNetworkOptimization> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilNetworkOptimization>();
// ********** End Enum ESigilNetworkOptimization ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
