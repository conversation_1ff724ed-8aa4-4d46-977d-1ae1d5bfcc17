// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SigilManagerComponent.h"
#include "GameplayAbilitySpecHandle.h"
#include "GameplayTagContainer.h"
#include "UObject/CoreNet.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilManagerComponent() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityBase_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAttributeSet_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilManagerComponent();
AURACRON_API UClass* Z_Construct_UClass_USigilManagerComponent_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilSubType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilType();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilSlotData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilSystemStats();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAbilitySpecHandle();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FSigilSlotData ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilSlotData;
class UScriptStruct* FSigilSlotData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilSlotData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilSlotData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilSlotData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilSlotData"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilSlotData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilSlotData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para dados de slot de sigilo\n * Cont\xc3\xa9m informa\xc3\xa7\xc3\xb5""es sobre cada slot individual\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de slot de sigilo\nCont\xc3\xa9m informa\xc3\xa7\xc3\xb5""es sobre cada slot individual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedSigil_MetaData[] = {
		{ "Category", "Sigil Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sigilo atualmente equipado neste slot */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sigilo atualmente equipado neste slot" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnlocked_MetaData[] = {
		{ "Category", "Sigil Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o slot est\xc3\xa1 desbloqueado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o slot est\xc3\xa1 desbloqueado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockLevel_MetaData[] = {
		{ "Category", "Sigil Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Level necess\xc3\xa1rio para desbloquear este slot */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level necess\xc3\xa1rio para desbloquear este slot" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquipTimestamp_MetaData[] = {
		{ "Category", "Sigil Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp quando sigilo foi equipado (para fus\xc3\xa3o) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp quando sigilo foi equipado (para fus\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReadyForFusion_MetaData[] = {
		{ "Category", "Sigil Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o sigilo est\xc3\xa1 pronto para fus\xc3\xa3o (6 minutos) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o sigilo est\xc3\xa1 pronto para fus\xc3\xa3o (6 minutos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionMultiplier_MetaData[] = {
		{ "Category", "Sigil Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de fus\xc3\xa3o aplicado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de fus\xc3\xa3o aplicado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotTags_MetaData[] = {
		{ "Category", "Sigil Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GameplayTags espec\xc3\xad""ficos do slot */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayTags espec\xc3\xad""ficos do slot" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EquippedSigil;
	static void NewProp_bIsUnlocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnlocked;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnlockLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EquipTimestamp;
	static void NewProp_bReadyForFusion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReadyForFusion;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlotTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilSlotData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_EquippedSigil = { "EquippedSigil", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotData, EquippedSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedSigil_MetaData), NewProp_EquippedSigil_MetaData) };
void Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_bIsUnlocked_SetBit(void* Obj)
{
	((FSigilSlotData*)Obj)->bIsUnlocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_bIsUnlocked = { "bIsUnlocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilSlotData), &Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_bIsUnlocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnlocked_MetaData), NewProp_bIsUnlocked_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_UnlockLevel = { "UnlockLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotData, UnlockLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockLevel_MetaData), NewProp_UnlockLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_EquipTimestamp = { "EquipTimestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotData, EquipTimestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquipTimestamp_MetaData), NewProp_EquipTimestamp_MetaData) };
void Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_bReadyForFusion_SetBit(void* Obj)
{
	((FSigilSlotData*)Obj)->bReadyForFusion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_bReadyForFusion = { "bReadyForFusion", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilSlotData), &Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_bReadyForFusion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReadyForFusion_MetaData), NewProp_bReadyForFusion_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_FusionMultiplier = { "FusionMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotData, FusionMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionMultiplier_MetaData), NewProp_FusionMultiplier_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_SlotTags = { "SlotTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotData, SlotTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotTags_MetaData), NewProp_SlotTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilSlotData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_EquippedSigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_bIsUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_UnlockLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_EquipTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_bReadyForFusion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_FusionMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewProp_SlotTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSlotData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilSlotData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilSlotData",
	Z_Construct_UScriptStruct_FSigilSlotData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSlotData_Statics::PropPointers),
	sizeof(FSigilSlotData),
	alignof(FSigilSlotData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSlotData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilSlotData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilSlotData()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilSlotData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilSlotData.InnerSingleton, Z_Construct_UScriptStruct_FSigilSlotData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilSlotData.InnerSingleton;
}
// ********** End ScriptStruct FSigilSlotData ******************************************************

// ********** Begin ScriptStruct FSigilFusionConfig ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilFusionConfig;
class UScriptStruct* FSigilFusionConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilFusionConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilFusionConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilFusionConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilFusionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xa3o de fus\xc3\xa3o de s\xc3\xadgilos\n * Define como s\xc3\xadgilos se fundem ap\xc3\xb3s 6 minutos\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de fus\xc3\xa3o de s\xc3\xadgilos\nDefine como s\xc3\xadgilos se fundem ap\xc3\xb3s 6 minutos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionTimeSeconds_MetaData[] = {
		{ "Category", "Fusion Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo em segundos para fus\xc3\xa3o autom\xc3\xa1tica */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo em segundos para fus\xc3\xa3o autom\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RarityMultipliers_MetaData[] = {
		{ "Category", "Fusion Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicadores por raridade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicadores por raridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionEffect_MetaData[] = {
		{ "Category", "Fusion Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GameplayEffect aplicado durante fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffect aplicado durante fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionVFX_MetaData[] = {
		{ "Category", "Fusion Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\xad""culas para fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionSound_MetaData[] = {
		{ "Category", "Fusion Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionTags_MetaData[] = {
		{ "Category", "Fusion Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags aplicadas durante fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags aplicadas durante fus\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionTimeSeconds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RarityMultipliers_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RarityMultipliers_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RarityMultipliers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RarityMultipliers;
	static const UECodeGen_Private::FClassPropertyParams NewProp_FusionEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FusionVFX;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FusionSound;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilFusionConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionTimeSeconds = { "FusionTimeSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionConfig, FusionTimeSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionTimeSeconds_MetaData), NewProp_FusionTimeSeconds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_RarityMultipliers_ValueProp = { "RarityMultipliers", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_RarityMultipliers_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_RarityMultipliers_Key_KeyProp = { "RarityMultipliers_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_RarityMultipliers = { "RarityMultipliers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionConfig, RarityMultipliers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RarityMultipliers_MetaData), NewProp_RarityMultipliers_MetaData) }; // 3544987888
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionEffect = { "FusionEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionConfig, FusionEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionEffect_MetaData), NewProp_FusionEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionVFX = { "FusionVFX", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionConfig, FusionVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionVFX_MetaData), NewProp_FusionVFX_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionSound = { "FusionSound", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionConfig, FusionSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionSound_MetaData), NewProp_FusionSound_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionTags = { "FusionTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionConfig, FusionTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionTags_MetaData), NewProp_FusionTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionTimeSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_RarityMultipliers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_RarityMultipliers_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_RarityMultipliers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_RarityMultipliers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewProp_FusionTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilFusionConfig",
	Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::PropPointers),
	sizeof(FSigilFusionConfig),
	alignof(FSigilFusionConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilFusionConfig.InnerSingleton, Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionConfig.InnerSingleton;
}
// ********** End ScriptStruct FSigilFusionConfig **************************************************

// ********** Begin ScriptStruct FSigilSystemStats *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilSystemStats;
class UScriptStruct* FSigilSystemStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilSystemStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilSystemStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilSystemStats, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilSystemStats"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilSystemStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilSystemStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para estat\xc3\xadsticas do sistema de s\xc3\xadgilos\n * Usada para debugging e balanceamento\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para estat\xc3\xadsticas do sistema de s\xc3\xadgilos\nUsada para debugging e balanceamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalEquippedSigils_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total de s\xc3\xadgilos equipados */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total de s\xc3\xadgilos equipados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalFusedSigils_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total de s\xc3\xadgilos fundidos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total de s\xc3\xadgilos fundidos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalSigilPower_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Poder total dos s\xc3\xadgilos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder total dos s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFusionTime_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo m\xc3\xa9""dio para fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo m\xc3\xa9""dio para fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalReforges_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total de reforges realizados */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total de reforges realizados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalEquippedSigils;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalFusedSigils;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalSigilPower;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFusionTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalReforges;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilSystemStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_TotalEquippedSigils = { "TotalEquippedSigils", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSystemStats, TotalEquippedSigils), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalEquippedSigils_MetaData), NewProp_TotalEquippedSigils_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_TotalFusedSigils = { "TotalFusedSigils", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSystemStats, TotalFusedSigils), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalFusedSigils_MetaData), NewProp_TotalFusedSigils_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_TotalSigilPower = { "TotalSigilPower", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSystemStats, TotalSigilPower), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalSigilPower_MetaData), NewProp_TotalSigilPower_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_AverageFusionTime = { "AverageFusionTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSystemStats, AverageFusionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFusionTime_MetaData), NewProp_AverageFusionTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_TotalReforges = { "TotalReforges", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSystemStats, TotalReforges), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalReforges_MetaData), NewProp_TotalReforges_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilSystemStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_TotalEquippedSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_TotalFusedSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_TotalSigilPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_AverageFusionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewProp_TotalReforges,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSystemStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilSystemStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilSystemStats",
	Z_Construct_UScriptStruct_FSigilSystemStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSystemStats_Statics::PropPointers),
	sizeof(FSigilSystemStats),
	alignof(FSigilSystemStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSystemStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilSystemStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilSystemStats()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilSystemStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilSystemStats.InnerSingleton, Z_Construct_UScriptStruct_FSigilSystemStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilSystemStats.InnerSingleton;
}
// ********** End ScriptStruct FSigilSystemStats ***************************************************

// ********** Begin Delegate FOnSigilEvent *********************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilEvent_Parms
	{
		ASigilItem* Sigil;
		int32 SlotIndex;
		FGameplayTag EventTag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Delegate para eventos do sistema de s\xc3\xadgilos\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate para eventos do sistema de s\xc3\xadgilos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilEvent_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilEvent_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilEvent_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::NewProp_EventTag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilEvent__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilEvent_DelegateWrapper(const FMulticastScriptDelegate& OnSigilEvent, ASigilItem* Sigil, int32 SlotIndex, FGameplayTag EventTag)
{
	struct _Script_AURACRON_eventOnSigilEvent_Parms
	{
		ASigilItem* Sigil;
		int32 SlotIndex;
		FGameplayTag EventTag;
	};
	_Script_AURACRON_eventOnSigilEvent_Parms Parms;
	Parms.Sigil=Sigil;
	Parms.SlotIndex=SlotIndex;
	Parms.EventTag=EventTag;
	OnSigilEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilEvent ***********************************************************

// ********** Begin Delegate FOnSigilFusion ********************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilFusion_Parms
	{
		ASigilItem* Sigil;
		float Multiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Multiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusion_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::NewProp_Multiplier = { "Multiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusion_Parms, Multiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::NewProp_Multiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilFusion__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilFusion_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusion, ASigilItem* Sigil, float Multiplier)
{
	struct _Script_AURACRON_eventOnSigilFusion_Parms
	{
		ASigilItem* Sigil;
		float Multiplier;
	};
	_Script_AURACRON_eventOnSigilFusion_Parms Parms;
	Parms.Sigil=Sigil;
	Parms.Multiplier=Multiplier;
	OnSigilFusion.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilFusion **********************************************************

// ********** Begin Delegate FOnSigilSlotUnlocked **************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilSlotUnlocked_Parms
	{
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilSlotUnlocked_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilSlotUnlocked__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilSlotUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilSlotUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilSlotUnlocked_DelegateWrapper(const FMulticastScriptDelegate& OnSigilSlotUnlocked, int32 SlotIndex)
{
	struct _Script_AURACRON_eventOnSigilSlotUnlocked_Parms
	{
		int32 SlotIndex;
	};
	_Script_AURACRON_eventOnSigilSlotUnlocked_Parms Parms;
	Parms.SlotIndex=SlotIndex;
	OnSigilSlotUnlocked.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilSlotUnlocked ****************************************************

// ********** Begin Delegate FOnSigilStatsChanged **************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilStatsChanged_Parms
	{
		FSigilSystemStats NewStats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewStats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::NewProp_NewStats = { "NewStats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilStatsChanged_Parms, NewStats), Z_Construct_UScriptStruct_FSigilSystemStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewStats_MetaData), NewProp_NewStats_MetaData) }; // 2041897055
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::NewProp_NewStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilStatsChanged__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilStatsChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilStatsChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilStatsChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSigilStatsChanged, FSigilSystemStats const& NewStats)
{
	struct _Script_AURACRON_eventOnSigilStatsChanged_Parms
	{
		FSigilSystemStats NewStats;
	};
	_Script_AURACRON_eventOnSigilStatsChanged_Parms Parms;
	Parms.NewStats=NewStats;
	OnSigilStatsChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilStatsChanged ****************************************************

// ********** Begin Delegate FOnExclusiveAbilityActivated ******************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnExclusiveAbilityActivated_Parms
	{
		ESigilSubType SubType;
		float CooldownDuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::NewProp_SubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::NewProp_SubType = { "SubType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnExclusiveAbilityActivated_Parms, SubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::NewProp_CooldownDuration = { "CooldownDuration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnExclusiveAbilityActivated_Parms, CooldownDuration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::NewProp_SubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::NewProp_SubType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::NewProp_CooldownDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnExclusiveAbilityActivated__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::_Script_AURACRON_eventOnExclusiveAbilityActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::_Script_AURACRON_eventOnExclusiveAbilityActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnExclusiveAbilityActivated_DelegateWrapper(const FMulticastScriptDelegate& OnExclusiveAbilityActivated, ESigilSubType SubType, float CooldownDuration)
{
	struct _Script_AURACRON_eventOnExclusiveAbilityActivated_Parms
	{
		ESigilSubType SubType;
		float CooldownDuration;
	};
	_Script_AURACRON_eventOnExclusiveAbilityActivated_Parms Parms;
	Parms.SubType=SubType;
	Parms.CooldownDuration=CooldownDuration;
	OnExclusiveAbilityActivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnExclusiveAbilityActivated ********************************************

// ********** Begin Delegate FOnExclusiveAbilityCooldownChanged ************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnExclusiveAbilityCooldownChanged_Parms
	{
		ESigilSubType SubType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::NewProp_SubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::NewProp_SubType = { "SubType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnExclusiveAbilityCooldownChanged_Parms, SubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::NewProp_SubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::NewProp_SubType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnExclusiveAbilityCooldownChanged__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnExclusiveAbilityCooldownChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnExclusiveAbilityCooldownChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnExclusiveAbilityCooldownChanged_DelegateWrapper(const FMulticastScriptDelegate& OnExclusiveAbilityCooldownChanged, ESigilSubType SubType)
{
	struct _Script_AURACRON_eventOnExclusiveAbilityCooldownChanged_Parms
	{
		ESigilSubType SubType;
	};
	_Script_AURACRON_eventOnExclusiveAbilityCooldownChanged_Parms Parms;
	Parms.SubType=SubType;
	OnExclusiveAbilityCooldownChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnExclusiveAbilityCooldownChanged **************************************

// ********** Begin Class USigilManagerComponent Function ActivateExclusiveAbility *****************
struct Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics
{
	struct SigilManagerComponent_eventActivateExclusiveAbility_Parms
	{
		ESigilSubType SubType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativa habilidade exclusiva de subtipo espec\xc3\xad""fico\n     * @param SubType - Subtipo do sigilo\n     * @return true se habilidade foi ativada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa habilidade exclusiva de subtipo espec\xc3\xad""fico\n@param SubType - Subtipo do sigilo\n@return true se habilidade foi ativada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::NewProp_SubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::NewProp_SubType = { "SubType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventActivateExclusiveAbility_Parms, SubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
void Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventActivateExclusiveAbility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventActivateExclusiveAbility_Parms), &Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::NewProp_SubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::NewProp_SubType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ActivateExclusiveAbility", Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::SigilManagerComponent_eventActivateExclusiveAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::SigilManagerComponent_eventActivateExclusiveAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execActivateExclusiveAbility)
{
	P_GET_ENUM(ESigilSubType,Z_Param_SubType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateExclusiveAbility(ESigilSubType(Z_Param_SubType));
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ActivateExclusiveAbility *******************

// ********** Begin Class USigilManagerComponent Function CalculateTotalSigilPower *****************
struct Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics
{
	struct SigilManagerComponent_eventCalculateTotalSigilPower_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Calcula poder total de todos os s\xc3\xadgilos\n     * @return Poder total combinado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcula poder total de todos os s\xc3\xadgilos\n@return Poder total combinado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventCalculateTotalSigilPower_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "CalculateTotalSigilPower", Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::SigilManagerComponent_eventCalculateTotalSigilPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::SigilManagerComponent_eventCalculateTotalSigilPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execCalculateTotalSigilPower)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTotalSigilPower();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function CalculateTotalSigilPower *******************

// ********** Begin Class USigilManagerComponent Function CanActivateExclusiveAbility **************
struct Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics
{
	struct SigilManagerComponent_eventCanActivateExclusiveAbility_Parms
	{
		ESigilSubType SubType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se pode ativar habilidade exclusiva\n     * @param SubType - Subtipo do sigilo\n     * @return true se pode ativar\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se pode ativar habilidade exclusiva\n@param SubType - Subtipo do sigilo\n@return true se pode ativar" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::NewProp_SubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::NewProp_SubType = { "SubType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventCanActivateExclusiveAbility_Parms, SubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
void Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventCanActivateExclusiveAbility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventCanActivateExclusiveAbility_Parms), &Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::NewProp_SubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::NewProp_SubType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "CanActivateExclusiveAbility", Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::SigilManagerComponent_eventCanActivateExclusiveAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::SigilManagerComponent_eventCanActivateExclusiveAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execCanActivateExclusiveAbility)
{
	P_GET_ENUM(ESigilSubType,Z_Param_SubType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanActivateExclusiveAbility(ESigilSubType(Z_Param_SubType));
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function CanActivateExclusiveAbility ****************

// ********** Begin Class USigilManagerComponent Function CanAffordReforge *************************
struct Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics
{
	struct SigilManagerComponent_eventCanAffordReforge_Parms
	{
		ASigilItem* Sigil;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se pode pagar pelo reforge\n     * @param Sigil - Sigilo para reforge\n     * @return true se tem recursos suficientes\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se pode pagar pelo reforge\n@param Sigil - Sigilo para reforge\n@return true se tem recursos suficientes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventCanAffordReforge_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventCanAffordReforge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventCanAffordReforge_Parms), &Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "CanAffordReforge", Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::SigilManagerComponent_eventCanAffordReforge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::SigilManagerComponent_eventCanAffordReforge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execCanAffordReforge)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanAffordReforge(Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function CanAffordReforge ***************************

// ********** Begin Class USigilManagerComponent Function CanEquipSigil ****************************
struct Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics
{
	struct SigilManagerComponent_eventCanEquipSigil_Parms
	{
		ASigilItem* Sigil;
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se sigilo pode ser equipado em slot\n     * @param Sigil - Sigilo para verificar\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return true se pode equipar\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se sigilo pode ser equipado em slot\n@param Sigil - Sigilo para verificar\n@param SlotIndex - \xc3\x8dndice do slot\n@return true se pode equipar" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventCanEquipSigil_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventCanEquipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventCanEquipSigil_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventCanEquipSigil_Parms), &Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "CanEquipSigil", Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::SigilManagerComponent_eventCanEquipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::SigilManagerComponent_eventCanEquipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execCanEquipSigil)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanEquipSigil(Z_Param_Sigil,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function CanEquipSigil ******************************

// ********** Begin Class USigilManagerComponent Function CanReforge *******************************
struct Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics
{
	struct SigilManagerComponent_eventCanReforge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se reforge est\xc3\xa1 dispon\xc3\xadvel\n     * @return true se pode reforjar\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se reforge est\xc3\xa1 dispon\xc3\xadvel\n@return true se pode reforjar" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventCanReforge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventCanReforge_Parms), &Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "CanReforge", Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::SigilManagerComponent_eventCanReforge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::SigilManagerComponent_eventCanReforge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_CanReforge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_CanReforge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execCanReforge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanReforge();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function CanReforge *********************************

// ********** Begin Class USigilManagerComponent Function ConsumeReforgeResources ******************
struct Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics
{
	struct SigilManagerComponent_eventConsumeReforgeResources_Parms
	{
		ASigilItem* Sigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Consome recursos necess\xc3\xa1rios para reforge\n     * @param Sigil - Sigilo sendo reforjado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Consome recursos necess\xc3\xa1rios para reforge\n@param Sigil - Sigilo sendo reforjado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventConsumeReforgeResources_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::NewProp_Sigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ConsumeReforgeResources", Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::SigilManagerComponent_eventConsumeReforgeResources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::SigilManagerComponent_eventConsumeReforgeResources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execConsumeReforgeResources)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConsumeReforgeResources(Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ConsumeReforgeResources ********************

// ********** Begin Class USigilManagerComponent Function DEBUG_EquipRandomSigil *******************
struct Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics
{
	struct SigilManagerComponent_eventDEBUG_EquipRandomSigil_Parms
	{
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Sigil Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Simula equipar sigilo aleat\xc3\xb3rio (debug)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simula equipar sigilo aleat\xc3\xb3rio (debug)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventDEBUG_EquipRandomSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "DEBUG_EquipRandomSigil", Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::SigilManagerComponent_eventDEBUG_EquipRandomSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::SigilManagerComponent_eventDEBUG_EquipRandomSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execDEBUG_EquipRandomSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DEBUG_EquipRandomSigil(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function DEBUG_EquipRandomSigil *********************

// ********** Begin Class USigilManagerComponent Function DEBUG_ForceActivateExclusiveAbility ******
struct Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics
{
	struct SigilManagerComponent_eventDEBUG_ForceActivateExclusiveAbility_Parms
	{
		ESigilSubType SubType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n      * For\xc3\xa7""a ativa\xc3\xa7\xc3\xa3o de habilidade exclusiva (debug)\n      * @param SubType - Subtipo do sigilo\n      */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a ativa\xc3\xa7\xc3\xa3o de habilidade exclusiva (debug)\n@param SubType - Subtipo do sigilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::NewProp_SubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::NewProp_SubType = { "SubType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventDEBUG_ForceActivateExclusiveAbility_Parms, SubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::NewProp_SubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::NewProp_SubType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "DEBUG_ForceActivateExclusiveAbility", Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::SigilManagerComponent_eventDEBUG_ForceActivateExclusiveAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::SigilManagerComponent_eventDEBUG_ForceActivateExclusiveAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execDEBUG_ForceActivateExclusiveAbility)
{
	P_GET_ENUM(ESigilSubType,Z_Param_SubType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DEBUG_ForceActivateExclusiveAbility(ESigilSubType(Z_Param_SubType));
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function DEBUG_ForceActivateExclusiveAbility ********

// ********** Begin Class USigilManagerComponent Function DEBUG_ForceAllFusions ********************
struct Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceAllFusions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Sigil Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * For\xc3\xa7""a fus\xc3\xa3o de todos os s\xc3\xadgilos (debug)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a fus\xc3\xa3o de todos os s\xc3\xadgilos (debug)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceAllFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "DEBUG_ForceAllFusions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceAllFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceAllFusions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceAllFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceAllFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execDEBUG_ForceAllFusions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DEBUG_ForceAllFusions();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function DEBUG_ForceAllFusions **********************

// ********** Begin Class USigilManagerComponent Function DEBUG_PrintSystemInfo ********************
struct Z_Construct_UFunction_USigilManagerComponent_DEBUG_PrintSystemInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mostra informa\xc3\xa7\xc3\xb5""es detalhadas no log\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostra informa\xc3\xa7\xc3\xb5""es detalhadas no log" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_PrintSystemInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "DEBUG_PrintSystemInfo", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_PrintSystemInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_DEBUG_PrintSystemInfo_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilManagerComponent_DEBUG_PrintSystemInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_DEBUG_PrintSystemInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execDEBUG_PrintSystemInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DEBUG_PrintSystemInfo();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function DEBUG_PrintSystemInfo **********************

// ********** Begin Class USigilManagerComponent Function DEBUG_ResetSystem ************************
struct Z_Construct_UFunction_USigilManagerComponent_DEBUG_ResetSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Sigil Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reset completo do sistema (debug)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reset completo do sistema (debug)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_ResetSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "DEBUG_ResetSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_ResetSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_DEBUG_ResetSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilManagerComponent_DEBUG_ResetSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_DEBUG_ResetSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execDEBUG_ResetSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DEBUG_ResetSystem();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function DEBUG_ResetSystem **************************

// ********** Begin Class USigilManagerComponent Function DEBUG_UnlockAllSlots *********************
struct Z_Construct_UFunction_USigilManagerComponent_DEBUG_UnlockAllSlots_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Sigil Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * For\xc3\xa7""a desbloqueio de todos os slots (debug)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a desbloqueio de todos os slots (debug)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_DEBUG_UnlockAllSlots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "DEBUG_UnlockAllSlots", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_DEBUG_UnlockAllSlots_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_DEBUG_UnlockAllSlots_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilManagerComponent_DEBUG_UnlockAllSlots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_DEBUG_UnlockAllSlots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execDEBUG_UnlockAllSlots)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DEBUG_UnlockAllSlots();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function DEBUG_UnlockAllSlots ***********************

// ********** Begin Class USigilManagerComponent Function EquipSigil *******************************
struct Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics
{
	struct SigilManagerComponent_eventEquipSigil_Parms
	{
		ASigilItem* Sigil;
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Equipa sigilo em slot espec\xc3\xad""fico\n     * @param Sigil - Sigilo para equipar\n     * @param SlotIndex - \xc3\x8dndice do slot (0-5)\n     * @return true se equipado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipa sigilo em slot espec\xc3\xad""fico\n@param Sigil - Sigilo para equipar\n@param SlotIndex - \xc3\x8dndice do slot (0-5)\n@return true se equipado com sucesso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventEquipSigil_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventEquipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventEquipSigil_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventEquipSigil_Parms), &Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "EquipSigil", Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::SigilManagerComponent_eventEquipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::SigilManagerComponent_eventEquipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_EquipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_EquipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execEquipSigil)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EquipSigil(Z_Param_Sigil,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function EquipSigil *********************************

// ********** Begin Class USigilManagerComponent Function ForceFuseSigil ***************************
struct Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics
{
	struct SigilManagerComponent_eventForceFuseSigil_Parms
	{
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * For\xc3\xa7""a fus\xc3\xa3o de sigilo espec\xc3\xad""fico\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return true se fus\xc3\xa3o foi aplicada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a fus\xc3\xa3o de sigilo espec\xc3\xad""fico\n@param SlotIndex - \xc3\x8dndice do slot\n@return true se fus\xc3\xa3o foi aplicada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventForceFuseSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventForceFuseSigil_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventForceFuseSigil_Parms), &Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ForceFuseSigil", Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::SigilManagerComponent_eventForceFuseSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::SigilManagerComponent_eventForceFuseSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execForceFuseSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ForceFuseSigil(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ForceFuseSigil *****************************

// ********** Begin Class USigilManagerComponent Function GetAllEquippedSigils *********************
struct Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics
{
	struct SigilManagerComponent_eventGetAllEquippedSigils_Parms
	{
		TArray<ASigilItem*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m todos os s\xc3\xadgilos equipados\n     * @return Array de s\xc3\xadgilos equipados\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m todos os s\xc3\xadgilos equipados\n@return Array de s\xc3\xadgilos equipados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetAllEquippedSigils_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetAllEquippedSigils", Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::SigilManagerComponent_eventGetAllEquippedSigils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::SigilManagerComponent_eventGetAllEquippedSigils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetAllEquippedSigils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ASigilItem*>*)Z_Param__Result=P_THIS->GetAllEquippedSigils();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetAllEquippedSigils ***********************

// ********** Begin Class USigilManagerComponent Function GetAvailableExclusiveAbilities ***********
struct Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics
{
	struct SigilManagerComponent_eventGetAvailableExclusiveAbilities_Parms
	{
		TArray<ESigilSubType> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m lista de habilidades exclusivas dispon\xc3\xadveis\n     * @return Array de subtipos dispon\xc3\xadveis\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m lista de habilidades exclusivas dispon\xc3\xadveis\n@return Array de subtipos dispon\xc3\xadveis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::NewProp_ReturnValue_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetAvailableExclusiveAbilities_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::NewProp_ReturnValue_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetAvailableExclusiveAbilities", Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::SigilManagerComponent_eventGetAvailableExclusiveAbilities_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::SigilManagerComponent_eventGetAvailableExclusiveAbilities_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetAvailableExclusiveAbilities)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ESigilSubType>*)Z_Param__Result=P_THIS->GetAvailableExclusiveAbilities();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetAvailableExclusiveAbilities *************

// ********** Begin Class USigilManagerComponent Function GetEquippedSigil *************************
struct Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics
{
	struct SigilManagerComponent_eventGetEquippedSigil_Parms
	{
		int32 SlotIndex;
		ASigilItem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m sigilo equipado em slot espec\xc3\xad""fico\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return Sigilo equipado ou nullptr\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m sigilo equipado em slot espec\xc3\xad""fico\n@param SlotIndex - \xc3\x8dndice do slot\n@return Sigilo equipado ou nullptr" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetEquippedSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetEquippedSigil_Parms, ReturnValue), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetEquippedSigil", Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::SigilManagerComponent_eventGetEquippedSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::SigilManagerComponent_eventGetEquippedSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetEquippedSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ASigilItem**)Z_Param__Result=P_THIS->GetEquippedSigil(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetEquippedSigil ***************************

// ********** Begin Class USigilManagerComponent Function GetExclusiveAbilityCooldown **************
struct Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics
{
	struct SigilManagerComponent_eventGetExclusiveAbilityCooldown_Parms
	{
		ESigilSubType SubType;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m cooldown restante da habilidade exclusiva\n     * @param SubType - Subtipo do sigilo\n     * @return Tempo em segundos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m cooldown restante da habilidade exclusiva\n@param SubType - Subtipo do sigilo\n@return Tempo em segundos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::NewProp_SubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::NewProp_SubType = { "SubType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetExclusiveAbilityCooldown_Parms, SubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetExclusiveAbilityCooldown_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::NewProp_SubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::NewProp_SubType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetExclusiveAbilityCooldown", Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::SigilManagerComponent_eventGetExclusiveAbilityCooldown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::SigilManagerComponent_eventGetExclusiveAbilityCooldown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetExclusiveAbilityCooldown)
{
	P_GET_ENUM(ESigilSubType,Z_Param_SubType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetExclusiveAbilityCooldown(ESigilSubType(Z_Param_SubType));
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetExclusiveAbilityCooldown ****************

// ********** Begin Class USigilManagerComponent Function GetFusionProgress ************************
struct Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics
{
	struct SigilManagerComponent_eventGetFusionProgress_Parms
	{
		int32 SlotIndex;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m progresso da fus\xc3\xa3o\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return Progresso de 0.0 a 1.0\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m progresso da fus\xc3\xa3o\n@param SlotIndex - \xc3\x8dndice do slot\n@return Progresso de 0.0 a 1.0" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetFusionProgress_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetFusionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetFusionProgress", Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::SigilManagerComponent_eventGetFusionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::SigilManagerComponent_eventGetFusionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetFusionProgress)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFusionProgress(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetFusionProgress **************************

// ********** Begin Class USigilManagerComponent Function GetReforgeTimeRemaining ******************
struct Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics
{
	struct SigilManagerComponent_eventGetReforgeTimeRemaining_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m tempo restante para pr\xc3\xb3ximo reforge\n     * @return Tempo em segundos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m tempo restante para pr\xc3\xb3ximo reforge\n@return Tempo em segundos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetReforgeTimeRemaining_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetReforgeTimeRemaining", Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::SigilManagerComponent_eventGetReforgeTimeRemaining_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::SigilManagerComponent_eventGetReforgeTimeRemaining_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetReforgeTimeRemaining)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetReforgeTimeRemaining();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetReforgeTimeRemaining ********************

// ********** Begin Class USigilManagerComponent Function GetSigilByID *****************************
struct Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics
{
	struct SigilManagerComponent_eventGetSigilByID_Parms
	{
		int32 SigilID;
		ASigilItem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m sigilo por ID\n     * @param SigilID - ID do sigilo para buscar\n     * @return Sigilo encontrado ou nullptr\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m sigilo por ID\n@param SigilID - ID do sigilo para buscar\n@return Sigilo encontrado ou nullptr" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetSigilByID_Parms, SigilID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetSigilByID_Parms, ReturnValue), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetSigilByID", Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::SigilManagerComponent_eventGetSigilByID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::SigilManagerComponent_eventGetSigilByID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetSigilByID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetSigilByID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetSigilByID)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SigilID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ASigilItem**)Z_Param__Result=P_THIS->GetSigilByID(Z_Param_SigilID);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetSigilByID *******************************

// ********** Begin Class USigilManagerComponent Function GetSigilsByRarity ************************
struct Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics
{
	struct SigilManagerComponent_eventGetSigilsByRarity_Parms
	{
		ESigilRarity Rarity;
		TArray<ASigilItem*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m s\xc3\xadgilos por raridade\n     * @param Rarity - Raridade do sigilo\n     * @return Array de s\xc3\xadgilos da raridade especificada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m s\xc3\xadgilos por raridade\n@param Rarity - Raridade do sigilo\n@return Array de s\xc3\xadgilos da raridade especificada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetSigilsByRarity_Parms, Rarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetSigilsByRarity_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetSigilsByRarity", Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::SigilManagerComponent_eventGetSigilsByRarity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::SigilManagerComponent_eventGetSigilsByRarity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetSigilsByRarity)
{
	P_GET_ENUM(ESigilRarity,Z_Param_Rarity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ASigilItem*>*)Z_Param__Result=P_THIS->GetSigilsByRarity(ESigilRarity(Z_Param_Rarity));
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetSigilsByRarity **************************

// ********** Begin Class USigilManagerComponent Function GetSigilsByType **************************
struct Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics
{
	struct SigilManagerComponent_eventGetSigilsByType_Parms
	{
		ESigilType Type;
		TArray<ASigilItem*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m s\xc3\xadgilos por tipo\n     * @param Type - Tipo de sigilo\n     * @return Array de s\xc3\xadgilos do tipo especificado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m s\xc3\xadgilos por tipo\n@param Type - Tipo de sigilo\n@return Array de s\xc3\xadgilos do tipo especificado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetSigilsByType_Parms, Type), Z_Construct_UEnum_AURACRON_ESigilType, METADATA_PARAMS(0, nullptr) }; // 3758400079
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetSigilsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetSigilsByType", Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::SigilManagerComponent_eventGetSigilsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::SigilManagerComponent_eventGetSigilsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetSigilsByType)
{
	P_GET_ENUM(ESigilType,Z_Param_Type);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ASigilItem*>*)Z_Param__Result=P_THIS->GetSigilsByType(ESigilType(Z_Param_Type));
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetSigilsByType ****************************

// ********** Begin Class USigilManagerComponent Function GetSystemStatistics **********************
struct Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics
{
	struct SigilManagerComponent_eventGetSystemStatistics_Parms
	{
		FSigilSystemStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m estat\xc3\xadsticas do sistema\n     * @return Estrutura com estat\xc3\xadsticas atuais\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m estat\xc3\xadsticas do sistema\n@return Estrutura com estat\xc3\xadsticas atuais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetSystemStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilSystemStats, METADATA_PARAMS(0, nullptr) }; // 2041897055
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetSystemStatistics", Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::SigilManagerComponent_eventGetSystemStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::SigilManagerComponent_eventGetSystemStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetSystemStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilSystemStats*)Z_Param__Result=P_THIS->GetSystemStatistics();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetSystemStatistics ************************

// ********** Begin Class USigilManagerComponent Function GetTimeToFusion **************************
struct Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics
{
	struct SigilManagerComponent_eventGetTimeToFusion_Parms
	{
		int32 SlotIndex;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m tempo restante para fus\xc3\xa3o\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return Tempo em segundos (0 se pronto)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m tempo restante para fus\xc3\xa3o\n@param SlotIndex - \xc3\x8dndice do slot\n@return Tempo em segundos (0 se pronto)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetTimeToFusion_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventGetTimeToFusion_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "GetTimeToFusion", Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::SigilManagerComponent_eventGetTimeToFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::SigilManagerComponent_eventGetTimeToFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execGetTimeToFusion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeToFusion(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function GetTimeToFusion ****************************

// ********** Begin Class USigilManagerComponent Function HasAvailableSlots ************************
struct Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics
{
	struct SigilManagerComponent_eventHasAvailableSlots_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se h\xc3\xa1 slots dispon\xc3\xadveis\n     * @return true se h\xc3\xa1 slots livres\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se h\xc3\xa1 slots dispon\xc3\xadveis\n@return true se h\xc3\xa1 slots livres" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventHasAvailableSlots_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventHasAvailableSlots_Parms), &Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "HasAvailableSlots", Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::SigilManagerComponent_eventHasAvailableSlots_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::SigilManagerComponent_eventHasAvailableSlots_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execHasAvailableSlots)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasAvailableSlots();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function HasAvailableSlots **************************

// ********** Begin Class USigilManagerComponent Function HasCompatibleSigilsForFusion *************
struct Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics
{
	struct SigilManagerComponent_eventHasCompatibleSigilsForFusion_Parms
	{
		ASigilItem* Sigil;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se h\xc3\xa1 s\xc3\xadgilos compat\xc3\xadveis para fus\xc3\xa3o\n     * @param Sigil - Sigilo de refer\xc3\xaancia\n     * @return true se h\xc3\xa1 s\xc3\xadgilos compat\xc3\xadveis\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se h\xc3\xa1 s\xc3\xadgilos compat\xc3\xadveis para fus\xc3\xa3o\n@param Sigil - Sigilo de refer\xc3\xaancia\n@return true se h\xc3\xa1 s\xc3\xadgilos compat\xc3\xadveis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventHasCompatibleSigilsForFusion_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventHasCompatibleSigilsForFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventHasCompatibleSigilsForFusion_Parms), &Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "HasCompatibleSigilsForFusion", Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::SigilManagerComponent_eventHasCompatibleSigilsForFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::SigilManagerComponent_eventHasCompatibleSigilsForFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execHasCompatibleSigilsForFusion)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasCompatibleSigilsForFusion(Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function HasCompatibleSigilsForFusion ***************

// ********** Begin Class USigilManagerComponent Function IsSigilReadyForFusion ********************
struct Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics
{
	struct SigilManagerComponent_eventIsSigilReadyForFusion_Parms
	{
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se sigilo est\xc3\xa1 pronto para fus\xc3\xa3o\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return true se pronto para fus\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se sigilo est\xc3\xa1 pronto para fus\xc3\xa3o\n@param SlotIndex - \xc3\x8dndice do slot\n@return true se pronto para fus\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventIsSigilReadyForFusion_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventIsSigilReadyForFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventIsSigilReadyForFusion_Parms), &Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "IsSigilReadyForFusion", Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::SigilManagerComponent_eventIsSigilReadyForFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::SigilManagerComponent_eventIsSigilReadyForFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execIsSigilReadyForFusion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSigilReadyForFusion(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function IsSigilReadyForFusion **********************

// ********** Begin Class USigilManagerComponent Function IsSlotAvailable **************************
struct Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics
{
	struct SigilManagerComponent_eventIsSlotAvailable_Parms
	{
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se slot espec\xc3\xad""fico est\xc3\xa1 dispon\xc3\xadvel\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return true se slot est\xc3\xa1 livre\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se slot espec\xc3\xad""fico est\xc3\xa1 dispon\xc3\xadvel\n@param SlotIndex - \xc3\x8dndice do slot\n@return true se slot est\xc3\xa1 livre" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventIsSlotAvailable_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventIsSlotAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventIsSlotAvailable_Parms), &Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "IsSlotAvailable", Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::SigilManagerComponent_eventIsSlotAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::SigilManagerComponent_eventIsSlotAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execIsSlotAvailable)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSlotAvailable(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function IsSlotAvailable ****************************

// ********** Begin Class USigilManagerComponent Function IsSlotUnlocked ***************************
struct Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics
{
	struct SigilManagerComponent_eventIsSlotUnlocked_Parms
	{
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se slot est\xc3\xa1 desbloqueado\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return true se desbloqueado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se slot est\xc3\xa1 desbloqueado\n@param SlotIndex - \xc3\x8dndice do slot\n@return true se desbloqueado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventIsSlotUnlocked_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventIsSlotUnlocked_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventIsSlotUnlocked_Parms), &Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "IsSlotUnlocked", Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::SigilManagerComponent_eventIsSlotUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::SigilManagerComponent_eventIsSlotUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execIsSlotUnlocked)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSlotUnlocked(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function IsSlotUnlocked *****************************

// ********** Begin Class USigilManagerComponent Function IsSystemActive ***************************
struct Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics
{
	struct SigilManagerComponent_eventIsSystemActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se sistema est\xc3\xa1 ativo\n     * @return true se sistema est\xc3\xa1 funcionando\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se sistema est\xc3\xa1 ativo\n@return true se sistema est\xc3\xa1 funcionando" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventIsSystemActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventIsSystemActive_Parms), &Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "IsSystemActive", Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::SigilManagerComponent_eventIsSystemActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::SigilManagerComponent_eventIsSystemActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_IsSystemActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_IsSystemActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execIsSystemActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSystemActive();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function IsSystemActive *****************************

// ********** Begin Class USigilManagerComponent Function IsValidSlotIndex *************************
struct Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics
{
	struct SigilManagerComponent_eventIsValidSlotIndex_Parms
	{
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se slot \xc3\xa9 v\xc3\xa1lido\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return true se slot \xc3\xa9 v\xc3\xa1lido\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se slot \xc3\xa9 v\xc3\xa1lido\n@param SlotIndex - \xc3\x8dndice do slot\n@return true se slot \xc3\xa9 v\xc3\xa1lido" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventIsValidSlotIndex_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventIsValidSlotIndex_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventIsValidSlotIndex_Parms), &Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "IsValidSlotIndex", Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::SigilManagerComponent_eventIsValidSlotIndex_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::SigilManagerComponent_eventIsValidSlotIndex_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execIsValidSlotIndex)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidSlotIndex(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function IsValidSlotIndex ***************************

// ********** Begin Class USigilManagerComponent Function MulticastNotifyFusion ********************
struct SigilManagerComponent_eventMulticastNotifyFusion_Parms
{
	int32 SlotIndex;
	float Multiplier;
};
static FName NAME_USigilManagerComponent_MulticastNotifyFusion = FName(TEXT("MulticastNotifyFusion"));
void USigilManagerComponent::MulticastNotifyFusion(int32 SlotIndex, float Multiplier)
{
	SigilManagerComponent_eventMulticastNotifyFusion_Parms Parms;
	Parms.SlotIndex=SlotIndex;
	Parms.Multiplier=Multiplier;
	UFunction* Func = FindFunctionChecked(NAME_USigilManagerComponent_MulticastNotifyFusion);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para notifica\xc3\xa7\xc3\xa3o de fus\xc3\xa3o (Multicast) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para notifica\xc3\xa7\xc3\xa3o de fus\xc3\xa3o (Multicast)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Multiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventMulticastNotifyFusion_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::NewProp_Multiplier = { "Multiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventMulticastNotifyFusion_Parms, Multiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::NewProp_Multiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "MulticastNotifyFusion", Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::PropPointers), sizeof(SigilManagerComponent_eventMulticastNotifyFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00084CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilManagerComponent_eventMulticastNotifyFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execMulticastNotifyFusion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Multiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyFusion_Implementation(Z_Param_SlotIndex,Z_Param_Multiplier);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function MulticastNotifyFusion **********************

// ********** Begin Class USigilManagerComponent Function MulticastPlayExclusiveAbilityVFX *********
struct SigilManagerComponent_eventMulticastPlayExclusiveAbilityVFX_Parms
{
	ESigilSubType SubType;
	FVector Location;
};
static FName NAME_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX = FName(TEXT("MulticastPlayExclusiveAbilityVFX"));
void USigilManagerComponent::MulticastPlayExclusiveAbilityVFX(ESigilSubType SubType, FVector Location)
{
	SigilManagerComponent_eventMulticastPlayExclusiveAbilityVFX_Parms Parms;
	Parms.SubType=SubType;
	Parms.Location=Location;
	UFunction* Func = FindFunctionChecked(NAME_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::NewProp_SubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::NewProp_SubType = { "SubType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventMulticastPlayExclusiveAbilityVFX_Parms, SubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventMulticastPlayExclusiveAbilityVFX_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::NewProp_SubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::NewProp_SubType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "MulticastPlayExclusiveAbilityVFX", Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::PropPointers), sizeof(SigilManagerComponent_eventMulticastPlayExclusiveAbilityVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00844CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilManagerComponent_eventMulticastPlayExclusiveAbilityVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execMulticastPlayExclusiveAbilityVFX)
{
	P_GET_ENUM(ESigilSubType,Z_Param_SubType);
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastPlayExclusiveAbilityVFX_Implementation(ESigilSubType(Z_Param_SubType),Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function MulticastPlayExclusiveAbilityVFX ***********

// ********** Begin Class USigilManagerComponent Function MulticastPlayVFX *************************
struct SigilManagerComponent_eventMulticastPlayVFX_Parms
{
	int32 SlotIndex;
	FGameplayTag VFXTag;
};
static FName NAME_USigilManagerComponent_MulticastPlayVFX = FName(TEXT("MulticastPlayVFX"));
void USigilManagerComponent::MulticastPlayVFX(int32 SlotIndex, FGameplayTag VFXTag)
{
	SigilManagerComponent_eventMulticastPlayVFX_Parms Parms;
	Parms.SlotIndex=SlotIndex;
	Parms.VFXTag=VFXTag;
	UFunction* Func = FindFunctionChecked(NAME_USigilManagerComponent_MulticastPlayVFX);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para efeitos visuais (Multicast) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para efeitos visuais (Multicast)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VFXTag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventMulticastPlayVFX_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::NewProp_VFXTag = { "VFXTag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventMulticastPlayVFX_Parms, VFXTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::NewProp_VFXTag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "MulticastPlayVFX", Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::PropPointers), sizeof(SigilManagerComponent_eventMulticastPlayVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00084CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilManagerComponent_eventMulticastPlayVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execMulticastPlayVFX)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_GET_STRUCT(FGameplayTag,Z_Param_VFXTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastPlayVFX_Implementation(Z_Param_SlotIndex,Z_Param_VFXTag);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function MulticastPlayVFX ***************************

// ********** Begin Class USigilManagerComponent Function OnRep_SigilSlots *************************
struct Z_Construct_UFunction_USigilManagerComponent_OnRep_SigilSlots_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Chamado quando SigilSlots replica */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chamado quando SigilSlots replica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_OnRep_SigilSlots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "OnRep_SigilSlots", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_OnRep_SigilSlots_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_OnRep_SigilSlots_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilManagerComponent_OnRep_SigilSlots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_OnRep_SigilSlots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execOnRep_SigilSlots)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilSlots();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function OnRep_SigilSlots ***************************

// ********** Begin Class USigilManagerComponent Function OnRep_SystemStats ************************
struct Z_Construct_UFunction_USigilManagerComponent_OnRep_SystemStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Chamado quando SystemStats replica */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chamado quando SystemStats replica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_OnRep_SystemStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "OnRep_SystemStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_OnRep_SystemStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_OnRep_SystemStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilManagerComponent_OnRep_SystemStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_OnRep_SystemStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execOnRep_SystemStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SystemStats();
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function OnRep_SystemStats **************************

// ********** Begin Class USigilManagerComponent Function ReforgeSigil *****************************
struct Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics
{
	struct SigilManagerComponent_eventReforgeSigil_Parms
	{
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reforge sigilo (reset com nova configura\xc3\xa7\xc3\xa3o)\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return true se reforge foi bem-sucedido\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reforge sigilo (reset com nova configura\xc3\xa7\xc3\xa3o)\n@param SlotIndex - \xc3\x8dndice do slot\n@return true se reforge foi bem-sucedido" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventReforgeSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventReforgeSigil_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventReforgeSigil_Parms), &Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ReforgeSigil", Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::SigilManagerComponent_eventReforgeSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::SigilManagerComponent_eventReforgeSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execReforgeSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReforgeSigil(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ReforgeSigil *******************************

// ********** Begin Class USigilManagerComponent Function ServerActivateExclusiveAbility ***********
struct SigilManagerComponent_eventServerActivateExclusiveAbility_Parms
{
	ESigilSubType SubType;
};
static FName NAME_USigilManagerComponent_ServerActivateExclusiveAbility = FName(TEXT("ServerActivateExclusiveAbility"));
void USigilManagerComponent::ServerActivateExclusiveAbility(ESigilSubType SubType)
{
	SigilManagerComponent_eventServerActivateExclusiveAbility_Parms Parms;
	Parms.SubType=SubType;
	UFunction* Func = FindFunctionChecked(NAME_USigilManagerComponent_ServerActivateExclusiveAbility);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fun\xc3\xa7\xc3\xb5""es de rede */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de rede" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::NewProp_SubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::NewProp_SubType = { "SubType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventServerActivateExclusiveAbility_Parms, SubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::NewProp_SubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::NewProp_SubType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ServerActivateExclusiveAbility", Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::PropPointers), sizeof(SigilManagerComponent_eventServerActivateExclusiveAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilManagerComponent_eventServerActivateExclusiveAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execServerActivateExclusiveAbility)
{
	P_GET_ENUM(ESigilSubType,Z_Param_SubType);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerActivateExclusiveAbility_Validate(ESigilSubType(Z_Param_SubType)))
	{
		RPC_ValidateFailed(TEXT("ServerActivateExclusiveAbility_Validate"));
		return;
	}
	P_THIS->ServerActivateExclusiveAbility_Implementation(ESigilSubType(Z_Param_SubType));
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ServerActivateExclusiveAbility *************

// ********** Begin Class USigilManagerComponent Function ServerEquipSigil *************************
struct SigilManagerComponent_eventServerEquipSigil_Parms
{
	ASigilItem* Sigil;
	int32 SlotIndex;
};
static FName NAME_USigilManagerComponent_ServerEquipSigil = FName(TEXT("ServerEquipSigil"));
void USigilManagerComponent::ServerEquipSigil(ASigilItem* Sigil, int32 SlotIndex)
{
	SigilManagerComponent_eventServerEquipSigil_Parms Parms;
	Parms.Sigil=Sigil;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilManagerComponent_ServerEquipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para equipar sigilo (Server) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para equipar sigilo (Server)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventServerEquipSigil_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventServerEquipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ServerEquipSigil", Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::PropPointers), sizeof(SigilManagerComponent_eventServerEquipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilManagerComponent_eventServerEquipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execServerEquipSigil)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerEquipSigil_Validate(Z_Param_Sigil,Z_Param_SlotIndex))
	{
		RPC_ValidateFailed(TEXT("ServerEquipSigil_Validate"));
		return;
	}
	P_THIS->ServerEquipSigil_Implementation(Z_Param_Sigil,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ServerEquipSigil ***************************

// ********** Begin Class USigilManagerComponent Function ServerForceFusion ************************
struct SigilManagerComponent_eventServerForceFusion_Parms
{
	int32 SlotIndex;
};
static FName NAME_USigilManagerComponent_ServerForceFusion = FName(TEXT("ServerForceFusion"));
void USigilManagerComponent::ServerForceFusion(int32 SlotIndex)
{
	SigilManagerComponent_eventServerForceFusion_Parms Parms;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilManagerComponent_ServerForceFusion);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para fus\xc3\xa3o for\xc3\xa7""ada (Server) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para fus\xc3\xa3o for\xc3\xa7""ada (Server)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventServerForceFusion_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ServerForceFusion", Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::PropPointers), sizeof(SigilManagerComponent_eventServerForceFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilManagerComponent_eventServerForceFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execServerForceFusion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerForceFusion_Validate(Z_Param_SlotIndex))
	{
		RPC_ValidateFailed(TEXT("ServerForceFusion_Validate"));
		return;
	}
	P_THIS->ServerForceFusion_Implementation(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ServerForceFusion **************************

// ********** Begin Class USigilManagerComponent Function ServerReforge ****************************
struct SigilManagerComponent_eventServerReforge_Parms
{
	int32 SlotIndex;
};
static FName NAME_USigilManagerComponent_ServerReforge = FName(TEXT("ServerReforge"));
void USigilManagerComponent::ServerReforge(int32 SlotIndex)
{
	SigilManagerComponent_eventServerReforge_Parms Parms;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilManagerComponent_ServerReforge);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para reforge (Server) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para reforge (Server)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventServerReforge_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ServerReforge", Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::PropPointers), sizeof(SigilManagerComponent_eventServerReforge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilManagerComponent_eventServerReforge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ServerReforge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ServerReforge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execServerReforge)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerReforge_Validate(Z_Param_SlotIndex))
	{
		RPC_ValidateFailed(TEXT("ServerReforge_Validate"));
		return;
	}
	P_THIS->ServerReforge_Implementation(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ServerReforge ******************************

// ********** Begin Class USigilManagerComponent Function ServerUnequipSigil ***********************
struct SigilManagerComponent_eventServerUnequipSigil_Parms
{
	int32 SlotIndex;
};
static FName NAME_USigilManagerComponent_ServerUnequipSigil = FName(TEXT("ServerUnequipSigil"));
void USigilManagerComponent::ServerUnequipSigil(int32 SlotIndex)
{
	SigilManagerComponent_eventServerUnequipSigil_Parms Parms;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilManagerComponent_ServerUnequipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para desequipar sigilo (Server) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para desequipar sigilo (Server)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventServerUnequipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "ServerUnequipSigil", Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::PropPointers), sizeof(SigilManagerComponent_eventServerUnequipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilManagerComponent_eventServerUnequipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execServerUnequipSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerUnequipSigil_Validate(Z_Param_SlotIndex))
	{
		RPC_ValidateFailed(TEXT("ServerUnequipSigil_Validate"));
		return;
	}
	P_THIS->ServerUnequipSigil_Implementation(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function ServerUnequipSigil *************************

// ********** Begin Class USigilManagerComponent Function SwapSigils *******************************
struct Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics
{
	struct SigilManagerComponent_eventSwapSigils_Parms
	{
		int32 FromSlot;
		int32 ToSlot;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Troca s\xc3\xadgilos entre dois slots\n     * @param FromSlot - Slot de origem\n     * @param ToSlot - Slot de destino\n     * @return true se troca foi bem-sucedida\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Troca s\xc3\xadgilos entre dois slots\n@param FromSlot - Slot de origem\n@param ToSlot - Slot de destino\n@return true se troca foi bem-sucedida" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_FromSlot;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ToSlot;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::NewProp_FromSlot = { "FromSlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventSwapSigils_Parms, FromSlot), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::NewProp_ToSlot = { "ToSlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventSwapSigils_Parms, ToSlot), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventSwapSigils_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventSwapSigils_Parms), &Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::NewProp_FromSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::NewProp_ToSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "SwapSigils", Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::SigilManagerComponent_eventSwapSigils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::SigilManagerComponent_eventSwapSigils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_SwapSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_SwapSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execSwapSigils)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_FromSlot);
	P_GET_PROPERTY(FIntProperty,Z_Param_ToSlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SwapSigils(Z_Param_FromSlot,Z_Param_ToSlot);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function SwapSigils *********************************

// ********** Begin Class USigilManagerComponent Function TriggerFusionForSigil ********************
struct Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics
{
	struct SigilManagerComponent_eventTriggerFusionForSigil_Parms
	{
		ASigilItem* Sigil;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Dispara fus\xc3\xa3o para sigilo espec\xc3\xad""fico\n     * @param Sigil - Sigilo para fus\xc3\xa3o\n     * @return true se fus\xc3\xa3o foi iniciada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dispara fus\xc3\xa3o para sigilo espec\xc3\xad""fico\n@param Sigil - Sigilo para fus\xc3\xa3o\n@return true se fus\xc3\xa3o foi iniciada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventTriggerFusionForSigil_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventTriggerFusionForSigil_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventTriggerFusionForSigil_Parms), &Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "TriggerFusionForSigil", Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::SigilManagerComponent_eventTriggerFusionForSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::SigilManagerComponent_eventTriggerFusionForSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execTriggerFusionForSigil)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TriggerFusionForSigil(Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function TriggerFusionForSigil **********************

// ********** Begin Class USigilManagerComponent Function UnequipSigil *****************************
struct Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics
{
	struct SigilManagerComponent_eventUnequipSigil_Parms
	{
		int32 SlotIndex;
		ASigilItem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remove sigilo de slot espec\xc3\xad""fico\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return Sigilo removido (pode ser nullptr)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove sigilo de slot espec\xc3\xad""fico\n@param SlotIndex - \xc3\x8dndice do slot\n@return Sigilo removido (pode ser nullptr)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventUnequipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventUnequipSigil_Parms, ReturnValue), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "UnequipSigil", Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::SigilManagerComponent_eventUnequipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::SigilManagerComponent_eventUnequipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_UnequipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_UnequipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execUnequipSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ASigilItem**)Z_Param__Result=P_THIS->UnequipSigil(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function UnequipSigil *******************************

// ********** Begin Class USigilManagerComponent Function UnlockSigilSlot **************************
struct Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics
{
	struct SigilManagerComponent_eventUnlockSigilSlot_Parms
	{
		int32 SlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Desbloqueia slot de sigilo\n     * @param SlotIndex - \xc3\x8dndice do slot\n     * @return true se desbloqueado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desbloqueia slot de sigilo\n@param SlotIndex - \xc3\x8dndice do slot\n@return true se desbloqueado com sucesso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilManagerComponent_eventUnlockSigilSlot_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilManagerComponent_eventUnlockSigilSlot_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilManagerComponent_eventUnlockSigilSlot_Parms), &Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilManagerComponent, nullptr, "UnlockSigilSlot", Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::SigilManagerComponent_eventUnlockSigilSlot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::SigilManagerComponent_eventUnlockSigilSlot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilManagerComponent::execUnlockSigilSlot)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnlockSigilSlot(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilManagerComponent Function UnlockSigilSlot ****************************

// ********** Begin Class USigilManagerComponent ***************************************************
void USigilManagerComponent::StaticRegisterNativesUSigilManagerComponent()
{
	UClass* Class = USigilManagerComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateExclusiveAbility", &USigilManagerComponent::execActivateExclusiveAbility },
		{ "CalculateTotalSigilPower", &USigilManagerComponent::execCalculateTotalSigilPower },
		{ "CanActivateExclusiveAbility", &USigilManagerComponent::execCanActivateExclusiveAbility },
		{ "CanAffordReforge", &USigilManagerComponent::execCanAffordReforge },
		{ "CanEquipSigil", &USigilManagerComponent::execCanEquipSigil },
		{ "CanReforge", &USigilManagerComponent::execCanReforge },
		{ "ConsumeReforgeResources", &USigilManagerComponent::execConsumeReforgeResources },
		{ "DEBUG_EquipRandomSigil", &USigilManagerComponent::execDEBUG_EquipRandomSigil },
		{ "DEBUG_ForceActivateExclusiveAbility", &USigilManagerComponent::execDEBUG_ForceActivateExclusiveAbility },
		{ "DEBUG_ForceAllFusions", &USigilManagerComponent::execDEBUG_ForceAllFusions },
		{ "DEBUG_PrintSystemInfo", &USigilManagerComponent::execDEBUG_PrintSystemInfo },
		{ "DEBUG_ResetSystem", &USigilManagerComponent::execDEBUG_ResetSystem },
		{ "DEBUG_UnlockAllSlots", &USigilManagerComponent::execDEBUG_UnlockAllSlots },
		{ "EquipSigil", &USigilManagerComponent::execEquipSigil },
		{ "ForceFuseSigil", &USigilManagerComponent::execForceFuseSigil },
		{ "GetAllEquippedSigils", &USigilManagerComponent::execGetAllEquippedSigils },
		{ "GetAvailableExclusiveAbilities", &USigilManagerComponent::execGetAvailableExclusiveAbilities },
		{ "GetEquippedSigil", &USigilManagerComponent::execGetEquippedSigil },
		{ "GetExclusiveAbilityCooldown", &USigilManagerComponent::execGetExclusiveAbilityCooldown },
		{ "GetFusionProgress", &USigilManagerComponent::execGetFusionProgress },
		{ "GetReforgeTimeRemaining", &USigilManagerComponent::execGetReforgeTimeRemaining },
		{ "GetSigilByID", &USigilManagerComponent::execGetSigilByID },
		{ "GetSigilsByRarity", &USigilManagerComponent::execGetSigilsByRarity },
		{ "GetSigilsByType", &USigilManagerComponent::execGetSigilsByType },
		{ "GetSystemStatistics", &USigilManagerComponent::execGetSystemStatistics },
		{ "GetTimeToFusion", &USigilManagerComponent::execGetTimeToFusion },
		{ "HasAvailableSlots", &USigilManagerComponent::execHasAvailableSlots },
		{ "HasCompatibleSigilsForFusion", &USigilManagerComponent::execHasCompatibleSigilsForFusion },
		{ "IsSigilReadyForFusion", &USigilManagerComponent::execIsSigilReadyForFusion },
		{ "IsSlotAvailable", &USigilManagerComponent::execIsSlotAvailable },
		{ "IsSlotUnlocked", &USigilManagerComponent::execIsSlotUnlocked },
		{ "IsSystemActive", &USigilManagerComponent::execIsSystemActive },
		{ "IsValidSlotIndex", &USigilManagerComponent::execIsValidSlotIndex },
		{ "MulticastNotifyFusion", &USigilManagerComponent::execMulticastNotifyFusion },
		{ "MulticastPlayExclusiveAbilityVFX", &USigilManagerComponent::execMulticastPlayExclusiveAbilityVFX },
		{ "MulticastPlayVFX", &USigilManagerComponent::execMulticastPlayVFX },
		{ "OnRep_SigilSlots", &USigilManagerComponent::execOnRep_SigilSlots },
		{ "OnRep_SystemStats", &USigilManagerComponent::execOnRep_SystemStats },
		{ "ReforgeSigil", &USigilManagerComponent::execReforgeSigil },
		{ "ServerActivateExclusiveAbility", &USigilManagerComponent::execServerActivateExclusiveAbility },
		{ "ServerEquipSigil", &USigilManagerComponent::execServerEquipSigil },
		{ "ServerForceFusion", &USigilManagerComponent::execServerForceFusion },
		{ "ServerReforge", &USigilManagerComponent::execServerReforge },
		{ "ServerUnequipSigil", &USigilManagerComponent::execServerUnequipSigil },
		{ "SwapSigils", &USigilManagerComponent::execSwapSigils },
		{ "TriggerFusionForSigil", &USigilManagerComponent::execTriggerFusionForSigil },
		{ "UnequipSigil", &USigilManagerComponent::execUnequipSigil },
		{ "UnlockSigilSlot", &USigilManagerComponent::execUnlockSigilSlot },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilManagerComponent;
UClass* USigilManagerComponent::GetPrivateStaticClass()
{
	using TClass = USigilManagerComponent;
	if (!Z_Registration_Info_UClass_USigilManagerComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilManagerComponent"),
			Z_Registration_Info_UClass_USigilManagerComponent.InnerSingleton,
			StaticRegisterNativesUSigilManagerComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilManagerComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilManagerComponent_NoRegister()
{
	return USigilManagerComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilManagerComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Componente principal para gerenciar sistema de s\xc3\xadgilos\n * Respons\xc3\xa1vel por equipar, desequipar, fus\xc3\xa3o autom\xc3\xa1tica e replica\xc3\xa7\xc3\xa3o\n * Suporta MOBA 5x5 com 10 jogadores simult\xc3\xa2neos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilManagerComponent.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente principal para gerenciar sistema de s\xc3\xadgilos\nRespons\xc3\xa1vel por equipar, desequipar, fus\xc3\xa3o autom\xc3\xa1tica e replica\xc3\xa7\xc3\xa3o\nSuporta MOBA 5x5 com 10 jogadores simult\xc3\xa2neos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionConfig_MetaData[] = {
		{ "Category", "Sigil System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o de fus\xc3\xa3o de s\xc3\xadgilos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de fus\xc3\xa3o de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSigilSlots_MetaData[] = {
		{ "Category", "Sigil System" },
		{ "ClampMax", "6" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de slots de sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de slots de sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoFusionEnabled_MetaData[] = {
		{ "Category", "Sigil System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se fus\xc3\xa3o autom\xc3\xa1tica est\xc3\xa1 habilitada */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se fus\xc3\xa3o autom\xc3\xa1tica est\xc3\xa1 habilitada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bVisualNotificationsEnabled_MetaData[] = {
		{ "Category", "Sigil System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se notifica\xc3\xa7\xc3\xb5""es visuais est\xc3\xa3o habilitadas */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se notifica\xc3\xa7\xc3\xb5""es visuais est\xc3\xa3o habilitadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReforgeCooldownSeconds_MetaData[] = {
		{ "Category", "Sigil System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown para reforge em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown para reforge em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SystemTags_MetaData[] = {
		{ "Category", "Sigil System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GameplayTags do sistema */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayTags do sistema" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilSlots_MetaData[] = {
		{ "Category", "Sigil Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Array de slots de sigilo (replicado) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Array de slots de sigilo (replicado)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SystemStats_MetaData[] = {
		{ "Category", "Sigil Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estat\xc3\xadsticas do sistema (replicado) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas do sistema (replicado)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastReforgeTimestamp_MetaData[] = {
		{ "Category", "Sigil Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp do \xc3\xbaltimo reforge */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp do \xc3\xbaltimo reforge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSystemActive_MetaData[] = {
		{ "Category", "Sigil Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o sistema est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o sistema est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilEvent_MetaData[] = {
		{ "Category", "Sigil Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando sigilo \xc3\xa9 equipado/desequipado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando sigilo \xc3\xa9 equipado/desequipado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilFusion_MetaData[] = {
		{ "Category", "Sigil Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando fus\xc3\xa3o ocorre */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando fus\xc3\xa3o ocorre" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilSlotUnlocked_MetaData[] = {
		{ "Category", "Sigil Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando slot \xc3\xa9 desbloqueado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando slot \xc3\xa9 desbloqueado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilStatsChanged_MetaData[] = {
		{ "Category", "Sigil Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando estat\xc3\xadsticas mudam */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando estat\xc3\xadsticas mudam" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnExclusiveAbilityActivated_MetaData[] = {
		{ "Category", "Sigil Events" },
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnExclusiveAbilityCooldownChanged_MetaData[] = {
		{ "Category", "Sigil Events" },
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedAbilitySystemComponent_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cache do AbilitySystemComponent */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache do AbilitySystemComponent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedSigilAttributeSet_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cache do SigilAttributeSet */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache do SigilAttributeSet" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusiveAbilityHandles_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Exclusive Abilities Management\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Exclusive Abilities Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusiveAbilityCooldowns_MetaData[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusiveAbilityClasses_MetaData[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotVFXComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de VFX para cada slot */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilManagerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de VFX para cada slot" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionConfig;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSigilSlots;
	static void NewProp_bAutoFusionEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoFusionEnabled;
	static void NewProp_bVisualNotificationsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisualNotificationsEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReforgeCooldownSeconds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SystemTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilSlots_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SigilSlots;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SystemStats;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastReforgeTimestamp;
	static void NewProp_bSystemActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSystemActive;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilEvent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilFusion;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilSlotUnlocked;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilStatsChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnExclusiveAbilityActivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnExclusiveAbilityCooldownChanged;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedAbilitySystemComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedSigilAttributeSet;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExclusiveAbilityHandles_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExclusiveAbilityHandles_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExclusiveAbilityHandles_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ExclusiveAbilityHandles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExclusiveAbilityCooldowns_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExclusiveAbilityCooldowns_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExclusiveAbilityCooldowns_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ExclusiveAbilityCooldowns;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ExclusiveAbilityClasses_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExclusiveAbilityClasses_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExclusiveAbilityClasses_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ExclusiveAbilityClasses;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SlotVFXComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SlotVFXComponents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilManagerComponent_ActivateExclusiveAbility, "ActivateExclusiveAbility" }, // 3390667925
		{ &Z_Construct_UFunction_USigilManagerComponent_CalculateTotalSigilPower, "CalculateTotalSigilPower" }, // 3231369176
		{ &Z_Construct_UFunction_USigilManagerComponent_CanActivateExclusiveAbility, "CanActivateExclusiveAbility" }, // 1573141616
		{ &Z_Construct_UFunction_USigilManagerComponent_CanAffordReforge, "CanAffordReforge" }, // 1945642251
		{ &Z_Construct_UFunction_USigilManagerComponent_CanEquipSigil, "CanEquipSigil" }, // 1673969342
		{ &Z_Construct_UFunction_USigilManagerComponent_CanReforge, "CanReforge" }, // 1347051888
		{ &Z_Construct_UFunction_USigilManagerComponent_ConsumeReforgeResources, "ConsumeReforgeResources" }, // 2282472352
		{ &Z_Construct_UFunction_USigilManagerComponent_DEBUG_EquipRandomSigil, "DEBUG_EquipRandomSigil" }, // 409074799
		{ &Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceActivateExclusiveAbility, "DEBUG_ForceActivateExclusiveAbility" }, // 2560694808
		{ &Z_Construct_UFunction_USigilManagerComponent_DEBUG_ForceAllFusions, "DEBUG_ForceAllFusions" }, // 1236303731
		{ &Z_Construct_UFunction_USigilManagerComponent_DEBUG_PrintSystemInfo, "DEBUG_PrintSystemInfo" }, // 3373036062
		{ &Z_Construct_UFunction_USigilManagerComponent_DEBUG_ResetSystem, "DEBUG_ResetSystem" }, // 2535581193
		{ &Z_Construct_UFunction_USigilManagerComponent_DEBUG_UnlockAllSlots, "DEBUG_UnlockAllSlots" }, // 2699979945
		{ &Z_Construct_UFunction_USigilManagerComponent_EquipSigil, "EquipSigil" }, // 1506634691
		{ &Z_Construct_UFunction_USigilManagerComponent_ForceFuseSigil, "ForceFuseSigil" }, // 3118296998
		{ &Z_Construct_UFunction_USigilManagerComponent_GetAllEquippedSigils, "GetAllEquippedSigils" }, // 3668332887
		{ &Z_Construct_UFunction_USigilManagerComponent_GetAvailableExclusiveAbilities, "GetAvailableExclusiveAbilities" }, // 22445950
		{ &Z_Construct_UFunction_USigilManagerComponent_GetEquippedSigil, "GetEquippedSigil" }, // 286254656
		{ &Z_Construct_UFunction_USigilManagerComponent_GetExclusiveAbilityCooldown, "GetExclusiveAbilityCooldown" }, // 1023136539
		{ &Z_Construct_UFunction_USigilManagerComponent_GetFusionProgress, "GetFusionProgress" }, // 2905542136
		{ &Z_Construct_UFunction_USigilManagerComponent_GetReforgeTimeRemaining, "GetReforgeTimeRemaining" }, // 2874481868
		{ &Z_Construct_UFunction_USigilManagerComponent_GetSigilByID, "GetSigilByID" }, // 804672518
		{ &Z_Construct_UFunction_USigilManagerComponent_GetSigilsByRarity, "GetSigilsByRarity" }, // 798452456
		{ &Z_Construct_UFunction_USigilManagerComponent_GetSigilsByType, "GetSigilsByType" }, // 4167011281
		{ &Z_Construct_UFunction_USigilManagerComponent_GetSystemStatistics, "GetSystemStatistics" }, // 1026411031
		{ &Z_Construct_UFunction_USigilManagerComponent_GetTimeToFusion, "GetTimeToFusion" }, // 384068152
		{ &Z_Construct_UFunction_USigilManagerComponent_HasAvailableSlots, "HasAvailableSlots" }, // 3575872388
		{ &Z_Construct_UFunction_USigilManagerComponent_HasCompatibleSigilsForFusion, "HasCompatibleSigilsForFusion" }, // 3043526724
		{ &Z_Construct_UFunction_USigilManagerComponent_IsSigilReadyForFusion, "IsSigilReadyForFusion" }, // 1101582470
		{ &Z_Construct_UFunction_USigilManagerComponent_IsSlotAvailable, "IsSlotAvailable" }, // 2047190000
		{ &Z_Construct_UFunction_USigilManagerComponent_IsSlotUnlocked, "IsSlotUnlocked" }, // 2645514657
		{ &Z_Construct_UFunction_USigilManagerComponent_IsSystemActive, "IsSystemActive" }, // 3320099129
		{ &Z_Construct_UFunction_USigilManagerComponent_IsValidSlotIndex, "IsValidSlotIndex" }, // 1314158926
		{ &Z_Construct_UFunction_USigilManagerComponent_MulticastNotifyFusion, "MulticastNotifyFusion" }, // 3339438104
		{ &Z_Construct_UFunction_USigilManagerComponent_MulticastPlayExclusiveAbilityVFX, "MulticastPlayExclusiveAbilityVFX" }, // 3110790453
		{ &Z_Construct_UFunction_USigilManagerComponent_MulticastPlayVFX, "MulticastPlayVFX" }, // 3244248360
		{ &Z_Construct_UFunction_USigilManagerComponent_OnRep_SigilSlots, "OnRep_SigilSlots" }, // 2107309838
		{ &Z_Construct_UFunction_USigilManagerComponent_OnRep_SystemStats, "OnRep_SystemStats" }, // 2027625072
		{ &Z_Construct_UFunction_USigilManagerComponent_ReforgeSigil, "ReforgeSigil" }, // 426990213
		{ &Z_Construct_UFunction_USigilManagerComponent_ServerActivateExclusiveAbility, "ServerActivateExclusiveAbility" }, // 3878853002
		{ &Z_Construct_UFunction_USigilManagerComponent_ServerEquipSigil, "ServerEquipSigil" }, // 3023186563
		{ &Z_Construct_UFunction_USigilManagerComponent_ServerForceFusion, "ServerForceFusion" }, // 4294025394
		{ &Z_Construct_UFunction_USigilManagerComponent_ServerReforge, "ServerReforge" }, // 1588933192
		{ &Z_Construct_UFunction_USigilManagerComponent_ServerUnequipSigil, "ServerUnequipSigil" }, // 857022152
		{ &Z_Construct_UFunction_USigilManagerComponent_SwapSigils, "SwapSigils" }, // 3800888017
		{ &Z_Construct_UFunction_USigilManagerComponent_TriggerFusionForSigil, "TriggerFusionForSigil" }, // 394933674
		{ &Z_Construct_UFunction_USigilManagerComponent_UnequipSigil, "UnequipSigil" }, // 4166005311
		{ &Z_Construct_UFunction_USigilManagerComponent_UnlockSigilSlot, "UnlockSigilSlot" }, // 3629308574
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilManagerComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_FusionConfig = { "FusionConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, FusionConfig), Z_Construct_UScriptStruct_FSigilFusionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionConfig_MetaData), NewProp_FusionConfig_MetaData) }; // 1102162724
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_MaxSigilSlots = { "MaxSigilSlots", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, MaxSigilSlots), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSigilSlots_MetaData), NewProp_MaxSigilSlots_MetaData) };
void Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bAutoFusionEnabled_SetBit(void* Obj)
{
	((USigilManagerComponent*)Obj)->bAutoFusionEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bAutoFusionEnabled = { "bAutoFusionEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilManagerComponent), &Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bAutoFusionEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoFusionEnabled_MetaData), NewProp_bAutoFusionEnabled_MetaData) };
void Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bVisualNotificationsEnabled_SetBit(void* Obj)
{
	((USigilManagerComponent*)Obj)->bVisualNotificationsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bVisualNotificationsEnabled = { "bVisualNotificationsEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilManagerComponent), &Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bVisualNotificationsEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bVisualNotificationsEnabled_MetaData), NewProp_bVisualNotificationsEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ReforgeCooldownSeconds = { "ReforgeCooldownSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, ReforgeCooldownSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReforgeCooldownSeconds_MetaData), NewProp_ReforgeCooldownSeconds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SystemTags = { "SystemTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, SystemTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SystemTags_MetaData), NewProp_SystemTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SigilSlots_Inner = { "SigilSlots", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilSlotData, METADATA_PARAMS(0, nullptr) }; // 1763786531
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SigilSlots = { "SigilSlots", "OnRep_SigilSlots", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, SigilSlots), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilSlots_MetaData), NewProp_SigilSlots_MetaData) }; // 1763786531
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SystemStats = { "SystemStats", "OnRep_SystemStats", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, SystemStats), Z_Construct_UScriptStruct_FSigilSystemStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SystemStats_MetaData), NewProp_SystemStats_MetaData) }; // 2041897055
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_LastReforgeTimestamp = { "LastReforgeTimestamp", nullptr, (EPropertyFlags)0x0010000000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, LastReforgeTimestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastReforgeTimestamp_MetaData), NewProp_LastReforgeTimestamp_MetaData) };
void Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bSystemActive_SetBit(void* Obj)
{
	((USigilManagerComponent*)Obj)->bSystemActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bSystemActive = { "bSystemActive", nullptr, (EPropertyFlags)0x0010000000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilManagerComponent), &Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bSystemActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSystemActive_MetaData), NewProp_bSystemActive_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnSigilEvent = { "OnSigilEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, OnSigilEvent), Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilEvent_MetaData), NewProp_OnSigilEvent_MetaData) }; // 641765176
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnSigilFusion = { "OnSigilFusion", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, OnSigilFusion), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilFusion_MetaData), NewProp_OnSigilFusion_MetaData) }; // 1774469904
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnSigilSlotUnlocked = { "OnSigilSlotUnlocked", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, OnSigilSlotUnlocked), Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilSlotUnlocked_MetaData), NewProp_OnSigilSlotUnlocked_MetaData) }; // 4132404191
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnSigilStatsChanged = { "OnSigilStatsChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, OnSigilStatsChanged), Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilStatsChanged_MetaData), NewProp_OnSigilStatsChanged_MetaData) }; // 1959274235
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnExclusiveAbilityActivated = { "OnExclusiveAbilityActivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, OnExclusiveAbilityActivated), Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnExclusiveAbilityActivated_MetaData), NewProp_OnExclusiveAbilityActivated_MetaData) }; // 3678603168
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnExclusiveAbilityCooldownChanged = { "OnExclusiveAbilityCooldownChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, OnExclusiveAbilityCooldownChanged), Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnExclusiveAbilityCooldownChanged_MetaData), NewProp_OnExclusiveAbilityCooldownChanged_MetaData) }; // 3238116691
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_CachedAbilitySystemComponent = { "CachedAbilitySystemComponent", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, CachedAbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedAbilitySystemComponent_MetaData), NewProp_CachedAbilitySystemComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_CachedSigilAttributeSet = { "CachedSigilAttributeSet", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, CachedSigilAttributeSet), Z_Construct_UClass_USigilAttributeSet_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedSigilAttributeSet_MetaData), NewProp_CachedSigilAttributeSet_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityHandles_ValueProp = { "ExclusiveAbilityHandles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FGameplayAbilitySpecHandle, METADATA_PARAMS(0, nullptr) }; // 417001783
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityHandles_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityHandles_Key_KeyProp = { "ExclusiveAbilityHandles_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityHandles = { "ExclusiveAbilityHandles", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, ExclusiveAbilityHandles), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusiveAbilityHandles_MetaData), NewProp_ExclusiveAbilityHandles_MetaData) }; // 3161995902 417001783
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityCooldowns_ValueProp = { "ExclusiveAbilityCooldowns", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityCooldowns_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityCooldowns_Key_KeyProp = { "ExclusiveAbilityCooldowns_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityCooldowns = { "ExclusiveAbilityCooldowns", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, ExclusiveAbilityCooldowns), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusiveAbilityCooldowns_MetaData), NewProp_ExclusiveAbilityCooldowns_MetaData) }; // 3161995902
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityClasses_ValueProp = { "ExclusiveAbilityClasses", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UClass, Z_Construct_UClass_USigilAbilityBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityClasses_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityClasses_Key_KeyProp = { "ExclusiveAbilityClasses_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityClasses = { "ExclusiveAbilityClasses", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, ExclusiveAbilityClasses), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusiveAbilityClasses_MetaData), NewProp_ExclusiveAbilityClasses_MetaData) }; // 3161995902
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SlotVFXComponents_Inner = { "SlotVFXComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SlotVFXComponents = { "SlotVFXComponents", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilManagerComponent, SlotVFXComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotVFXComponents_MetaData), NewProp_SlotVFXComponents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilManagerComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_FusionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_MaxSigilSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bAutoFusionEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bVisualNotificationsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ReforgeCooldownSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SystemTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SigilSlots_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SigilSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SystemStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_LastReforgeTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_bSystemActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnSigilEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnSigilFusion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnSigilSlotUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnSigilStatsChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnExclusiveAbilityActivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_OnExclusiveAbilityCooldownChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_CachedAbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_CachedSigilAttributeSet,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityHandles_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityHandles_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityHandles_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityHandles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityCooldowns_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityCooldowns_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityCooldowns_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityCooldowns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityClasses_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityClasses_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityClasses_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_ExclusiveAbilityClasses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SlotVFXComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilManagerComponent_Statics::NewProp_SlotVFXComponents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilManagerComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilManagerComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilManagerComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilManagerComponent_Statics::ClassParams = {
	&USigilManagerComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilManagerComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilManagerComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilManagerComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilManagerComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilManagerComponent()
{
	if (!Z_Registration_Info_UClass_USigilManagerComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilManagerComponent.OuterSingleton, Z_Construct_UClass_USigilManagerComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilManagerComponent.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void USigilManagerComponent::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_SigilSlots(TEXT("SigilSlots"));
	static FName Name_SystemStats(TEXT("SystemStats"));
	static FName Name_LastReforgeTimestamp(TEXT("LastReforgeTimestamp"));
	static FName Name_bSystemActive(TEXT("bSystemActive"));
	const bool bIsValid = true
		&& Name_SigilSlots == ClassReps[(int32)ENetFields_Private::SigilSlots].Property->GetFName()
		&& Name_SystemStats == ClassReps[(int32)ENetFields_Private::SystemStats].Property->GetFName()
		&& Name_LastReforgeTimestamp == ClassReps[(int32)ENetFields_Private::LastReforgeTimestamp].Property->GetFName()
		&& Name_bSystemActive == ClassReps[(int32)ENetFields_Private::bSystemActive].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in USigilManagerComponent"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilManagerComponent);
USigilManagerComponent::~USigilManagerComponent() {}
// ********** End Class USigilManagerComponent *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilSlotData::StaticStruct, Z_Construct_UScriptStruct_FSigilSlotData_Statics::NewStructOps, TEXT("SigilSlotData"), &Z_Registration_Info_UScriptStruct_FSigilSlotData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilSlotData), 1763786531U) },
		{ FSigilFusionConfig::StaticStruct, Z_Construct_UScriptStruct_FSigilFusionConfig_Statics::NewStructOps, TEXT("SigilFusionConfig"), &Z_Registration_Info_UScriptStruct_FSigilFusionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilFusionConfig), 1102162724U) },
		{ FSigilSystemStats::StaticStruct, Z_Construct_UScriptStruct_FSigilSystemStats_Statics::NewStructOps, TEXT("SigilSystemStats"), &Z_Registration_Info_UScriptStruct_FSigilSystemStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilSystemStats), 2041897055U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilManagerComponent, USigilManagerComponent::StaticClass, TEXT("USigilManagerComponent"), &Z_Registration_Info_UClass_USigilManagerComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilManagerComponent), 2989629304U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h__Script_AURACRON_2335755433(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilManagerComponent_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
