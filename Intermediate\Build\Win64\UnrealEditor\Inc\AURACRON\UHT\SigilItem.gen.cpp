// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SigilItem.h"
#include "ActiveGameplayEffectHandle.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilItem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ASigilItem();
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAttributeSet_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilDataAsset();
AURACRON_API UClass* Z_Construct_UClass_USigilDataAsset_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilPropertyType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilSubType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilType();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilProperty();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilSpectralBonus();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UDataAsset();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemInterface_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayAbility_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FActiveGameplayEffectHandle();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ESigilType ****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilType;
static UEnum* ESigilType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilType"));
	}
	return Z_Registration_Info_UEnum_ESigilType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilType>()
{
	return ESigilType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum para tipos de s\xc3\xadgilos espectrais\n * Usado para categorizar s\xc3\xadgilos em Tank, Damage e Utility\n */" },
#endif
		{ "Damage.DisplayName", "Damage - Offensive Focus" },
		{ "Damage.Name", "ESigilType::Damage" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ESigilType::None" },
		{ "Tank.DisplayName", "Tank - Defensive Focus" },
		{ "Tank.Name", "ESigilType::Tank" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para tipos de s\xc3\xadgilos espectrais\nUsado para categorizar s\xc3\xadgilos em Tank, Damage e Utility" },
#endif
		{ "Utility.DisplayName", "Utility - Support Focus" },
		{ "Utility.Name", "ESigilType::Utility" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilType::None", (int64)ESigilType::None },
		{ "ESigilType::Tank", (int64)ESigilType::Tank },
		{ "ESigilType::Damage", (int64)ESigilType::Damage },
		{ "ESigilType::Utility", (int64)ESigilType::Utility },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilType",
	"ESigilType",
	Z_Construct_UEnum_AURACRON_ESigilType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilType()
{
	if (!Z_Registration_Info_UEnum_ESigilType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilType.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilType.InnerSingleton;
}
// ********** End Enum ESigilType ******************************************************************

// ********** Begin Enum ESigilSubType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilSubType;
static UEnum* ESigilSubType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilSubType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilSubType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilSubType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilSubType"));
	}
	return Z_Registration_Info_UEnum_ESigilSubType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilSubType>()
{
	return ESigilSubType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilSubType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aegis.Comment", "// Tank Subtypes\n" },
		{ "Aegis.DisplayName", "Aegis - Frontliner/Iniciado" },
		{ "Aegis.Name", "ESigilSubType::Aegis" },
		{ "Aegis.ToolTip", "Tank Subtypes" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum para subtipos espec\xc3\xad""ficos de s\xc3\xadgilos\n * Implementa os tipos Aegis, Ruin e Vesper do documento de design\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ESigilSubType::None" },
		{ "Ruin.Comment", "// Damage Subtypes  \n" },
		{ "Ruin.DisplayName", "Ruin - Burst/Skirmisher" },
		{ "Ruin.Name", "ESigilSubType::Ruin" },
		{ "Ruin.ToolTip", "Damage Subtypes" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para subtipos espec\xc3\xad""ficos de s\xc3\xadgilos\nImplementa os tipos Aegis, Ruin e Vesper do documento de design" },
#endif
		{ "Vesper.Comment", "// Utility Subtypes\n" },
		{ "Vesper.DisplayName", "Vesper - Roamer/Suporte" },
		{ "Vesper.Name", "ESigilSubType::Vesper" },
		{ "Vesper.ToolTip", "Utility Subtypes" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilSubType::None", (int64)ESigilSubType::None },
		{ "ESigilSubType::Aegis", (int64)ESigilSubType::Aegis },
		{ "ESigilSubType::Ruin", (int64)ESigilSubType::Ruin },
		{ "ESigilSubType::Vesper", (int64)ESigilSubType::Vesper },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilSubType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilSubType",
	"ESigilSubType",
	Z_Construct_UEnum_AURACRON_ESigilSubType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilSubType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilSubType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilSubType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilSubType()
{
	if (!Z_Registration_Info_UEnum_ESigilSubType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilSubType.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilSubType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilSubType.InnerSingleton;
}
// ********** End Enum ESigilSubType ***************************************************************

// ********** Begin Enum ESigilRarity **************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilRarity;
static UEnum* ESigilRarity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilRarity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilRarity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilRarity, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilRarity"));
	}
	return Z_Registration_Info_UEnum_ESigilRarity.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilRarity>()
{
	return ESigilRarity_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilRarity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum para raridade dos s\xc3\xadgilos\n * Determina a pot\xc3\xaancia dos efeitos e apar\xc3\xaancia visual\n */" },
#endif
		{ "Common.DisplayName", "Common - Basic Effects" },
		{ "Common.Name", "ESigilRarity::Common" },
		{ "Epic.DisplayName", "Epic - High Effects" },
		{ "Epic.Name", "ESigilRarity::Epic" },
		{ "Legendary.DisplayName", "Legendary - Maximum Effects" },
		{ "Legendary.Name", "ESigilRarity::Legendary" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
		{ "Rare.DisplayName", "Rare - Moderate Effects" },
		{ "Rare.Name", "ESigilRarity::Rare" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para raridade dos s\xc3\xadgilos\nDetermina a pot\xc3\xaancia dos efeitos e apar\xc3\xaancia visual" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilRarity::Common", (int64)ESigilRarity::Common },
		{ "ESigilRarity::Rare", (int64)ESigilRarity::Rare },
		{ "ESigilRarity::Epic", (int64)ESigilRarity::Epic },
		{ "ESigilRarity::Legendary", (int64)ESigilRarity::Legendary },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilRarity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilRarity",
	"ESigilRarity",
	Z_Construct_UEnum_AURACRON_ESigilRarity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilRarity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilRarity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilRarity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity()
{
	if (!Z_Registration_Info_UEnum_ESigilRarity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilRarity.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilRarity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilRarity.InnerSingleton;
}
// ********** End Enum ESigilRarity ****************************************************************

// ********** Begin Enum ESigilState ***************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilState;
static UEnum* ESigilState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilState"));
	}
	return Z_Registration_Info_UEnum_ESigilState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilState>()
{
	return ESigilState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum para estado do sigilo\n * Controla quando e como o sigilo pode ser modificado\n */" },
#endif
		{ "Equipped.DisplayName", "Equipped" },
		{ "Equipped.Name", "ESigilState::Equipped" },
		{ "Fused.DisplayName", "Fused - Enhanced" },
		{ "Fused.Name", "ESigilState::Fused" },
		{ "Locked.DisplayName", "Locked - Cannot Modify" },
		{ "Locked.Name", "ESigilState::Locked" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para estado do sigilo\nControla quando e como o sigilo pode ser modificado" },
#endif
		{ "Unequipped.DisplayName", "Unequipped" },
		{ "Unequipped.Name", "ESigilState::Unequipped" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilState::Unequipped", (int64)ESigilState::Unequipped },
		{ "ESigilState::Equipped", (int64)ESigilState::Equipped },
		{ "ESigilState::Fused", (int64)ESigilState::Fused },
		{ "ESigilState::Locked", (int64)ESigilState::Locked },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilState",
	"ESigilState",
	Z_Construct_UEnum_AURACRON_ESigilState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilState()
{
	if (!Z_Registration_Info_UEnum_ESigilState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilState.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilState.InnerSingleton;
}
// ********** End Enum ESigilState *****************************************************************

// ********** Begin Enum ESigilPropertyType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilPropertyType;
static UEnum* ESigilPropertyType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilPropertyType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilPropertyType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilPropertyType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilPropertyType"));
	}
	return Z_Registration_Info_UEnum_ESigilPropertyType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilPropertyType>()
{
	return ESigilPropertyType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilPropertyType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Additive.DisplayName", "Additive" },
		{ "Additive.Name", "ESigilPropertyType::Additive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum para tipos de propriedades de s\xc3\xadgilos\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
		{ "Multiplicative.DisplayName", "Multiplicative" },
		{ "Multiplicative.Name", "ESigilPropertyType::Multiplicative" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ESigilPropertyType::None" },
		{ "Override.DisplayName", "Override" },
		{ "Override.Name", "ESigilPropertyType::Override" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para tipos de propriedades de s\xc3\xadgilos" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilPropertyType::None", (int64)ESigilPropertyType::None },
		{ "ESigilPropertyType::Additive", (int64)ESigilPropertyType::Additive },
		{ "ESigilPropertyType::Multiplicative", (int64)ESigilPropertyType::Multiplicative },
		{ "ESigilPropertyType::Override", (int64)ESigilPropertyType::Override },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilPropertyType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilPropertyType",
	"ESigilPropertyType",
	Z_Construct_UEnum_AURACRON_ESigilPropertyType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilPropertyType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilPropertyType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilPropertyType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilPropertyType()
{
	if (!Z_Registration_Info_UEnum_ESigilPropertyType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilPropertyType.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilPropertyType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilPropertyType.InnerSingleton;
}
// ********** End Enum ESigilPropertyType **********************************************************

// ********** Begin ScriptStruct FSigilProperty ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilProperty;
class UScriptStruct* FSigilProperty::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilProperty.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilProperty.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilProperty, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilProperty"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilProperty.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilProperty_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para propriedades din\xc3\xa2micas de s\xc3\xadgilos\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para propriedades din\xc3\xa2micas de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PropertyName_MetaData[] = {
		{ "Category", "Property" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "Category", "Property" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PropertyType_MetaData[] = {
		{ "Category", "Property" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PropertyName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PropertyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PropertyType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilProperty>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FSigilProperty_Statics::NewProp_PropertyName = { "PropertyName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilProperty, PropertyName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PropertyName_MetaData), NewProp_PropertyName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilProperty_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilProperty, Value), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilProperty_Statics::NewProp_PropertyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilProperty_Statics::NewProp_PropertyType = { "PropertyType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilProperty, PropertyType), Z_Construct_UEnum_AURACRON_ESigilPropertyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PropertyType_MetaData), NewProp_PropertyType_MetaData) }; // 177189723
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilProperty_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilProperty_Statics::NewProp_PropertyName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilProperty_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilProperty_Statics::NewProp_PropertyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilProperty_Statics::NewProp_PropertyType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilProperty_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilProperty_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilProperty",
	Z_Construct_UScriptStruct_FSigilProperty_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilProperty_Statics::PropPointers),
	sizeof(FSigilProperty),
	alignof(FSigilProperty),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilProperty_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilProperty_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilProperty()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilProperty.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilProperty.InnerSingleton, Z_Construct_UScriptStruct_FSigilProperty_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilProperty.InnerSingleton;
}
// ********** End ScriptStruct FSigilProperty ******************************************************

// ********** Begin ScriptStruct FSigilSpectralBonus ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilSpectralBonus;
class UScriptStruct* FSigilSpectralBonus::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilSpectralBonus.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilSpectralBonus.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilSpectralBonus, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilSpectralBonus"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilSpectralBonus.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar b\xc3\xb4nus espectrais calculados\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar b\xc3\xb4nus espectrais calculados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BonusName_MetaData[] = {
		{ "Category", "Spectral Bonus" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BonusValue_MetaData[] = {
		{ "Category", "Spectral Bonus" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_BonusName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BonusValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilSpectralBonus>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::NewProp_BonusName = { "BonusName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSpectralBonus, BonusName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BonusName_MetaData), NewProp_BonusName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::NewProp_BonusValue = { "BonusValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSpectralBonus, BonusValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BonusValue_MetaData), NewProp_BonusValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::NewProp_BonusName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::NewProp_BonusValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilSpectralBonus",
	Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::PropPointers),
	sizeof(FSigilSpectralBonus),
	alignof(FSigilSpectralBonus),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilSpectralBonus()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilSpectralBonus.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilSpectralBonus.InnerSingleton, Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilSpectralBonus.InnerSingleton;
}
// ********** End ScriptStruct FSigilSpectralBonus *************************************************

// ********** Begin ScriptStruct FSigilData ********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilData;
class UScriptStruct* FSigilData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilData"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Struct para dados completos do sigilo\n * Cont\xc3\xa9m todas as informa\xc3\xa7\xc3\xb5""es necess\xc3\xa1rias para funcionamento\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Struct para dados completos do sigilo\nCont\xc3\xa9m todas as informa\xc3\xa7\xc3\xb5""es necess\xc3\xa1rias para funcionamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilID_MetaData[] = {
		{ "Category", "Basic Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Informa\xc3\xa7\xc3\xb5""es B\xc3\xa1sicas\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es B\xc3\xa1sicas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilName_MetaData[] = {
		{ "Category", "Basic Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Basic Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilType_MetaData[] = {
		{ "Category", "Basic Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilSubType_MetaData[] = {
		{ "Category", "Basic Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rarity_MetaData[] = {
		{ "Category", "Basic Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Basic Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "Category", "Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual Assets\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual Assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilMesh_MetaData[] = {
		{ "Category", "Visuals" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilTag_MetaData[] = {
		{ "Category", "Gameplay Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GameplayTags System\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayTags System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Gameplay Tags" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlockedTags_MetaData[] = {
		{ "Category", "Gameplay Tags" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RarityTag_MetaData[] = {
		{ "Category", "Gameplay Tags" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PassiveEffects_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GameplayEffects System\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffects System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionEffects_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UniqueEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusiveAbility_MetaData[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Habilidades Exclusivas dos Subtipos\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidades Exclusivas dos Subtipos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusiveAbilityEffects_MetaData[] = {
		{ "Category", "Sigil Abilities" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquipVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Niagara VFX System\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Niagara VFX System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionVFX_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AuraVFX_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralPowerBonus_MetaData[] = {
		{ "Category", "Spectral Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Modificadores Espectrais\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modificadores Espectrais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralResilienceBonus_MetaData[] = {
		{ "Category", "Spectral Modifiers" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralVelocityBonus_MetaData[] = {
		{ "Category", "Spectral Modifiers" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralFocusBonus_MetaData[] = {
		{ "Category", "Spectral Modifiers" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionMultiplier_MetaData[] = {
		{ "Category", "Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de Fus\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de Fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeFused_MetaData[] = {
		{ "Category", "Fusion" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFused_MetaData[] = {
		{ "Category", "Fusion" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLevel_MetaData[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Propriedades de progress\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades de progress\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLevel_MetaData[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentExperience_MetaData[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredLevel_MetaData[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BasePower_MetaData[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilRarity_MetaData[] = {
		{ "Category", "Basic Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estados adicionais\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados adicionais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilState_MetaData[] = {
		{ "Category", "Basic Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionAuraVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// VFX adicionais\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX adicionais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IdleVFX_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelUpVFX_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquipSound_MetaData[] = {
		{ "Category", "Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sons\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Propriedades din\xc3\xa2micas\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades din\xc3\xa2micas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralBonuses_MetaData[] = {
		{ "Category", "Spectral Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// B\xc3\xb4nus espectrais calculados\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus espectrais calculados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SigilName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Description;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilSubType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilSubType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Icon;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SigilMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilTag;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlockedTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RarityTag;
	static const UECodeGen_Private::FClassPropertyParams NewProp_PassiveEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PassiveEffects;
	static const UECodeGen_Private::FClassPropertyParams NewProp_FusionEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FusionEffects;
	static const UECodeGen_Private::FClassPropertyParams NewProp_UniqueEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ExclusiveAbility;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ExclusiveAbilityEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExclusiveAbilityEffects;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EquipVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FusionVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AuraVFX;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpectralPowerBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpectralResilienceBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpectralVelocityBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpectralFocusBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionMultiplier;
	static void NewProp_bCanBeFused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeFused;
	static void NewProp_bIsFused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFused;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentExperience;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BasePower;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilRarity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilState;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FusionAuraVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_IdleVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LevelUpVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EquipSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FusionSound;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Properties_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Properties;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpectralBonuses_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpectralBonuses;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SigilID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilID_MetaData), NewProp_SigilID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilName = { "SigilName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SigilName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilName_MetaData), NewProp_SigilName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SigilType), Z_Construct_UEnum_AURACRON_ESigilType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilType_MetaData), NewProp_SigilType_MetaData) }; // 3758400079
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilSubType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilSubType = { "SigilSubType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SigilSubType), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilSubType_MetaData), NewProp_SigilSubType_MetaData) }; // 3161995902
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, Rarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rarity_MetaData), NewProp_Rarity_MetaData) }; // 3544987888
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, State), Z_Construct_UEnum_AURACRON_ESigilState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 2914959479
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilMesh = { "SigilMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SigilMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilMesh_MetaData), NewProp_SigilMesh_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilTag = { "SigilTag", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SigilTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilTag_MetaData), NewProp_SigilTag_MetaData) }; // 133831994
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_BlockedTags = { "BlockedTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, BlockedTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlockedTags_MetaData), NewProp_BlockedTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_RarityTag = { "RarityTag", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, RarityTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RarityTag_MetaData), NewProp_RarityTag_MetaData) }; // 133831994
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_PassiveEffects_Inner = { "PassiveEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_PassiveEffects = { "PassiveEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, PassiveEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PassiveEffects_MetaData), NewProp_PassiveEffects_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionEffects_Inner = { "FusionEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionEffects = { "FusionEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, FusionEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionEffects_MetaData), NewProp_FusionEffects_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_UniqueEffect = { "UniqueEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, UniqueEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UniqueEffect_MetaData), NewProp_UniqueEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_ExclusiveAbility = { "ExclusiveAbility", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, ExclusiveAbility), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusiveAbility_MetaData), NewProp_ExclusiveAbility_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_ExclusiveAbilityEffects_Inner = { "ExclusiveAbilityEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_ExclusiveAbilityEffects = { "ExclusiveAbilityEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, ExclusiveAbilityEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusiveAbilityEffects_MetaData), NewProp_ExclusiveAbilityEffects_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_EquipVFX = { "EquipVFX", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, EquipVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquipVFX_MetaData), NewProp_EquipVFX_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionVFX = { "FusionVFX", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, FusionVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionVFX_MetaData), NewProp_FusionVFX_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_AuraVFX = { "AuraVFX", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, AuraVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AuraVFX_MetaData), NewProp_AuraVFX_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralPowerBonus = { "SpectralPowerBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SpectralPowerBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralPowerBonus_MetaData), NewProp_SpectralPowerBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralResilienceBonus = { "SpectralResilienceBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SpectralResilienceBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralResilienceBonus_MetaData), NewProp_SpectralResilienceBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralVelocityBonus = { "SpectralVelocityBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SpectralVelocityBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralVelocityBonus_MetaData), NewProp_SpectralVelocityBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralFocusBonus = { "SpectralFocusBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SpectralFocusBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralFocusBonus_MetaData), NewProp_SpectralFocusBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionMultiplier = { "FusionMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, FusionMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionMultiplier_MetaData), NewProp_FusionMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_bCanBeFused_SetBit(void* Obj)
{
	((FSigilData*)Obj)->bCanBeFused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_bCanBeFused = { "bCanBeFused", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilData), &Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_bCanBeFused_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeFused_MetaData), NewProp_bCanBeFused_MetaData) };
void Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_bIsFused_SetBit(void* Obj)
{
	((FSigilData*)Obj)->bIsFused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_bIsFused = { "bIsFused", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilData), &Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_bIsFused_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFused_MetaData), NewProp_bIsFused_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_CurrentLevel = { "CurrentLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, CurrentLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLevel_MetaData), NewProp_CurrentLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_MaxLevel = { "MaxLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, MaxLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLevel_MetaData), NewProp_MaxLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_CurrentExperience = { "CurrentExperience", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, CurrentExperience), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentExperience_MetaData), NewProp_CurrentExperience_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_RequiredLevel = { "RequiredLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, RequiredLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredLevel_MetaData), NewProp_RequiredLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_BasePower = { "BasePower", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, BasePower), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BasePower_MetaData), NewProp_BasePower_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilRarity = { "SigilRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SigilRarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilRarity_MetaData), NewProp_SigilRarity_MetaData) }; // 3544987888
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilState = { "SigilState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SigilState), Z_Construct_UEnum_AURACRON_ESigilState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilState_MetaData), NewProp_SigilState_MetaData) }; // 2914959479
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionAuraVFX = { "FusionAuraVFX", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, FusionAuraVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionAuraVFX_MetaData), NewProp_FusionAuraVFX_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_IdleVFX = { "IdleVFX", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, IdleVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IdleVFX_MetaData), NewProp_IdleVFX_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_LevelUpVFX = { "LevelUpVFX", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, LevelUpVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelUpVFX_MetaData), NewProp_LevelUpVFX_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_EquipSound = { "EquipSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, EquipSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquipSound_MetaData), NewProp_EquipSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionSound = { "FusionSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, FusionSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionSound_MetaData), NewProp_FusionSound_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Properties_Inner = { "Properties", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilProperty, METADATA_PARAMS(0, nullptr) }; // 694755336
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, Properties), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) }; // 694755336
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralBonuses_Inner = { "SpectralBonuses", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilSpectralBonus, METADATA_PARAMS(0, nullptr) }; // 2259416974
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralBonuses = { "SpectralBonuses", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilData, SpectralBonuses), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralBonuses_MetaData), NewProp_SpectralBonuses_MetaData) }; // 2259416974
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilSubType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilSubType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_BlockedTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_RarityTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_PassiveEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_PassiveEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_UniqueEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_ExclusiveAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_ExclusiveAbilityEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_ExclusiveAbilityEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_EquipVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_AuraVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralPowerBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralResilienceBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralVelocityBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralFocusBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_bCanBeFused,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_bIsFused,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_CurrentLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_MaxLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_CurrentExperience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_RequiredLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_BasePower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SigilState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionAuraVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_IdleVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_LevelUpVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_EquipSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_FusionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Properties_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralBonuses_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilData_Statics::NewProp_SpectralBonuses,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilData",
	Z_Construct_UScriptStruct_FSigilData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilData_Statics::PropPointers),
	sizeof(FSigilData),
	alignof(FSigilData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilData()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilData.InnerSingleton, Z_Construct_UScriptStruct_FSigilData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilData.InnerSingleton;
}
// ********** End ScriptStruct FSigilData **********************************************************

// ********** Begin Class ASigilItem Function AddExperience ****************************************
struct Z_Construct_UFunction_ASigilItem_AddExperience_Statics
{
	struct SigilItem_eventAddExperience_Parms
	{
		float Amount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Level and Experience System\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level and Experience System" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ASigilItem_AddExperience_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventAddExperience_Parms, Amount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_AddExperience_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_AddExperience_Statics::NewProp_Amount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_AddExperience_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_AddExperience_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "AddExperience", Z_Construct_UFunction_ASigilItem_AddExperience_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_AddExperience_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_AddExperience_Statics::SigilItem_eventAddExperience_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_AddExperience_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_AddExperience_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_AddExperience_Statics::SigilItem_eventAddExperience_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_AddExperience()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_AddExperience_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execAddExperience)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddExperience(Z_Param_Amount);
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function AddExperience ******************************************

// ********** Begin Class ASigilItem Function ApplyFusionEffects ***********************************
struct Z_Construct_UFunction_ASigilItem_ApplyFusionEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion System" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_ApplyFusionEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "ApplyFusionEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ApplyFusionEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_ApplyFusionEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_ApplyFusionEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_ApplyFusionEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execApplyFusionEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyFusionEffects();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function ApplyFusionEffects *************************************

// ********** Begin Class ASigilItem Function CalculateSigilPower **********************************
struct Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics
{
	struct SigilItem_eventCalculateSigilPower_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventCalculateSigilPower_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "CalculateSigilPower", Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::SigilItem_eventCalculateSigilPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::SigilItem_eventCalculateSigilPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_CalculateSigilPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_CalculateSigilPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execCalculateSigilPower)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateSigilPower();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function CalculateSigilPower ************************************

// ********** Begin Class ASigilItem Function CanEquipToActor **************************************
struct Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics
{
	struct SigilItem_eventCanEquipToActor_Parms
	{
		AActor* TargetActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventCanEquipToActor_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventCanEquipToActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventCanEquipToActor_Parms), &Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "CanEquipToActor", Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::SigilItem_eventCanEquipToActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::SigilItem_eventCanEquipToActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_CanEquipToActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_CanEquipToActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execCanEquipToActor)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanEquipToActor(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function CanEquipToActor ****************************************

// ********** Begin Class ASigilItem Function CanFuse **********************************************
struct Z_Construct_UFunction_ASigilItem_CanFuse_Statics
{
	struct SigilItem_eventCanFuse_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion System" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_CanFuse_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventCanFuse_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_CanFuse_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventCanFuse_Parms), &Z_Construct_UFunction_ASigilItem_CanFuse_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_CanFuse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_CanFuse_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanFuse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_CanFuse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "CanFuse", Z_Construct_UFunction_ASigilItem_CanFuse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanFuse_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_CanFuse_Statics::SigilItem_eventCanFuse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanFuse_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_CanFuse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_CanFuse_Statics::SigilItem_eventCanFuse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_CanFuse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_CanFuse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execCanFuse)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanFuse();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function CanFuse ************************************************

// ********** Begin Class ASigilItem Function CanLevelUp *******************************************
struct Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics
{
	struct SigilItem_eventCanLevelUp_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventCanLevelUp_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventCanLevelUp_Parms), &Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "CanLevelUp", Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::SigilItem_eventCanLevelUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::SigilItem_eventCanLevelUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_CanLevelUp()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_CanLevelUp_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execCanLevelUp)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanLevelUp();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function CanLevelUp *********************************************

// ********** Begin Class ASigilItem Function CanReforge *******************************************
struct Z_Construct_UFunction_ASigilItem_CanReforge_Statics
{
	struct SigilItem_eventCanReforge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reforge System" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_CanReforge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventCanReforge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_CanReforge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventCanReforge_Parms), &Z_Construct_UFunction_ASigilItem_CanReforge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_CanReforge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_CanReforge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanReforge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_CanReforge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "CanReforge", Z_Construct_UFunction_ASigilItem_CanReforge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanReforge_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_CanReforge_Statics::SigilItem_eventCanReforge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanReforge_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_CanReforge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_CanReforge_Statics::SigilItem_eventCanReforge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_CanReforge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_CanReforge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execCanReforge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanReforge();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function CanReforge *********************************************

// ********** Begin Class ASigilItem Function CanUnequip *******************************************
struct Z_Construct_UFunction_ASigilItem_CanUnequip_Statics
{
	struct SigilItem_eventCanUnequip_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventCanUnequip_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventCanUnequip_Parms), &Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "CanUnequip", Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::SigilItem_eventCanUnequip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::SigilItem_eventCanUnequip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_CanUnequip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_CanUnequip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execCanUnequip)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanUnequip();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function CanUnequip *********************************************

// ********** Begin Class ASigilItem Function EquipToActor *****************************************
struct Z_Construct_UFunction_ASigilItem_EquipToActor_Statics
{
	struct SigilItem_eventEquipToActor_Parms
	{
		AActor* TargetActor;
		int32 TargetSlotIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core Sigil Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core Sigil Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetSlotIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventEquipToActor_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::NewProp_TargetSlotIndex = { "TargetSlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventEquipToActor_Parms, TargetSlotIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventEquipToActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventEquipToActor_Parms), &Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::NewProp_TargetSlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "EquipToActor", Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::SigilItem_eventEquipToActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::SigilItem_eventEquipToActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_EquipToActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_EquipToActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execEquipToActor)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetSlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EquipToActor(Z_Param_TargetActor,Z_Param_TargetSlotIndex);
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function EquipToActor *******************************************

// ********** Begin Class ASigilItem Function GetEffectiveBonus ************************************
struct Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics
{
	struct SigilItem_eventGetEffectiveBonus_Parms
	{
		FString AttributeName;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetEffectiveBonus_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetEffectiveBonus_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetEffectiveBonus", Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::SigilItem_eventGetEffectiveBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::SigilItem_eventGetEffectiveBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetEffectiveBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetEffectiveBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetEffectiveBonus)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEffectiveBonus(Z_Param_AttributeName);
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetEffectiveBonus **************************************

// ********** Begin Class ASigilItem Function GetExperienceToNextLevel *****************************
struct Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics
{
	struct SigilItem_eventGetExperienceToNextLevel_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetExperienceToNextLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetExperienceToNextLevel", Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::SigilItem_eventGetExperienceToNextLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::SigilItem_eventGetExperienceToNextLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetExperienceToNextLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetExperienceToNextLevel();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetExperienceToNextLevel *******************************

// ********** Begin Class ASigilItem Function GetSigilData *****************************************
struct Z_Construct_UFunction_ASigilItem_GetSigilData_Statics
{
	struct SigilItem_eventGetSigilData_Parms
	{
		FSigilData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000008000582, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetSigilData_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) }; // 332626528
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetSigilData", Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::SigilItem_eventGetSigilData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::SigilItem_eventGetSigilData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetSigilData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetSigilData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetSigilData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilData*)Z_Param__Result=P_THIS->GetSigilData();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetSigilData *******************************************

// ********** Begin Class ASigilItem Function GetSigilID *******************************************
struct Z_Construct_UFunction_ASigilItem_GetSigilID_Statics
{
	struct SigilItem_eventGetSigilID_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetSigilID_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetSigilID", Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::SigilItem_eventGetSigilID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::SigilItem_eventGetSigilID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetSigilID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetSigilID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetSigilID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetSigilID();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetSigilID *********************************************

// ********** Begin Class ASigilItem Function GetSigilRarity ***************************************
struct Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics
{
	struct SigilItem_eventGetSigilRarity_Parms
	{
		ESigilRarity ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetSigilRarity_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetSigilRarity", Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::SigilItem_eventGetSigilRarity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::SigilItem_eventGetSigilRarity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetSigilRarity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetSigilRarity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetSigilRarity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ESigilRarity*)Z_Param__Result=P_THIS->GetSigilRarity();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetSigilRarity *****************************************

// ********** Begin Class ASigilItem Function GetSigilState ****************************************
struct Z_Construct_UFunction_ASigilItem_GetSigilState_Statics
{
	struct SigilItem_eventGetSigilState_Parms
	{
		ESigilState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetSigilState_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_ESigilState, METADATA_PARAMS(0, nullptr) }; // 2914959479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetSigilState", Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::SigilItem_eventGetSigilState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::SigilItem_eventGetSigilState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetSigilState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetSigilState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetSigilState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ESigilState*)Z_Param__Result=P_THIS->GetSigilState();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetSigilState ******************************************

// ********** Begin Class ASigilItem Function GetSigilTag ******************************************
struct Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics
{
	struct SigilItem_eventGetSigilTag_Parms
	{
		FGameplayTag ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetSigilTag_Parms, ReturnValue), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetSigilTag", Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::SigilItem_eventGetSigilTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::SigilItem_eventGetSigilTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetSigilTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetSigilTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetSigilTag)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FGameplayTag*)Z_Param__Result=P_THIS->GetSigilTag();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetSigilTag ********************************************

// ********** Begin Class ASigilItem Function GetSigilType *****************************************
struct Z_Construct_UFunction_ASigilItem_GetSigilType_Statics
{
	struct SigilItem_eventGetSigilType_Parms
	{
		ESigilType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Getters - BlueprintPure for performance\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Getters - BlueprintPure for performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetSigilType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_ESigilType, METADATA_PARAMS(0, nullptr) }; // 3758400079
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetSigilType", Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::SigilItem_eventGetSigilType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::SigilItem_eventGetSigilType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetSigilType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetSigilType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetSigilType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ESigilType*)Z_Param__Result=P_THIS->GetSigilType();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetSigilType *******************************************

// ********** Begin Class ASigilItem Function GetTotalSpectralPower ********************************
struct Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics
{
	struct SigilItem_eventGetTotalSpectralPower_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventGetTotalSpectralPower_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "GetTotalSpectralPower", Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::SigilItem_eventGetTotalSpectralPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::SigilItem_eventGetTotalSpectralPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execGetTotalSpectralPower)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalSpectralPower();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function GetTotalSpectralPower **********************************

// ********** Begin Class ASigilItem Function IsEquipped *******************************************
struct Z_Construct_UFunction_ASigilItem_IsEquipped_Statics
{
	struct SigilItem_eventIsEquipped_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventIsEquipped_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventIsEquipped_Parms), &Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "IsEquipped", Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::SigilItem_eventIsEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::SigilItem_eventIsEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_IsEquipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_IsEquipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execIsEquipped)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsEquipped();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function IsEquipped *********************************************

// ********** Begin Class ASigilItem Function IsFused **********************************************
struct Z_Construct_UFunction_ASigilItem_IsFused_Statics
{
	struct SigilItem_eventIsFused_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Info" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_IsFused_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventIsFused_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_IsFused_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventIsFused_Parms), &Z_Construct_UFunction_ASigilItem_IsFused_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_IsFused_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_IsFused_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_IsFused_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_IsFused_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "IsFused", Z_Construct_UFunction_ASigilItem_IsFused_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_IsFused_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_IsFused_Statics::SigilItem_eventIsFused_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_IsFused_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_IsFused_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_IsFused_Statics::SigilItem_eventIsFused_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_IsFused()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_IsFused_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execIsFused)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsFused();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function IsFused ************************************************

// ********** Begin Class ASigilItem Function LevelUp **********************************************
struct Z_Construct_UFunction_ASigilItem_LevelUp_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_LevelUp_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "LevelUp", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_LevelUp_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_LevelUp_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_LevelUp()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_LevelUp_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execLevelUp)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LevelUp();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function LevelUp ************************************************

// ********** Begin Class ASigilItem Function MulticastPlayEquipVFX ********************************
static FName NAME_ASigilItem_MulticastPlayEquipVFX = FName(TEXT("MulticastPlayEquipVFX"));
void ASigilItem::MulticastPlayEquipVFX()
{
	UFunction* Func = FindFunctionChecked(NAME_ASigilItem_MulticastPlayEquipVFX);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ASigilItem_MulticastPlayEquipVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_MulticastPlayEquipVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "MulticastPlayEquipVFX", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00084CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_MulticastPlayEquipVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_MulticastPlayEquipVFX_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_MulticastPlayEquipVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_MulticastPlayEquipVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execMulticastPlayEquipVFX)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastPlayEquipVFX_Implementation();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function MulticastPlayEquipVFX **********************************

// ********** Begin Class ASigilItem Function MulticastPlayFusionVFX *******************************
static FName NAME_ASigilItem_MulticastPlayFusionVFX = FName(TEXT("MulticastPlayFusionVFX"));
void ASigilItem::MulticastPlayFusionVFX()
{
	UFunction* Func = FindFunctionChecked(NAME_ASigilItem_MulticastPlayFusionVFX);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ASigilItem_MulticastPlayFusionVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_MulticastPlayFusionVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "MulticastPlayFusionVFX", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00084CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_MulticastPlayFusionVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_MulticastPlayFusionVFX_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_MulticastPlayFusionVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_MulticastPlayFusionVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execMulticastPlayFusionVFX)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastPlayFusionVFX_Implementation();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function MulticastPlayFusionVFX *********************************

// ********** Begin Class ASigilItem Function MulticastUpdateRarityGlow ****************************
static FName NAME_ASigilItem_MulticastUpdateRarityGlow = FName(TEXT("MulticastUpdateRarityGlow"));
void ASigilItem::MulticastUpdateRarityGlow()
{
	UFunction* Func = FindFunctionChecked(NAME_ASigilItem_MulticastUpdateRarityGlow);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ASigilItem_MulticastUpdateRarityGlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_MulticastUpdateRarityGlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "MulticastUpdateRarityGlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00084CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_MulticastUpdateRarityGlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_MulticastUpdateRarityGlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_MulticastUpdateRarityGlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_MulticastUpdateRarityGlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execMulticastUpdateRarityGlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastUpdateRarityGlow_Implementation();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function MulticastUpdateRarityGlow ******************************

// ********** Begin Class ASigilItem Function OnRep_SigilData **************************************
struct Z_Construct_UFunction_ASigilItem_OnRep_SigilData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Replication Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Replication Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_OnRep_SigilData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "OnRep_SigilData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_OnRep_SigilData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_OnRep_SigilData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_OnRep_SigilData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_OnRep_SigilData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execOnRep_SigilData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilData();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function OnRep_SigilData ****************************************

// ********** Begin Class ASigilItem Function PlayAuraVFX ******************************************
struct Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics
{
	struct SigilItem_eventPlayAuraVFX_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((SigilItem_eventPlayAuraVFX_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventPlayAuraVFX_Parms), &Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "PlayAuraVFX", Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::SigilItem_eventPlayAuraVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::SigilItem_eventPlayAuraVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_PlayAuraVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_PlayAuraVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execPlayAuraVFX)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayAuraVFX(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function PlayAuraVFX ********************************************

// ********** Begin Class ASigilItem Function PlayEquipVFX *****************************************
struct Z_Construct_UFunction_ASigilItem_PlayEquipVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// VFX Management\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_PlayEquipVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "PlayEquipVFX", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_PlayEquipVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_PlayEquipVFX_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_PlayEquipVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_PlayEquipVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execPlayEquipVFX)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayEquipVFX();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function PlayEquipVFX *******************************************

// ********** Begin Class ASigilItem Function PlayFusionVFX ****************************************
struct Z_Construct_UFunction_ASigilItem_PlayFusionVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_PlayFusionVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "PlayFusionVFX", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_PlayFusionVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_PlayFusionVFX_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_PlayFusionVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_PlayFusionVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execPlayFusionVFX)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayFusionVFX();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function PlayFusionVFX ******************************************

// ********** Begin Class ASigilItem Function ReforgeProperties ************************************
struct Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics
{
	struct SigilItem_eventReforgeProperties_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reforge System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Reforge System\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reforge System" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventReforgeProperties_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventReforgeProperties_Parms), &Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "ReforgeProperties", Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::SigilItem_eventReforgeProperties_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::SigilItem_eventReforgeProperties_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_ReforgeProperties()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_ReforgeProperties_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execReforgeProperties)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReforgeProperties();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function ReforgeProperties **************************************

// ********** Begin Class ASigilItem Function RegenerateSigilData **********************************
struct Z_Construct_UFunction_ASigilItem_RegenerateSigilData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_RegenerateSigilData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "RegenerateSigilData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_RegenerateSigilData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_RegenerateSigilData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_RegenerateSigilData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_RegenerateSigilData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execRegenerateSigilData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegenerateSigilData();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function RegenerateSigilData ************************************

// ********** Begin Class ASigilItem Function RemoveFusionEffects **********************************
struct Z_Construct_UFunction_ASigilItem_RemoveFusionEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion System" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_RemoveFusionEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "RemoveFusionEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_RemoveFusionEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_RemoveFusionEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_RemoveFusionEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_RemoveFusionEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execRemoveFusionEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveFusionEffects();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function RemoveFusionEffects ************************************

// ********** Begin Class ASigilItem Function ServerEquipToActor ***********************************
struct SigilItem_eventServerEquipToActor_Parms
{
	AActor* TargetActor;
	int32 TargetSlotIndex;
};
static FName NAME_ASigilItem_ServerEquipToActor = FName(TEXT("ServerEquipToActor"));
void ASigilItem::ServerEquipToActor(AActor* TargetActor, int32 TargetSlotIndex)
{
	SigilItem_eventServerEquipToActor_Parms Parms;
	Parms.TargetActor=TargetActor;
	Parms.TargetSlotIndex=TargetSlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_ASigilItem_ServerEquipToActor);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Network Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetSlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventServerEquipToActor_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::NewProp_TargetSlotIndex = { "TargetSlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventServerEquipToActor_Parms, TargetSlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::NewProp_TargetSlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "ServerEquipToActor", Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::PropPointers), sizeof(SigilItem_eventServerEquipToActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00280CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilItem_eventServerEquipToActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_ServerEquipToActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_ServerEquipToActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execServerEquipToActor)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetSlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ServerEquipToActor_Implementation(Z_Param_TargetActor,Z_Param_TargetSlotIndex);
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function ServerEquipToActor *************************************

// ********** Begin Class ASigilItem Function ServerReforgeProperties ******************************
static FName NAME_ASigilItem_ServerReforgeProperties = FName(TEXT("ServerReforgeProperties"));
void ASigilItem::ServerReforgeProperties()
{
	UFunction* Func = FindFunctionChecked(NAME_ASigilItem_ServerReforgeProperties);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ASigilItem_ServerReforgeProperties_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_ServerReforgeProperties_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "ServerReforgeProperties", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00280CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ServerReforgeProperties_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_ServerReforgeProperties_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_ServerReforgeProperties()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_ServerReforgeProperties_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execServerReforgeProperties)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ServerReforgeProperties_Implementation();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function ServerReforgeProperties ********************************

// ********** Begin Class ASigilItem Function ServerTriggerFusion **********************************
static FName NAME_ASigilItem_ServerTriggerFusion = FName(TEXT("ServerTriggerFusion"));
void ASigilItem::ServerTriggerFusion()
{
	UFunction* Func = FindFunctionChecked(NAME_ASigilItem_ServerTriggerFusion);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ASigilItem_ServerTriggerFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_ServerTriggerFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "ServerTriggerFusion", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00280CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ServerTriggerFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_ServerTriggerFusion_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_ServerTriggerFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_ServerTriggerFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execServerTriggerFusion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ServerTriggerFusion_Implementation();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function ServerTriggerFusion ************************************

// ********** Begin Class ASigilItem Function ServerUnequipFromActor *******************************
static FName NAME_ASigilItem_ServerUnequipFromActor = FName(TEXT("ServerUnequipFromActor"));
void ASigilItem::ServerUnequipFromActor()
{
	UFunction* Func = FindFunctionChecked(NAME_ASigilItem_ServerUnequipFromActor);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ASigilItem_ServerUnequipFromActor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_ServerUnequipFromActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "ServerUnequipFromActor", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00280CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_ServerUnequipFromActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_ServerUnequipFromActor_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_ServerUnequipFromActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_ServerUnequipFromActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execServerUnequipFromActor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ServerUnequipFromActor_Implementation();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function ServerUnequipFromActor *********************************

// ********** Begin Class ASigilItem Function SetOwningSlot ****************************************
struct Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics
{
	struct SigilItem_eventSetOwningSlot_Parms
	{
		int32 NewSlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewSlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::NewProp_NewSlotIndex = { "NewSlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventSetOwningSlot_Parms, NewSlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::NewProp_NewSlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "SetOwningSlot", Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::SigilItem_eventSetOwningSlot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::SigilItem_eventSetOwningSlot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_SetOwningSlot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_SetOwningSlot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execSetOwningSlot)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewSlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetOwningSlot(Z_Param_NewSlotIndex);
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function SetOwningSlot ******************************************

// ********** Begin Class ASigilItem Function SetSigilState ****************************************
struct Z_Construct_UFunction_ASigilItem_SetSigilState_Statics
{
	struct SigilItem_eventSetSigilState_Parms
	{
		ESigilState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilItem_eventSetSigilState_Parms, NewState), Z_Construct_UEnum_AURACRON_ESigilState, METADATA_PARAMS(0, nullptr) }; // 2914959479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "SetSigilState", Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::SigilItem_eventSetSigilState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::SigilItem_eventSetSigilState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_SetSigilState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_SetSigilState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execSetSigilState)
{
	P_GET_ENUM(ESigilState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSigilState(ESigilState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function SetSigilState ******************************************

// ********** Begin Class ASigilItem Function TriggerFusion ****************************************
struct Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics
{
	struct SigilItem_eventTriggerFusion_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fusion System\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fusion System" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventTriggerFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventTriggerFusion_Parms), &Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "TriggerFusion", Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::SigilItem_eventTriggerFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::SigilItem_eventTriggerFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_TriggerFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_TriggerFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execTriggerFusion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TriggerFusion();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function TriggerFusion ******************************************

// ********** Begin Class ASigilItem Function UnequipFromActor *************************************
struct Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics
{
	struct SigilItem_eventUnequipFromActor_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Management" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilItem_eventUnequipFromActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilItem_eventUnequipFromActor_Parms), &Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "UnequipFromActor", Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::SigilItem_eventUnequipFromActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::SigilItem_eventUnequipFromActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASigilItem_UnequipFromActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_UnequipFromActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execUnequipFromActor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnequipFromActor();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function UnequipFromActor ***************************************

// ********** Begin Class ASigilItem Function UpdateRarityGlow *************************************
struct Z_Construct_UFunction_ASigilItem_UpdateRarityGlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASigilItem_UpdateRarityGlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASigilItem, nullptr, "UpdateRarityGlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASigilItem_UpdateRarityGlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASigilItem_UpdateRarityGlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASigilItem_UpdateRarityGlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASigilItem_UpdateRarityGlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASigilItem::execUpdateRarityGlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRarityGlow();
	P_NATIVE_END;
}
// ********** End Class ASigilItem Function UpdateRarityGlow ***************************************

// ********** Begin Class ASigilItem ***************************************************************
void ASigilItem::StaticRegisterNativesASigilItem()
{
	UClass* Class = ASigilItem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddExperience", &ASigilItem::execAddExperience },
		{ "ApplyFusionEffects", &ASigilItem::execApplyFusionEffects },
		{ "CalculateSigilPower", &ASigilItem::execCalculateSigilPower },
		{ "CanEquipToActor", &ASigilItem::execCanEquipToActor },
		{ "CanFuse", &ASigilItem::execCanFuse },
		{ "CanLevelUp", &ASigilItem::execCanLevelUp },
		{ "CanReforge", &ASigilItem::execCanReforge },
		{ "CanUnequip", &ASigilItem::execCanUnequip },
		{ "EquipToActor", &ASigilItem::execEquipToActor },
		{ "GetEffectiveBonus", &ASigilItem::execGetEffectiveBonus },
		{ "GetExperienceToNextLevel", &ASigilItem::execGetExperienceToNextLevel },
		{ "GetSigilData", &ASigilItem::execGetSigilData },
		{ "GetSigilID", &ASigilItem::execGetSigilID },
		{ "GetSigilRarity", &ASigilItem::execGetSigilRarity },
		{ "GetSigilState", &ASigilItem::execGetSigilState },
		{ "GetSigilTag", &ASigilItem::execGetSigilTag },
		{ "GetSigilType", &ASigilItem::execGetSigilType },
		{ "GetTotalSpectralPower", &ASigilItem::execGetTotalSpectralPower },
		{ "IsEquipped", &ASigilItem::execIsEquipped },
		{ "IsFused", &ASigilItem::execIsFused },
		{ "LevelUp", &ASigilItem::execLevelUp },
		{ "MulticastPlayEquipVFX", &ASigilItem::execMulticastPlayEquipVFX },
		{ "MulticastPlayFusionVFX", &ASigilItem::execMulticastPlayFusionVFX },
		{ "MulticastUpdateRarityGlow", &ASigilItem::execMulticastUpdateRarityGlow },
		{ "OnRep_SigilData", &ASigilItem::execOnRep_SigilData },
		{ "PlayAuraVFX", &ASigilItem::execPlayAuraVFX },
		{ "PlayEquipVFX", &ASigilItem::execPlayEquipVFX },
		{ "PlayFusionVFX", &ASigilItem::execPlayFusionVFX },
		{ "ReforgeProperties", &ASigilItem::execReforgeProperties },
		{ "RegenerateSigilData", &ASigilItem::execRegenerateSigilData },
		{ "RemoveFusionEffects", &ASigilItem::execRemoveFusionEffects },
		{ "ServerEquipToActor", &ASigilItem::execServerEquipToActor },
		{ "ServerReforgeProperties", &ASigilItem::execServerReforgeProperties },
		{ "ServerTriggerFusion", &ASigilItem::execServerTriggerFusion },
		{ "ServerUnequipFromActor", &ASigilItem::execServerUnequipFromActor },
		{ "SetOwningSlot", &ASigilItem::execSetOwningSlot },
		{ "SetSigilState", &ASigilItem::execSetSigilState },
		{ "TriggerFusion", &ASigilItem::execTriggerFusion },
		{ "UnequipFromActor", &ASigilItem::execUnequipFromActor },
		{ "UpdateRarityGlow", &ASigilItem::execUpdateRarityGlow },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ASigilItem;
UClass* ASigilItem::GetPrivateStaticClass()
{
	using TClass = ASigilItem;
	if (!Z_Registration_Info_UClass_ASigilItem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilItem"),
			Z_Registration_Info_UClass_ASigilItem.InnerSingleton,
			StaticRegisterNativesASigilItem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ASigilItem.InnerSingleton;
}
UClass* Z_Construct_UClass_ASigilItem_NoRegister()
{
	return ASigilItem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ASigilItem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe base para itens de sigilo espectral\n * Implementa IAbilitySystemInterface para integra\xc3\xa7\xc3\xa3o com GAS\n * Suporta replica\xc3\xa7\xc3\xa3o multiplayer para 10 jogadores\n */" },
#endif
		{ "IncludePath", "Sigils/SigilItem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe base para itens de sigilo espectral\nImplementa IAbilitySystemInterface para integra\xc3\xa7\xc3\xa3o com GAS\nSuporta replica\xc3\xa7\xc3\xa3o multiplayer para 10 jogadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySystemComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeSet_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilData_MetaData[] = {
		{ "Category", "Sigil Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sigil Data - Replicated for multiplayer\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sigil Data - Replicated for multiplayer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilLevel_MetaData[] = {
		{ "Category", "Sigil Data" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperiencePoints_MetaData[] = {
		{ "Category", "Sigil Data" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedOwner_MetaData[] = {
		{ "Category", "Sigil Data" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotIndex_MetaData[] = {
		{ "Category", "Sigil Data" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEquipped_MetaData[] = {
		{ "Category", "Sigil Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Propriedades adicionais para funcionalidade completa\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades adicionais para funcionalidade completa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFused_MetaData[] = {
		{ "Category", "Sigil Data" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastReforgeTime_MetaData[] = {
		{ "Category", "Sigil Data" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwningActor_MetaData[] = {
		{ "Category", "Sigil Data" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivePassiveEffects_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Active GameplayEffect Handles for cleanup\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active GameplayEffect Handles for cleanup" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveUniqueEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveFusionEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilitySystemComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AttributeSet;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VFXComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollisionComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilData;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExperiencePoints;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EquippedOwner;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static void NewProp_bIsEquipped_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEquipped;
	static void NewProp_bIsFused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFused;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastReforgeTime;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwningActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActivePassiveEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActivePassiveEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveUniqueEffect;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveFusionEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveFusionEffects;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ASigilItem_AddExperience, "AddExperience" }, // 3221119670
		{ &Z_Construct_UFunction_ASigilItem_ApplyFusionEffects, "ApplyFusionEffects" }, // 1562378338
		{ &Z_Construct_UFunction_ASigilItem_CalculateSigilPower, "CalculateSigilPower" }, // 4088825390
		{ &Z_Construct_UFunction_ASigilItem_CanEquipToActor, "CanEquipToActor" }, // 3321785357
		{ &Z_Construct_UFunction_ASigilItem_CanFuse, "CanFuse" }, // 287802341
		{ &Z_Construct_UFunction_ASigilItem_CanLevelUp, "CanLevelUp" }, // 97814332
		{ &Z_Construct_UFunction_ASigilItem_CanReforge, "CanReforge" }, // 1969387869
		{ &Z_Construct_UFunction_ASigilItem_CanUnequip, "CanUnequip" }, // 3279033863
		{ &Z_Construct_UFunction_ASigilItem_EquipToActor, "EquipToActor" }, // 2381458023
		{ &Z_Construct_UFunction_ASigilItem_GetEffectiveBonus, "GetEffectiveBonus" }, // 1684925375
		{ &Z_Construct_UFunction_ASigilItem_GetExperienceToNextLevel, "GetExperienceToNextLevel" }, // 3137364304
		{ &Z_Construct_UFunction_ASigilItem_GetSigilData, "GetSigilData" }, // 707482512
		{ &Z_Construct_UFunction_ASigilItem_GetSigilID, "GetSigilID" }, // 2439531947
		{ &Z_Construct_UFunction_ASigilItem_GetSigilRarity, "GetSigilRarity" }, // 996664436
		{ &Z_Construct_UFunction_ASigilItem_GetSigilState, "GetSigilState" }, // 4287603177
		{ &Z_Construct_UFunction_ASigilItem_GetSigilTag, "GetSigilTag" }, // 374529140
		{ &Z_Construct_UFunction_ASigilItem_GetSigilType, "GetSigilType" }, // 2153144211
		{ &Z_Construct_UFunction_ASigilItem_GetTotalSpectralPower, "GetTotalSpectralPower" }, // 2558264566
		{ &Z_Construct_UFunction_ASigilItem_IsEquipped, "IsEquipped" }, // 1638008235
		{ &Z_Construct_UFunction_ASigilItem_IsFused, "IsFused" }, // 2443497754
		{ &Z_Construct_UFunction_ASigilItem_LevelUp, "LevelUp" }, // 1141394205
		{ &Z_Construct_UFunction_ASigilItem_MulticastPlayEquipVFX, "MulticastPlayEquipVFX" }, // 2287121900
		{ &Z_Construct_UFunction_ASigilItem_MulticastPlayFusionVFX, "MulticastPlayFusionVFX" }, // 245510996
		{ &Z_Construct_UFunction_ASigilItem_MulticastUpdateRarityGlow, "MulticastUpdateRarityGlow" }, // 2215373913
		{ &Z_Construct_UFunction_ASigilItem_OnRep_SigilData, "OnRep_SigilData" }, // 2878034776
		{ &Z_Construct_UFunction_ASigilItem_PlayAuraVFX, "PlayAuraVFX" }, // 2434873721
		{ &Z_Construct_UFunction_ASigilItem_PlayEquipVFX, "PlayEquipVFX" }, // 1936504559
		{ &Z_Construct_UFunction_ASigilItem_PlayFusionVFX, "PlayFusionVFX" }, // 375369541
		{ &Z_Construct_UFunction_ASigilItem_ReforgeProperties, "ReforgeProperties" }, // 2428026071
		{ &Z_Construct_UFunction_ASigilItem_RegenerateSigilData, "RegenerateSigilData" }, // 2751966937
		{ &Z_Construct_UFunction_ASigilItem_RemoveFusionEffects, "RemoveFusionEffects" }, // 2654411186
		{ &Z_Construct_UFunction_ASigilItem_ServerEquipToActor, "ServerEquipToActor" }, // 3480905473
		{ &Z_Construct_UFunction_ASigilItem_ServerReforgeProperties, "ServerReforgeProperties" }, // 3748016606
		{ &Z_Construct_UFunction_ASigilItem_ServerTriggerFusion, "ServerTriggerFusion" }, // 2717740092
		{ &Z_Construct_UFunction_ASigilItem_ServerUnequipFromActor, "ServerUnequipFromActor" }, // 208279174
		{ &Z_Construct_UFunction_ASigilItem_SetOwningSlot, "SetOwningSlot" }, // 2788834077
		{ &Z_Construct_UFunction_ASigilItem_SetSigilState, "SetSigilState" }, // 3410610143
		{ &Z_Construct_UFunction_ASigilItem_TriggerFusion, "TriggerFusion" }, // 4063609224
		{ &Z_Construct_UFunction_ASigilItem_UnequipFromActor, "UnequipFromActor" }, // 4216453205
		{ &Z_Construct_UFunction_ASigilItem_UpdateRarityGlow, "UpdateRarityGlow" }, // 312677552
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ASigilItem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_AbilitySystemComponent = { "AbilitySystemComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, AbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySystemComponent_MetaData), NewProp_AbilitySystemComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_AttributeSet = { "AttributeSet", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, AttributeSet), Z_Construct_UClass_USigilAttributeSet_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeSet_MetaData), NewProp_AttributeSet_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_VFXComponent = { "VFXComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, VFXComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXComponent_MetaData), NewProp_VFXComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_CollisionComponent = { "CollisionComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, CollisionComponent), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionComponent_MetaData), NewProp_CollisionComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_SigilData = { "SigilData", "OnRep_SigilData", (EPropertyFlags)0x0010000100000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, SigilData), Z_Construct_UScriptStruct_FSigilData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilData_MetaData), NewProp_SigilData_MetaData) }; // 332626528
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_SigilLevel = { "SigilLevel", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, SigilLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilLevel_MetaData), NewProp_SigilLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_ExperiencePoints = { "ExperiencePoints", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, ExperiencePoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperiencePoints_MetaData), NewProp_ExperiencePoints_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_EquippedOwner = { "EquippedOwner", nullptr, (EPropertyFlags)0x0114000000000034, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, EquippedOwner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedOwner_MetaData), NewProp_EquippedOwner_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000034, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, SlotIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotIndex_MetaData), NewProp_SlotIndex_MetaData) };
void Z_Construct_UClass_ASigilItem_Statics::NewProp_bIsEquipped_SetBit(void* Obj)
{
	((ASigilItem*)Obj)->bIsEquipped = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_bIsEquipped = { "bIsEquipped", nullptr, (EPropertyFlags)0x0010000000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ASigilItem), &Z_Construct_UClass_ASigilItem_Statics::NewProp_bIsEquipped_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEquipped_MetaData), NewProp_bIsEquipped_MetaData) };
void Z_Construct_UClass_ASigilItem_Statics::NewProp_bIsFused_SetBit(void* Obj)
{
	((ASigilItem*)Obj)->bIsFused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_bIsFused = { "bIsFused", nullptr, (EPropertyFlags)0x0010000000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ASigilItem), &Z_Construct_UClass_ASigilItem_Statics::NewProp_bIsFused_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFused_MetaData), NewProp_bIsFused_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_LastReforgeTime = { "LastReforgeTime", nullptr, (EPropertyFlags)0x0010000000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, LastReforgeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastReforgeTime_MetaData), NewProp_LastReforgeTime_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_OwningActor = { "OwningActor", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, OwningActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwningActor_MetaData), NewProp_OwningActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_ActivePassiveEffects_Inner = { "ActivePassiveEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_ActivePassiveEffects = { "ActivePassiveEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, ActivePassiveEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivePassiveEffects_MetaData), NewProp_ActivePassiveEffects_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_ActiveUniqueEffect = { "ActiveUniqueEffect", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, ActiveUniqueEffect), Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveUniqueEffect_MetaData), NewProp_ActiveUniqueEffect_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_ActiveFusionEffects_Inner = { "ActiveFusionEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ASigilItem_Statics::NewProp_ActiveFusionEffects = { "ActiveFusionEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASigilItem, ActiveFusionEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveFusionEffects_MetaData), NewProp_ActiveFusionEffects_MetaData) }; // 386907876
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ASigilItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_AbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_AttributeSet,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_VFXComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_CollisionComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_SigilData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_SigilLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_ExperiencePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_EquippedOwner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_bIsEquipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_bIsFused,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_LastReforgeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_OwningActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_ActivePassiveEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_ActivePassiveEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_ActiveUniqueEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_ActiveFusionEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASigilItem_Statics::NewProp_ActiveFusionEffects,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ASigilItem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ASigilItem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ASigilItem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_ASigilItem_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_UAbilitySystemInterface_NoRegister, (int32)VTABLE_OFFSET(ASigilItem, IAbilitySystemInterface), false },  // 1199015870
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_ASigilItem_Statics::ClassParams = {
	&ASigilItem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ASigilItem_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ASigilItem_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ASigilItem_Statics::Class_MetaDataParams), Z_Construct_UClass_ASigilItem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ASigilItem()
{
	if (!Z_Registration_Info_UClass_ASigilItem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ASigilItem.OuterSingleton, Z_Construct_UClass_ASigilItem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ASigilItem.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void ASigilItem::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_SigilData(TEXT("SigilData"));
	static FName Name_SigilLevel(TEXT("SigilLevel"));
	static FName Name_ExperiencePoints(TEXT("ExperiencePoints"));
	static FName Name_EquippedOwner(TEXT("EquippedOwner"));
	static FName Name_SlotIndex(TEXT("SlotIndex"));
	static FName Name_bIsEquipped(TEXT("bIsEquipped"));
	static FName Name_bIsFused(TEXT("bIsFused"));
	static FName Name_LastReforgeTime(TEXT("LastReforgeTime"));
	const bool bIsValid = true
		&& Name_SigilData == ClassReps[(int32)ENetFields_Private::SigilData].Property->GetFName()
		&& Name_SigilLevel == ClassReps[(int32)ENetFields_Private::SigilLevel].Property->GetFName()
		&& Name_ExperiencePoints == ClassReps[(int32)ENetFields_Private::ExperiencePoints].Property->GetFName()
		&& Name_EquippedOwner == ClassReps[(int32)ENetFields_Private::EquippedOwner].Property->GetFName()
		&& Name_SlotIndex == ClassReps[(int32)ENetFields_Private::SlotIndex].Property->GetFName()
		&& Name_bIsEquipped == ClassReps[(int32)ENetFields_Private::bIsEquipped].Property->GetFName()
		&& Name_bIsFused == ClassReps[(int32)ENetFields_Private::bIsFused].Property->GetFName()
		&& Name_LastReforgeTime == ClassReps[(int32)ENetFields_Private::LastReforgeTime].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in ASigilItem"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(ASigilItem);
ASigilItem::~ASigilItem() {}
// ********** End Class ASigilItem *****************************************************************

// ********** Begin Class USigilDataAsset **********************************************************
void USigilDataAsset::StaticRegisterNativesUSigilDataAsset()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilDataAsset;
UClass* USigilDataAsset::GetPrivateStaticClass()
{
	using TClass = USigilDataAsset;
	if (!Z_Registration_Info_UClass_USigilDataAsset.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilDataAsset"),
			Z_Registration_Info_UClass_USigilDataAsset.InnerSingleton,
			StaticRegisterNativesUSigilDataAsset,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilDataAsset.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilDataAsset_NoRegister()
{
	return USigilDataAsset::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilDataAsset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Asset para configura\xc3\xa7\xc3\xa3o de s\xc3\xadgilos\n * Permite cria\xc3\xa7\xc3\xa3o de templates de s\xc3\xadgilos no editor\n */" },
#endif
		{ "IncludePath", "Sigils/SigilItem.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Asset para configura\xc3\xa7\xc3\xa3o de s\xc3\xadgilos\nPermite cria\xc3\xa7\xc3\xa3o de templates de s\xc3\xadgilos no editor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilTemplate_MetaData[] = {
		{ "Category", "Sigil Template" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilClass_MetaData[] = {
		{ "Category", "Spawn Settings" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnWeight_MetaData[] = {
		{ "Category", "Spawn Settings" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinPlayerLevel_MetaData[] = {
		{ "Category", "Spawn Settings" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredGameModes_MetaData[] = {
		{ "Category", "Spawn Settings" },
		{ "ModuleRelativePath", "Public/Sigils/SigilItem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilTemplate;
	static const UECodeGen_Private::FClassPropertyParams NewProp_SigilClass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnWeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinPlayerLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredGameModes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredGameModes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilDataAsset>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilDataAsset_Statics::NewProp_SigilTemplate = { "SigilTemplate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDataAsset, SigilTemplate), Z_Construct_UScriptStruct_FSigilData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilTemplate_MetaData), NewProp_SigilTemplate_MetaData) }; // 332626528
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilDataAsset_Statics::NewProp_SigilClass = { "SigilClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDataAsset, SigilClass), Z_Construct_UClass_UClass, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilClass_MetaData), NewProp_SigilClass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilDataAsset_Statics::NewProp_SpawnWeight = { "SpawnWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDataAsset, SpawnWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnWeight_MetaData), NewProp_SpawnWeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilDataAsset_Statics::NewProp_MinPlayerLevel = { "MinPlayerLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDataAsset, MinPlayerLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinPlayerLevel_MetaData), NewProp_MinPlayerLevel_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilDataAsset_Statics::NewProp_RequiredGameModes_Inner = { "RequiredGameModes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilDataAsset_Statics::NewProp_RequiredGameModes = { "RequiredGameModes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDataAsset, RequiredGameModes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredGameModes_MetaData), NewProp_RequiredGameModes_MetaData) }; // 133831994
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilDataAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDataAsset_Statics::NewProp_SigilTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDataAsset_Statics::NewProp_SigilClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDataAsset_Statics::NewProp_SpawnWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDataAsset_Statics::NewProp_MinPlayerLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDataAsset_Statics::NewProp_RequiredGameModes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDataAsset_Statics::NewProp_RequiredGameModes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDataAsset_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilDataAsset_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDataAsset,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDataAsset_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilDataAsset_Statics::ClassParams = {
	&USigilDataAsset::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilDataAsset_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilDataAsset_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDataAsset_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilDataAsset_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilDataAsset()
{
	if (!Z_Registration_Info_UClass_USigilDataAsset.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilDataAsset.OuterSingleton, Z_Construct_UClass_USigilDataAsset_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilDataAsset.OuterSingleton;
}
USigilDataAsset::USigilDataAsset(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilDataAsset);
USigilDataAsset::~USigilDataAsset() {}
// ********** End Class USigilDataAsset ************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESigilType_StaticEnum, TEXT("ESigilType"), &Z_Registration_Info_UEnum_ESigilType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3758400079U) },
		{ ESigilSubType_StaticEnum, TEXT("ESigilSubType"), &Z_Registration_Info_UEnum_ESigilSubType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3161995902U) },
		{ ESigilRarity_StaticEnum, TEXT("ESigilRarity"), &Z_Registration_Info_UEnum_ESigilRarity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3544987888U) },
		{ ESigilState_StaticEnum, TEXT("ESigilState"), &Z_Registration_Info_UEnum_ESigilState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2914959479U) },
		{ ESigilPropertyType_StaticEnum, TEXT("ESigilPropertyType"), &Z_Registration_Info_UEnum_ESigilPropertyType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 177189723U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilProperty::StaticStruct, Z_Construct_UScriptStruct_FSigilProperty_Statics::NewStructOps, TEXT("SigilProperty"), &Z_Registration_Info_UScriptStruct_FSigilProperty, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilProperty), 694755336U) },
		{ FSigilSpectralBonus::StaticStruct, Z_Construct_UScriptStruct_FSigilSpectralBonus_Statics::NewStructOps, TEXT("SigilSpectralBonus"), &Z_Registration_Info_UScriptStruct_FSigilSpectralBonus, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilSpectralBonus), 2259416974U) },
		{ FSigilData::StaticStruct, Z_Construct_UScriptStruct_FSigilData_Statics::NewStructOps, TEXT("SigilData"), &Z_Registration_Info_UScriptStruct_FSigilData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilData), 332626528U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ASigilItem, ASigilItem::StaticClass, TEXT("ASigilItem"), &Z_Registration_Info_UClass_ASigilItem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ASigilItem), 1619650097U) },
		{ Z_Construct_UClass_USigilDataAsset, USigilDataAsset::StaticClass, TEXT("USigilDataAsset"), &Z_Registration_Info_UClass_USigilDataAsset, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilDataAsset), 1231657273U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h__Script_AURACRON_2615042487(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilItem_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
