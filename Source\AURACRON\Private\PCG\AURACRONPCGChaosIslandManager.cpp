// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGChaosIslandManager.h"
#include "PCG/AURACRONPCGChaosIsland.h"
#include "PCG/AURACRONPCGChaosPortal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Components/StaticMeshComponent.h"
#include "UObject/ConstructorHelpers.h"
#include "EngineUtils.h"

AAURACRONPCGChaosIslandManager::AAURACRONPCGChaosIslandManager()
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurações padrão conforme GDD
    MaxChaosIslands = 4; // Número balanceado para o mapa
    MinDistanceBetweenIslands = 2000.0f; // Distância mínima em unidades UE
    bAutoSpawnPortals = true; // Auto-spawnar portais por padrão

    // Definir classes padrão
    ChaosIslandClass = AChaosIsland::StaticClass();
    ChaosPortalClass = AAURACRONPCGChaosPortal::StaticClass();
    
    PrismalFlow = nullptr;
}

void AAURACRONPCGChaosIslandManager::BeginPlay()
{
    Super::BeginPlay();
    
    // Se não foi inicializado manualmente, tentar encontrar o PrismalFlow na cena
    if (!PrismalFlow)
    {
        PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGPrismalFlow::StaticClass()));
        if (PrismalFlow)
        {
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: PrismalFlow encontrado automaticamente"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: PrismalFlow não encontrado na cena"));
        }
    }
}

void AAURACRONPCGChaosIslandManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar comportamento das ilhas caos se necessário
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Lógica de atualização das ilhas pode ser implementada aqui
        }
    }
}

void AAURACRONPCGChaosIslandManager::Initialize(AAURACRONPCGPrismalFlow* InPrismalFlow)
{
    if (!InPrismalFlow)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager::Initialize - PrismalFlow inválido"));
        return;
    }
    
    PrismalFlow = InPrismalFlow;
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Inicializado com PrismalFlow"));
}

void AAURACRONPCGChaosIslandManager::GenerateChaosIslands()
{
    if (!PrismalFlow)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - PrismalFlow não definido"));
        return;
    }
    
    // Limpar ilhas existentes
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            Island->Destroy();
        }
    }
    ChaosIslands.Empty();
    
    // Limpar portais existentes
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            Portal->Destroy();
        }
    }
    ChaosPortals.Empty();
    
    // Encontrar pontos de interseção do fluxo
    TArray<FVector> IntersectionPoints = FindAllFlowIntersections();
    
    if (IntersectionPoints.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - Nenhum ponto de interseção encontrado"));
        return;
    }
    
    // Limitar ao número máximo de ilhas
    int32 IslandsToCreate = FMath::Min(IntersectionPoints.Num(), MaxChaosIslands);
    
    // Gerar ilhas nos pontos de interseção
    for (int32 i = 0; i < IslandsToCreate; i++)
    {
        FVector SpawnLocation = IntersectionPoints[i];
        
        // Verificar se a posição está válida e não muito próxima de outras ilhas
        bool bValidPosition = true;
        for (const AChaosIsland* ExistingIsland : ChaosIslands)
        {
            if (ExistingIsland && FVector::Dist(SpawnLocation, ExistingIsland->GetActorLocation()) < MinDistanceBetweenIslands)
            {
                bValidPosition = false;
                break;
            }
        }
        
        if (!bValidPosition)
        {
            continue;
        }
        
        // Spawnar ilha caos
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        AChaosIsland* NewIsland = GetWorld()->SpawnActor<AChaosIsland>(ChaosIslandClass, SpawnLocation, FRotator::ZeroRotator, SpawnParams);
        if (NewIsland)
        {
            ChaosIslands.Add(NewIsland);
            
            // Configurar a ilha conforme especificações do GDD
            NewIsland->SetIslandType(EPrismalFlowIslandType::Chaos);
            
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Ilha Caos criada em %s"), *SpawnLocation.ToString());
            
            // Criar portal caos associado
            AAURACRONPCGChaosPortal* NewPortal = GetWorld()->SpawnActor<AAURACRONPCGChaosPortal>(ChaosPortalClass, SpawnLocation + FVector(0, 0, 200), FRotator::ZeroRotator, SpawnParams);
            if (NewPortal)
            {
                ChaosPortals.Add(NewPortal);
                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Portal Caos criado para ilha em %s"), *SpawnLocation.ToString());
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - Geradas %d ilhas caos"), ChaosIslands.Num());
}

void AAURACRONPCGChaosIslandManager::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Atualizar comportamento das ilhas baseado na fase do mapa
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Implementar lógica específica por fase
            switch (MapPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    // Fase inicial - ilhas menos ativas
                    Island->SetActivityLevel(0.3f);
                    break;
                    
                case EAURACRONMapPhase::Convergence:
                    // Fase de convergência - atividade moderada
                    Island->SetActivityLevel(0.6f);
                    break;
                    
                case EAURACRONMapPhase::Intensification:
                    // Fase de intensificação - alta atividade
                    Island->SetActivityLevel(0.9f);
                    break;
                    
                case EAURACRONMapPhase::Resolution:
                    // Fase de resolução - máxima atividade
                    Island->SetActivityLevel(1.0f);
                    break;
                    

            }
        }
    }
    
    // Atualizar portais também
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            Portal->UpdateForMapPhase(MapPhase);
        }
    }
}

bool AAURACRONPCGChaosIslandManager::IsPointAtFlowIntersection(const FVector& Point, float Tolerance) const
{
    if (!PrismalFlow)
    {
        return false;
    }
    
    // Verificar se o ponto está próximo de uma interseção do fluxo
    TArray<FVector> IntersectionPoints = FindAllFlowIntersections();
    
    for (const FVector& IntersectionPoint : IntersectionPoints)
    {
        if (FVector::Dist(Point, IntersectionPoint) <= Tolerance)
        {
            return true;
        }
    }
    
    return false;
}

TArray<FVector> AAURACRONPCGChaosIslandManager::FindAllFlowIntersections() const
{
    TArray<FVector> IntersectionPoints;
    
    if (!PrismalFlow)
    {
        return IntersectionPoints;
    }
    
    // Obter pontos de controle do fluxo prismal
    TArray<FVector> FlowControlPoints = PrismalFlow->GetFlowControlPoints();
    
    if (FlowControlPoints.Num() < 3)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager::FindAllFlowIntersections - Pontos de controle insuficientes"));
        return IntersectionPoints;
    }
    
    // Algoritmo para encontrar interseções entre segmentos do fluxo
    for (int32 i = 0; i < FlowControlPoints.Num() - 1; i++)
    {
        for (int32 j = i + 2; j < FlowControlPoints.Num() - 1; j++)
        {
            // Evitar segmentos adjacentes
            if (FMath::Abs(i - j) <= 1)
            {
                continue;
            }
            
            FVector P1 = FlowControlPoints[i];
            FVector P2 = FlowControlPoints[i + 1];
            FVector P3 = FlowControlPoints[j];
            FVector P4 = FlowControlPoints[j + 1];
            
            // Calcular interseção entre os segmentos P1-P2 e P3-P4
            FVector IntersectionPoint;
            if (CalculateLineIntersection(P1, P2, P3, P4, IntersectionPoint))
            {
                IntersectionPoints.Add(IntersectionPoint);
            }
        }
    }
    
    // Adicionar pontos de convergência principais (centro do mapa, etc.)
    if (FlowControlPoints.Num() > 0)
    {
        FVector CenterPoint = FVector::ZeroVector;
        for (const FVector& Point : FlowControlPoints)
        {
            CenterPoint += Point;
        }
        CenterPoint /= FlowControlPoints.Num();
        IntersectionPoints.Add(CenterPoint);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::FindAllFlowIntersections - Encontrados %d pontos de interseção"), IntersectionPoints.Num());
    
    return IntersectionPoints;
}

void AAURACRONPCGChaosIslandManager::SetAllChaosIslandsActive(bool bActive)
{
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            Island->SetActorHiddenInGame(!bActive);
            Island->SetActorEnableCollision(bActive);
            Island->SetActorTickEnabled(bActive);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::SetAllChaosIslandsActive - Ilhas %s"), bActive ? TEXT("ativadas") : TEXT("desativadas"));
}

void AAURACRONPCGChaosIslandManager::SetAllChaosPortalsActive(bool bActive, float Duration, float Intensity)
{
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            if (bActive)
            {
                Portal->ActivatePortal(Duration, Intensity);
            }
            else
            {
                Portal->DeactivatePortal();
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::SetAllChaosPortalsActive - Portais %s"), bActive ? TEXT("ativados") : TEXT("desativados"));
}

bool AAURACRONPCGChaosIslandManager::CalculateLineIntersection(const FVector& P1, const FVector& P2, const FVector& P3, const FVector& P4, FVector& OutIntersection) const
{
    // Calcular interseção entre duas linhas em 2D (ignorando Z)
    float X1 = P1.X, Y1 = P1.Y;
    float X2 = P2.X, Y2 = P2.Y;
    float X3 = P3.X, Y3 = P3.Y;
    float X4 = P4.X, Y4 = P4.Y;
    
    float Denominator = (X1 - X2) * (Y3 - Y4) - (Y1 - Y2) * (X3 - X4);
    
    if (FMath::IsNearlyZero(Denominator))
    {
        // Linhas paralelas
        return false;
    }
    
    float T = ((X1 - X3) * (Y3 - Y4) - (Y1 - Y3) * (X3 - X4)) / Denominator;
    float U = -((X1 - X2) * (Y1 - Y3) - (Y1 - Y2) * (X1 - X3)) / Denominator;
    
    // Verificar se a interseção está dentro dos segmentos
    if (T >= 0.0f && T <= 1.0f && U >= 0.0f && U <= 1.0f)
    {
        OutIntersection.X = X1 + T * (X2 - X1);
        OutIntersection.Y = Y1 + T * (Y2 - Y1);
        OutIntersection.Z = (P1.Z + P2.Z + P3.Z + P4.Z) / 4.0f; // Média das alturas
        
        return true;
    }
    
    return false;
}

// Implementação das funções ausentes usando APIs modernas do UE 5.6

AChaosIsland* AAURACRONPCGChaosIslandManager::SpawnChaosIsland(const FVector& Location)
{
    if (!ChaosIslandClass)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: ChaosIslandClass is not set!"));
        return nullptr;
    }

    // Verificar se o ponto não está muito próximo de ilhas existentes
    if (IsPointTooCloseToExistingIslands(Location))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: Cannot spawn Chaos Island at %s - too close to existing islands"),
            *Location.ToString());
        return nullptr;
    }

    // Ajustar posição baseado no terreno usando line trace
    FVector AdjustedLocation = Location;
    FVector TraceStart = Location + FVector(0, 0, 2000);
    FVector TraceEnd = Location - FVector(0, 0, 2000);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.AddIgnoredActor(this);

    if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
    {
        AdjustedLocation = HitResult.Location + FVector(0, 0, 100); // Elevar 1 metro acima do terreno
    }

    // Configurar parâmetros de spawn usando APIs modernas
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;
    SpawnParams.Owner = this;
    SpawnParams.Instigator = GetInstigator();

    // Spawnar a ilha caos
    AChaosIsland* NewChaosIsland = GetWorld()->SpawnActor<AChaosIsland>(
        ChaosIslandClass,
        AdjustedLocation,
        FRotator::ZeroRotator,
        SpawnParams
    );

    if (NewChaosIsland)
    {
        // Configurar propriedades da ilha baseado no estado atual do manager
        NewChaosIsland->SetActivityLevel(0.5f + (ChaosIslands.Num() * 0.1f)); // Aumentar atividade com mais ilhas

        // Configurar intensidades baseado na fase atual do mapa
        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                NewChaosIsland->EnvironmentalHazardIntensity = 0.8f;
                NewChaosIsland->TerrainInstabilityIntensity = 0.6f;
                NewChaosIsland->HighRiskRewardMultiplier = 1.2f;
                break;

            case EAURACRONMapPhase::Convergence:
                NewChaosIsland->EnvironmentalHazardIntensity = 1.2f;
                NewChaosIsland->TerrainInstabilityIntensity = 1.0f;
                NewChaosIsland->HighRiskRewardMultiplier = 1.5f;
                break;

            case EAURACRONMapPhase::Intensification:
                NewChaosIsland->EnvironmentalHazardIntensity = 1.6f;
                NewChaosIsland->TerrainInstabilityIntensity = 1.4f;
                NewChaosIsland->HighRiskRewardMultiplier = 1.8f;
                break;

            case EAURACRONMapPhase::Resolution:
                NewChaosIsland->EnvironmentalHazardIntensity = 2.0f;
                NewChaosIsland->TerrainInstabilityIntensity = 1.8f;
                NewChaosIsland->HighRiskRewardMultiplier = 2.2f;
                break;
        }

        // Configurar ambientes de transição baseado na localização
        if (AdjustedLocation.X > 0 && AdjustedLocation.Y > 0)
        {
            NewChaosIsland->TransitionEnvironments.Add(EAURACRONEnvironmentType::RadiantPlains);
        }
        else if (AdjustedLocation.X < 0 && AdjustedLocation.Y > 0)
        {
            NewChaosIsland->TransitionEnvironments.Add(EAURACRONEnvironmentType::ZephyrFirmament);
        }
        else
        {
            NewChaosIsland->TransitionEnvironments.Add(EAURACRONEnvironmentType::PurgatoryRealm);
        }

        // Adicionar à lista de ilhas gerenciadas
        ChaosIslands.Add(NewChaosIsland);

        // Configurar networking se necessário
        if (HasAuthority())
        {
            NewChaosIsland->SetReplicates(true);
            NewChaosIsland->SetReplicateMovement(false); // Ilhas não se movem
        }

        // Ativar a ilha
        NewChaosIsland->bIsActive = true;

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Successfully spawned Chaos Island at %s (Total islands: %d)"),
            *AdjustedLocation.ToString(), ChaosIslands.Num());

        // Spawnar portal associado se configurado
        if (bAutoSpawnPortals)
        {
            FVector PortalLocation = AdjustedLocation + FVector(
                FMath::RandRange(-500.0f, 500.0f),
                FMath::RandRange(-500.0f, 500.0f),
                50.0f
            );

            SpawnChaosPortal(PortalLocation);
        }

        return NewChaosIsland;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: Failed to spawn Chaos Island at %s"),
            *AdjustedLocation.ToString());
        return nullptr;
    }
}

AAURACRONPCGChaosPortal* AAURACRONPCGChaosIslandManager::SpawnChaosPortal(const FVector& Location)
{
    if (!ChaosPortalClass)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: ChaosPortalClass is not set!"));
        return nullptr;
    }

    // Ajustar posição baseado no terreno
    FVector AdjustedLocation = Location;
    FVector TraceStart = Location + FVector(0, 0, 1000);
    FVector TraceEnd = Location - FVector(0, 0, 1000);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.AddIgnoredActor(this);

    if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
    {
        AdjustedLocation = HitResult.Location + FVector(0, 0, 50); // Elevar meio metro acima do terreno
    }

    // Configurar parâmetros de spawn
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;
    SpawnParams.Owner = this;
    SpawnParams.Instigator = GetInstigator();

    // Spawnar o portal caos
    AAURACRONPCGChaosPortal* NewChaosPortal = GetWorld()->SpawnActor<AAURACRONPCGChaosPortal>(
        ChaosPortalClass,
        AdjustedLocation,
        FRotator::ZeroRotator,
        SpawnParams
    );

    if (NewChaosPortal)
    {
        // Determinar tipo do portal baseado no número de portais existentes e fase do mapa
        EChaosPortalType PortalType = EChaosPortalType::Standard;

        int32 ExistingPortals = ChaosPortals.Num();
        float RandomValue = FMath::RandRange(0.0f, 1.0f);

        // Probabilidades baseadas na fase do mapa
        float EliteChance = 0.2f;
        float LegendaryChance = 0.05f;

        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                EliteChance = 0.15f;
                LegendaryChance = 0.02f;
                break;

            case EAURACRONMapPhase::Convergence:
                EliteChance = 0.25f;
                LegendaryChance = 0.08f;
                break;

            case EAURACRONMapPhase::Intensification:
                EliteChance = 0.35f;
                LegendaryChance = 0.15f;
                break;

            case EAURACRONMapPhase::Resolution:
                EliteChance = 0.45f;
                LegendaryChance = 0.25f;
                break;
        }

        // Aumentar chances com mais portais existentes
        EliteChance += ExistingPortals * 0.05f;
        LegendaryChance += ExistingPortals * 0.02f;

        // Determinar tipo
        if (RandomValue < LegendaryChance)
        {
            PortalType = EChaosPortalType::Legendary;
        }
        else if (RandomValue < LegendaryChance + EliteChance)
        {
            PortalType = EChaosPortalType::Elite;
        }

        // Configurar o portal
        NewChaosPortal->SetPortalType(PortalType);
        NewChaosPortal->UpdateForMapPhase(CurrentMapPhase);

        // Configurar duração baseada no tipo e fase
        float PortalDuration = 0.0f; // 0 = permanente por padrão

        if (CurrentMapPhase == EAURACRONMapPhase::Resolution)
        {
            // Na fase de resolução, portais têm duração limitada para criar urgência
            switch (PortalType)
            {
                case EChaosPortalType::Standard:
                    PortalDuration = 300.0f; // 5 minutos
                    break;
                case EChaosPortalType::Elite:
                    PortalDuration = 240.0f; // 4 minutos
                    break;
                case EChaosPortalType::Legendary:
                    PortalDuration = 180.0f; // 3 minutos
                    break;
            }
        }

        // Ativar o portal
        NewChaosPortal->ActivatePortal(PortalDuration, 1.0f);

        // Adicionar à lista de portais gerenciados
        ChaosPortals.Add(NewChaosPortal);

        // Configurar networking
        if (HasAuthority())
        {
            NewChaosPortal->SetReplicates(true);
            NewChaosPortal->SetReplicateMovement(false);
        }

        // Log do spawn
        FString TypeName;
        switch (PortalType)
        {
            case EChaosPortalType::Standard: TypeName = TEXT("Standard"); break;
            case EChaosPortalType::Elite: TypeName = TEXT("Elite"); break;
            case EChaosPortalType::Legendary: TypeName = TEXT("Legendary"); break;
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Successfully spawned %s Chaos Portal at %s (Total portals: %d)"),
            *TypeName, *AdjustedLocation.ToString(), ChaosPortals.Num());

        return NewChaosPortal;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: Failed to spawn Chaos Portal at %s"),
            *AdjustedLocation.ToString());
        return nullptr;
    }
}

bool AAURACRONPCGChaosIslandManager::IsPointTooCloseToExistingIslands(const FVector& Point) const
{
    // Distância mínima entre ilhas (em unidades do UE)
    float MinDistance = 2000.0f; // 20 metros

    // Verificar distância para todas as ilhas caos existentes
    for (const AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            float Distance = FVector::Dist(Point, Island->GetActorLocation());
            if (Distance < MinDistance)
            {
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Point %s is too close to existing Chaos Island (distance: %.2f, min: %.2f)"),
                    *Point.ToString(), Distance, MinDistance);
                return true;
            }
        }
    }

    // Verificar distância para portais também
    float MinPortalDistance = 1000.0f; // 10 metros
    for (const AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            float Distance = FVector::Dist(Point, Portal->GetActorLocation());
            if (Distance < MinPortalDistance)
            {
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Point %s is too close to existing Chaos Portal (distance: %.2f, min: %.2f)"),
                    *Point.ToString(), Distance, MinPortalDistance);
                return true;
            }
        }
    }

    // Verificar proximidade com outras ilhas importantes no mundo
    if (UWorld* World = GetWorld())
    {
        // Buscar outras ilhas no mundo (Sanctuary, Arsenal, etc.)
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (Actor && Actor != this)
            {
                // Verificar se é uma ilha (baseado no nome da classe)
                FString ClassName = Actor->GetClass()->GetName();
                if (ClassName.Contains(TEXT("Island")) || ClassName.Contains(TEXT("Sanctuary")) || ClassName.Contains(TEXT("Arsenal")))
                {
                    float Distance = FVector::Dist(Point, Actor->GetActorLocation());
                    if (Distance < MinDistance)
                    {
                        UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Point %s is too close to existing island %s (distance: %.2f)"),
                            *Point.ToString(), *ClassName, Distance);
                        return true;
                    }
                }
            }
        }
    }

    return false;
}

void AAURACRONPCGChaosIslandManager::UpdateEffectsIntensity()
{
    // Calcular intensidade base baseada na fase do mapa
    float BaseIntensity = 1.0f;
    float IntensityMultiplier = 1.0f;

    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            BaseIntensity = 0.6f;
            IntensityMultiplier = 1.0f;
            break;

        case EAURACRONMapPhase::Convergence:
            BaseIntensity = 0.8f;
            IntensityMultiplier = 1.2f;
            break;

        case EAURACRONMapPhase::Intensification:
            BaseIntensity = 1.2f;
            IntensityMultiplier = 1.5f;
            break;

        case EAURACRONMapPhase::Resolution:
            BaseIntensity = 1.5f;
            IntensityMultiplier = 2.0f;
            break;
    }

    // Modificar intensidade baseado no número de ilhas ativas
    int32 ActiveIslands = 0;
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island) && Island->bIsActive)
        {
            ActiveIslands++;
        }
    }

    // Mais ilhas = mais caos = mais intensidade
    float IslandMultiplier = 1.0f + (ActiveIslands * 0.15f);
    float FinalIntensity = BaseIntensity * IntensityMultiplier * IslandMultiplier;

    // Atualizar todas as ilhas caos
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Aplicar intensidade com variação aleatória para cada ilha
            float IslandVariation = FMath::RandRange(0.8f, 1.2f);
            float IslandIntensity = FinalIntensity * IslandVariation;

            // Atualizar propriedades da ilha
            Island->EnvironmentalHazardIntensity = FMath::Clamp(IslandIntensity * 1.0f, 0.1f, 3.0f);
            Island->TerrainInstabilityIntensity = FMath::Clamp(IslandIntensity * 0.8f, 0.1f, 2.5f);
            Island->HighRiskRewardMultiplier = FMath::Clamp(1.0f + (IslandIntensity * 0.5f), 1.0f, 3.0f);

            // Atualizar velocidades de efeitos
            Island->VortexRotationSpeed = FMath::Clamp(30.0f + (IslandIntensity * 20.0f), 10.0f, 120.0f);
            Island->RunePulseIntensity = FMath::Clamp(IslandIntensity * 0.7f, 0.1f, 2.0f);

            // Forçar atualização visual
            Island->UpdateIslandVisuals();

            UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Updated island %s with intensity %.2f"),
                *Island->GetName(), IslandIntensity);
        }
    }

    // Atualizar todos os portais caos
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            // Aplicar intensidade aos portais
            float PortalVariation = FMath::RandRange(0.9f, 1.1f);
            float PortalIntensity = FinalIntensity * PortalVariation;

            Portal->SetPortalIntensity(FMath::Clamp(PortalIntensity, 0.5f, 3.0f));

            // Atualizar probabilidades baseado na intensidade
            float ProbabilityMultiplier = FMath::Clamp(PortalIntensity, 0.5f, 2.0f);

            // Acessar propriedades através de reflexão ou métodos públicos se disponíveis
            // Como as propriedades são protected, vamos usar uma abordagem indireta
            Portal->UpdateForMapPhase(CurrentMapPhase);

            UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Updated portal %s with intensity %.2f"),
                *Portal->GetName(), PortalIntensity);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Updated effects intensity - Base: %.2f, Multiplier: %.2f, Islands: %d, Final: %.2f"),
        BaseIntensity, IntensityMultiplier, ActiveIslands, FinalIntensity);
}