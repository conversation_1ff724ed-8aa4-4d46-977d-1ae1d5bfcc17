// SigilGameplayEffects.cpp
// AURACRON - Sistema de Sígilos
// Implementação dos GameplayEffects para fusão automática e modificadores espectrais
// APIs verificadas: GameplayEffect.h, GameplayEffectExecutionCalculation.h

#include "Sigils/SigilGameplayEffects.h"
#include "Sigils/SigilAttributeSet.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffectTypes.h"
#include "GameplayEffectComponents/TargetTagRequirementsGameplayEffectComponent.h"
#include "GameplayEffectComponents/TargetTagsGameplayEffectComponent.h"
#include "GameplayTagsManager.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"

// ========================================
// INICIALIZAÇÃO DE DADOS ESTÁTICOS
// ========================================

TMap<ESpectralEffectType, TSubclassOf<UGameplayEffect>> USigilEffectFactory::EffectTemplates;
TMap<ESigilRarity, float> USigilEffectFactory::DefaultRarityMultipliers;

// ========================================
// GAMEPLAY EFFECT BASE
// ========================================

USigilGameplayEffectBase::USigilGameplayEffectBase()
{
    // Configuração base do GameplayEffect
    DurationPolicy = EGameplayEffectDurationType::Infinite;
    StackingType = EGameplayEffectStackingType::None;
    
    // Configurar tags padrão usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& TagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer BaseTagContainer = FInheritedTagContainer();
    BaseTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.Base")));
    TagsComponent.SetAndApplyTargetTagChanges(BaseTagContainer);
    
    // Configuração padrão do efeito espectral
    SpectralConfig.EffectType = ESpectralEffectType::None;
    SpectralConfig.BaseMagnitude = 1.0f;
    SpectralConfig.Duration = -1.0f;
    SpectralConfig.bCanStack = false;
    SpectralConfig.MaxStacks = 1;
    
    // Configurações MOBA
    TeamFightMultiplier = 1.0f;
    ObjectiveMultiplier = 1.0f;
    
    // Configurações de aplicação
    MinimumRarity = ESigilRarity::Common;
    RequiredSigilType = ESigilType::None;
    bFusionOnly = false;
}

void USigilGameplayEffectBase::PostInitProperties()
{
    Super::PostInitProperties();
    
    // Configurar duração baseada na configuração espectral
    if (SpectralConfig.Duration > 0.0f)
    {
        DurationPolicy = EGameplayEffectDurationType::HasDuration;
        DurationMagnitude = FGameplayEffectModifierMagnitude(SpectralConfig.Duration);
    }
    else
    {
        DurationPolicy = EGameplayEffectDurationType::Infinite;
    }
    
    // Configurar empilhamento
    if (SpectralConfig.bCanStack)
    {
        StackingType = EGameplayEffectStackingType::AggregateBySource;
        StackLimitCount = SpectralConfig.MaxStacks;
    }
    
    // Adicionar tags do efeito espectral usando UTargetTagsGameplayEffectComponent
    if (!SpectralConfig.EffectTags.IsEmpty())
    {
        UTargetTagsGameplayEffectComponent& SpectralTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
        FInheritedTagContainer SpectralTags;
        for (const FGameplayTag& Tag : SpectralConfig.EffectTags)
        {
            SpectralTags.AddTag(Tag);
        }
        SpectralTagsComponent.SetAndApplyTargetTagChanges(SpectralTags);
    }
}

// ========================================
// EFEITO DE FUSÃO AUTOMÁTICA
// ========================================

USigilAutoFusionEffect::USigilAutoFusionEffect()
{
    // Configuração específica de fusão automática
    FusionTimeSeconds = SigilEffectConstants::DEFAULT_FUSION_TIME;
    FusionPowerMultiplier = 1.5f;
    bPermanentFusion = true;
    
    // Configurar multiplicadores por raridade
    FusionMultipliersByRarity.Add(ESigilRarity::Common, 1.2f);
    FusionMultipliersByRarity.Add(ESigilRarity::Rare, 1.4f);
    FusionMultipliersByRarity.Add(ESigilRarity::Epic, 1.7f);
    FusionMultipliersByRarity.Add(ESigilRarity::Legendary, 2.2f);
    
    // Configurar como efeito de fusão
    bFusionOnly = true;
    
    // Tags específicas de fusão usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& FusionTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer FusionTagContainer = FInheritedTagContainer();
    FusionTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.AutoFusion")));
    FusionTagsComponent.SetAndApplyTargetTagChanges(FusionTagContainer);
    
    // Configurar período de aplicação (verificar a cada segundo)
    Period = 1.0f;
    bExecutePeriodicEffectOnApplication = false;
    
    // Configurar como efeito infinito até fusão
    DurationPolicy = EGameplayEffectDurationType::Infinite;
}

void USigilAutoFusionEffect::PostInitProperties()
{
    Super::PostInitProperties();
    
    // Configurar modificadores de atributos espectrais
    FGameplayModifierInfo PowerModifier;
    PowerModifier.Attribute = USigilAttributeSet::GetSpectralPowerAttribute();
    PowerModifier.ModifierOp = EGameplayModOp::Additive;
    PowerModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FusionPowerMultiplier);
    Modifiers.Add(PowerModifier);
    
    // Configurar execution calculation para lógica complexa
    FGameplayEffectExecutionDefinition ExecutionDef;
    ExecutionDef.CalculationClass = USigilFusionExecutionCalculation::StaticClass();
    Executions.Add(ExecutionDef);
}

// ========================================
// EFEITO DE FUSÃO FORÇADA
// ========================================

USigilForceFusionEffect::USigilForceFusionEffect()
{
    ManaCost = SigilEffectConstants::FORCE_FUSION_MANA_COST;
    CooldownSeconds = SigilEffectConstants::FORCE_FUSION_COOLDOWN;
    ForcedFusionPenalty = 0.8f;
    
    // Configurar como efeito instantâneo
    DurationPolicy = EGameplayEffectDurationType::Instant;
    
    // Tags de fusão forçada usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& ForceFusionTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer ForceFusionTagContainer = FInheritedTagContainer();
    ForceFusionTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.ForceFusion")));
    ForceFusionTagsComponent.SetAndApplyTargetTagChanges(ForceFusionTagContainer);
    
    // Configurar custo de mana
    FGameplayModifierInfo ManaCostModifier;
    ManaCostModifier.Attribute = USigilAttributeSet::GetManaRegenerationAttribute();
    ManaCostModifier.ModifierOp = EGameplayModOp::Additive;
    ManaCostModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(-ManaCost);
    Modifiers.Add(ManaCostModifier);
}

// ========================================
// EFEITOS ESPECTRAIS POR ATRIBUTO
// ========================================

USigilSpectralPowerEffect::USigilSpectralPowerEffect()
{
    PowerIncrease = 10.0f;
    PowerIncreasePercent = 0.0f;
    
    SpectralConfig.EffectType = ESpectralEffectType::PowerBoost;
    SpectralConfig.BaseMagnitude = PowerIncrease;
    
    // Tags de poder espectral usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& SpectralPowerTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer SpectralPowerTagContainer = FInheritedTagContainer();
    SpectralPowerTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralPower")));
    SpectralPowerTagsComponent.SetAndApplyTargetTagChanges(SpectralPowerTagContainer);
    
    // Modificador de poder espectral
    FGameplayModifierInfo PowerModifier;
    PowerModifier.Attribute = USigilAttributeSet::GetSpectralPowerAttribute();
    PowerModifier.ModifierOp = EGameplayModOp::Additive;
    PowerModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(PowerIncrease);
    Modifiers.Add(PowerModifier);
    
    // Modificador percentual se especificado
    if (PowerIncreasePercent > 0.0f)
    {
        FGameplayModifierInfo PercentModifier;
        PercentModifier.Attribute = USigilAttributeSet::GetSpectralPowerAttribute();
        PercentModifier.ModifierOp = EGameplayModOp::Multiplicitive;
        PercentModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(PowerIncreasePercent / 100.0f);
        Modifiers.Add(PercentModifier);
    }
}

USigilSpectralResilienceEffect::USigilSpectralResilienceEffect()
{
    ResilienceIncrease = 8.0f;
    DamageReductionPercent = 5.0f;
    
    SpectralConfig.EffectType = ESpectralEffectType::ResilienceBoost;
    SpectralConfig.BaseMagnitude = ResilienceIncrease;
    
    // Tags de resistência espectral usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& SpectralResilienceTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer SpectralResilienceTagContainer = FInheritedTagContainer();
    SpectralResilienceTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralResilience")));
    SpectralResilienceTagsComponent.SetAndApplyTargetTagChanges(SpectralResilienceTagContainer);
    
    // Modificador de resistência espectral
    FGameplayModifierInfo ResilienceModifier;
    ResilienceModifier.Attribute = USigilAttributeSet::GetSpectralResilienceAttribute();
    ResilienceModifier.ModifierOp = EGameplayModOp::Additive;
    ResilienceModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(ResilienceIncrease);
    Modifiers.Add(ResilienceModifier);
    
    // Modificador de poder defensivo
    FGameplayModifierInfo DefenseModifier;
    DefenseModifier.Attribute = USigilAttributeSet::GetDefensePowerAttribute();
    DefenseModifier.ModifierOp = EGameplayModOp::Multiplicitive;
    DefenseModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(DamageReductionPercent / 100.0f);
    Modifiers.Add(DefenseModifier);
}

USigilSpectralVelocityEffect::USigilSpectralVelocityEffect()
{
    VelocityIncrease = 12.0f;
    MovementSpeedIncrease = 25.0f;
    CooldownReductionPercent = 10.0f;
    
    SpectralConfig.EffectType = ESpectralEffectType::VelocityBoost;
    SpectralConfig.BaseMagnitude = VelocityIncrease;
    
    // Tags de velocidade espectral usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& SpectralVelocityTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer SpectralVelocityTagContainer = FInheritedTagContainer();
    SpectralVelocityTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralVelocity")));
    SpectralVelocityTagsComponent.SetAndApplyTargetTagChanges(SpectralVelocityTagContainer);
    
    // Modificador de velocidade espectral
    FGameplayModifierInfo VelocityModifier;
    VelocityModifier.Attribute = USigilAttributeSet::GetSpectralVelocityAttribute();
    VelocityModifier.ModifierOp = EGameplayModOp::Additive;
    VelocityModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(VelocityIncrease);
    Modifiers.Add(VelocityModifier);
    
    // Modificador de velocidade de movimento
    FGameplayModifierInfo MovementModifier;
    MovementModifier.Attribute = USigilAttributeSet::GetMovementSpeedAttribute();
    MovementModifier.ModifierOp = EGameplayModOp::Additive;
    MovementModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(MovementSpeedIncrease);
    Modifiers.Add(MovementModifier);
    
    // Modificador de redução de cooldown
    FGameplayModifierInfo CooldownModifier;
    CooldownModifier.Attribute = USigilAttributeSet::GetCooldownReductionAttribute();
    CooldownModifier.ModifierOp = EGameplayModOp::Additive;
    CooldownModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(CooldownReductionPercent);
    Modifiers.Add(CooldownModifier);
}

USigilSpectralFocusEffect::USigilSpectralFocusEffect()
{
    FocusIncrease = 15.0f;
    CriticalChanceIncrease = 8.0f;
    ManaRegenIncrease = 5.0f;
    
    SpectralConfig.EffectType = ESpectralEffectType::FocusBoost;
    SpectralConfig.BaseMagnitude = FocusIncrease;
    
    // Tags de foco espectral usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& SpectralFocusTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer SpectralFocusTagContainer = FInheritedTagContainer();
    SpectralFocusTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralFocus")));
    SpectralFocusTagsComponent.SetAndApplyTargetTagChanges(SpectralFocusTagContainer);
    
    // Modificador de foco espectral
    FGameplayModifierInfo FocusModifier;
    FocusModifier.Attribute = USigilAttributeSet::GetSpectralFocusAttribute();
    FocusModifier.ModifierOp = EGameplayModOp::Additive;
    FocusModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FocusIncrease);
    Modifiers.Add(FocusModifier);
    
    // Modificador de chance crítica
    FGameplayModifierInfo CriticalModifier;
    CriticalModifier.Attribute = USigilAttributeSet::GetCriticalChanceAttribute();
    CriticalModifier.ModifierOp = EGameplayModOp::Additive;
    CriticalModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(CriticalChanceIncrease);
    Modifiers.Add(CriticalModifier);
    
    // Modificador de regeneração de mana
    FGameplayModifierInfo ManaRegenModifier;
    ManaRegenModifier.Attribute = USigilAttributeSet::GetManaRegenerationAttribute();
    ManaRegenModifier.ModifierOp = EGameplayModOp::Additive;
    ManaRegenModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(ManaRegenIncrease);
    Modifiers.Add(ManaRegenModifier);
}

// ========================================
// EFEITOS MOBA ESPECÍFICOS
// ========================================

USigilTeamFightEffect::USigilTeamFightEffect()
{
    DamageBonus = 15.0f;
    ResistanceBonus = 10.0f;
    TeamFightRadius = SigilEffectConstants::TEAM_FIGHT_RADIUS;
    MinEnemiesForActivation = SigilEffectConstants::MIN_ENEMIES_TEAM_FIGHT;
    
    SpectralConfig.EffectType = ESpectralEffectType::TeamFightBonus;
    SpectralConfig.BaseMagnitude = DamageBonus;
    
    // Configurar como efeito condicional
    DurationPolicy = EGameplayEffectDurationType::Infinite;
    Period = 0.5f; // Verificar a cada meio segundo
    bExecutePeriodicEffectOnApplication = true;
    
    // Tags de team fight usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& TeamFightTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TeamFightTagContainer = FInheritedTagContainer();
    TeamFightTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.TeamFight")));
    TeamFightTagsComponent.SetAndApplyTargetTagChanges(TeamFightTagContainer);
    
    // Configurar execution calculation para detecção de team fight
    FGameplayEffectExecutionDefinition ExecutionDef;
    ExecutionDef.CalculationClass = USigilFusionExecutionCalculation::StaticClass();
    Executions.Add(ExecutionDef);
}

USigilObjectiveEffect::USigilObjectiveEffect()
{
    ObjectiveDamageBonus = 20.0f;
    AttackSpeedBonus = 25.0f;
    ObjectiveRadius = SigilEffectConstants::OBJECTIVE_RADIUS;
    
    SpectralConfig.EffectType = ESpectralEffectType::ObjectiveBonus;
    SpectralConfig.BaseMagnitude = ObjectiveDamageBonus;
    
    // Configurar tags de objetivos válidos
    ValidObjectiveTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.Objective.Tower"))
    );
    ValidObjectiveTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.Objective.Nexus"))
    );
    ValidObjectiveTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.Objective.Dragon"))
    );
    ValidObjectiveTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.Objective.Baron"))
    );
    
    // Configurar como efeito condicional
    DurationPolicy = EGameplayEffectDurationType::Infinite;
    Period = 1.0f; // Verificar a cada segundo
    bExecutePeriodicEffectOnApplication = true;
    
    // Tags de objetivo usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& ObjectiveTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer ObjectiveTagContainer = FInheritedTagContainer();
    ObjectiveTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.Objective")));
    ObjectiveTagsComponent.SetAndApplyTargetTagChanges(ObjectiveTagContainer);
}

// ========================================
// EFEITO DE REFORGE
// ========================================

USigilReforgeEffect::USigilReforgeEffect()
{
    ReforgeCooldown = SigilEffectConstants::REFORGE_COOLDOWN;
    ReforgeGoldCost = SigilEffectConstants::REFORGE_GOLD_COST;
    RarityUpgradeChance = SigilEffectConstants::RARITY_UPGRADE_CHANCE;
    KeepStatsChance = SigilEffectConstants::KEEP_STATS_CHANCE;
    
    // Configurar como efeito instantâneo
    DurationPolicy = EGameplayEffectDurationType::Instant;
    
    // Tags de reforge usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& ReforgeTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer ReforgeTagContainer = FInheritedTagContainer();
    ReforgeTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.Reforge")));
    ReforgeTagsComponent.SetAndApplyTargetTagChanges(ReforgeTagContainer);
    
    // Usar UTargetTagRequirementsGameplayEffectComponent em vez de APIs deprecated
    UTargetTagRequirementsGameplayEffectComponent& TagComponent = AddComponent<UTargetTagRequirementsGameplayEffectComponent>();
    TagComponent.ApplicationTagRequirements.IgnoreTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.State.ReforgeCD"))
    );
    TagComponent.OngoingTagRequirements.IgnoreTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.State.ReforgeCD"))
    );
}

// ========================================
// EXECUTION CALCULATIONS
// ========================================

USigilFusionExecutionCalculation::USigilFusionExecutionCalculation()
{
    // Definir atributos relevantes para captura
    FGameplayEffectAttributeCaptureDefinition SpectralPowerDef;
    SpectralPowerDef.AttributeToCapture = USigilAttributeSet::GetSpectralPowerAttribute();
    SpectralPowerDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralPowerDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralPowerDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralResilienceDef;
    SpectralResilienceDef.AttributeToCapture = USigilAttributeSet::GetSpectralResilienceAttribute();
    SpectralResilienceDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralResilienceDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralResilienceDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralVelocityDef;
    SpectralVelocityDef.AttributeToCapture = USigilAttributeSet::GetSpectralVelocityAttribute();
    SpectralVelocityDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralVelocityDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralVelocityDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralFocusDef;
    SpectralFocusDef.AttributeToCapture = USigilAttributeSet::GetSpectralFocusAttribute();
    SpectralFocusDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralFocusDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralFocusDef);
    
    FGameplayEffectAttributeCaptureDefinition FusionMultiplierDef;
    FusionMultiplierDef.AttributeToCapture = USigilAttributeSet::GetFusionMultiplierAttribute();
    FusionMultiplierDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    FusionMultiplierDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(FusionMultiplierDef);
    
    FGameplayEffectAttributeCaptureDefinition TeamFightBonusDef;
    TeamFightBonusDef.AttributeToCapture = USigilAttributeSet::GetTeamFightBonusAttribute();
    TeamFightBonusDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    TeamFightBonusDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(TeamFightBonusDef);
    
    FGameplayEffectAttributeCaptureDefinition ObjectiveBonusDef;
    ObjectiveBonusDef.AttributeToCapture = USigilAttributeSet::GetObjectiveBonusAttribute();
    ObjectiveBonusDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    ObjectiveBonusDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(ObjectiveBonusDef);
}

void USigilFusionExecutionCalculation::Execute_Implementation(
    const FGameplayEffectCustomExecutionParameters& ExecutionParams,
    FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    UAbilitySystemComponent* SourceASC = ExecutionParams.GetSourceAbilitySystemComponent();
    
    if (!TargetASC || !SourceASC)
    {
        return;
    }
    
    // Obter contexto do efeito
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
    const FGameplayTagContainer* TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();
    
    // Determinar tipo e raridade do sigilo (via tags ou contexto)
    ESigilRarity SigilRarity = ESigilRarity::Common;
    ESigilType SigilType = ESigilType::None;
    
    // Extrair raridade das tags
    if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Rarity.Legendary"))))
    {
        SigilRarity = ESigilRarity::Legendary;
    }
    else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Rarity.Epic"))))
    {
        SigilRarity = ESigilRarity::Epic;
    }
    else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Rarity.Rare"))))
    {
        SigilRarity = ESigilRarity::Rare;
    }
    
    // Extrair tipo das tags
    if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Type.Tank"))))
    {
        SigilType = ESigilType::Tank;
    }
    else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Type.Damage"))))
    {
        SigilType = ESigilType::Damage;
    }
    else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Type.Utility"))))
    {
        SigilType = ESigilType::Utility;
    }
    
    // Calcular multiplicador de fusão
    float FusionMultiplier = CalculateFusionMultiplier(SigilRarity, SigilType);
    
    // Aplicar bônus de team fight se aplicável
    if (IsInTeamFight(ExecutionParams))
    {
        float TeamFightBonus = CalculateTeamFightBonus(ExecutionParams);
        FusionMultiplier *= (1.0f + TeamFightBonus);
    }
    
    // Aplicar bônus de objetivo se aplicável
    if (IsNearObjective(ExecutionParams))
    {
        float ObjectiveBonus = CalculateObjectiveBonus(ExecutionParams);
        FusionMultiplier *= (1.0f + ObjectiveBonus);
    }
    
    // Aplicar modificação ao atributo de multiplicador de fusão
    OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
        USigilAttributeSet::GetFusionMultiplierAttribute(),
        EGameplayModOp::Override,
        FusionMultiplier
    ));
    
    // Log para debugging
    UE_LOG(LogTemp, Log, TEXT("Fusion calculation: Rarity=%d, Type=%d, Multiplier=%f"), 
           (int32)SigilRarity, (int32)SigilType, FusionMultiplier);
}

float USigilFusionExecutionCalculation::CalculateFusionMultiplier(ESigilRarity Rarity, ESigilType Type) const
{
    float BaseMultiplier = 1.0f;
    
    // Multiplicador base por raridade
    switch (Rarity)
    {
        case ESigilRarity::Common:
            BaseMultiplier = SigilEffectConstants::COMMON_MULTIPLIER;
            break;
        case ESigilRarity::Rare:
            BaseMultiplier = SigilEffectConstants::RARE_MULTIPLIER;
            break;
        case ESigilRarity::Epic:
            BaseMultiplier = SigilEffectConstants::EPIC_MULTIPLIER;
            break;
        case ESigilRarity::Legendary:
            BaseMultiplier = SigilEffectConstants::LEGENDARY_MULTIPLIER;
            break;
    }
    
    // Bônus adicional por tipo
    float TypeBonus = 0.0f;
    switch (Type)
    {
        case ESigilType::Tank:
            TypeBonus = 0.1f; // +10% para tanques
            break;
        case ESigilType::Damage:
            TypeBonus = 0.15f; // +15% para dano
            break;
        case ESigilType::Utility:
            TypeBonus = 0.05f; // +5% para utilidade
            break;
    }
    
    return BaseMultiplier * (1.0f + TypeBonus);
}

float USigilFusionExecutionCalculation::CalculateTeamFightBonus(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    // Bônus base de team fight
    return 0.2f; // +20% durante team fights
}

float USigilFusionExecutionCalculation::CalculateObjectiveBonus(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    // Bônus base de objetivo
    return 0.15f; // +15% perto de objetivos
}

bool USigilFusionExecutionCalculation::IsInTeamFight(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return false;
    }
    
    // Verificar se tem tag de team fight ativa
    return TargetASC->HasMatchingGameplayTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.State.TeamFight"))
    );
}

bool USigilFusionExecutionCalculation::IsNearObjective(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return false;
    }
    
    // Verificar se tem tag de proximidade de objetivo ativa
    return TargetASC->HasMatchingGameplayTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.State.NearObjective"))
    );
}

// ========================================
// SPECTRAL EXECUTION CALCULATION
// ========================================

USigilSpectralExecutionCalculation::USigilSpectralExecutionCalculation()
{
    // Definir atributos relevantes para captura
    FGameplayEffectAttributeCaptureDefinition SpectralPowerDef;
    SpectralPowerDef.AttributeToCapture = USigilAttributeSet::GetSpectralPowerAttribute();
    SpectralPowerDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralPowerDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralPowerDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralResilienceDef;
    SpectralResilienceDef.AttributeToCapture = USigilAttributeSet::GetSpectralResilienceAttribute();
    SpectralResilienceDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralResilienceDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralResilienceDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralVelocityDef;
    SpectralVelocityDef.AttributeToCapture = USigilAttributeSet::GetSpectralVelocityAttribute();
    SpectralVelocityDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralVelocityDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralVelocityDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralFocusDef;
    SpectralFocusDef.AttributeToCapture = USigilAttributeSet::GetSpectralFocusAttribute();
    SpectralFocusDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralFocusDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralFocusDef);
    
    FGameplayEffectAttributeCaptureDefinition SigilEfficiencyDef;
    SigilEfficiencyDef.AttributeToCapture = USigilAttributeSet::GetSigilEfficiencyAttribute();
    SigilEfficiencyDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SigilEfficiencyDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SigilEfficiencyDef);
}

void USigilSpectralExecutionCalculation::Execute_Implementation(
    const FGameplayEffectCustomExecutionParameters& ExecutionParams,
    FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return;
    }
    
    // Obter contexto do efeito
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    
    // Determinar tipo de efeito espectral
    ESpectralEffectType EffectType = ESpectralEffectType::PowerBoost; // Padrão
    
    // Calcular modificação espectral
    float SpectralModification = CalculateSpectralModification(ExecutionParams, EffectType);
    
    // Aplicar sinergia entre sígilos
    float SynergyMultiplier = CalculateSigilSynergy(ExecutionParams);
    SpectralModification *= SynergyMultiplier;
    
    // Aplicar modificação baseada no tipo de efeito
    switch (EffectType)
    {
        case ESpectralEffectType::PowerBoost:
            OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
                USigilAttributeSet::GetSpectralPowerAttribute(),
                EGameplayModOp::Additive,
                SpectralModification
            ));
            break;
            
        case ESpectralEffectType::ResilienceBoost:
            OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
                USigilAttributeSet::GetSpectralResilienceAttribute(),
                EGameplayModOp::Additive,
                SpectralModification
            ));
            break;
            
        case ESpectralEffectType::VelocityBoost:
            OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
                USigilAttributeSet::GetSpectralVelocityAttribute(),
                EGameplayModOp::Additive,
                SpectralModification
            ));
            break;
            
        case ESpectralEffectType::FocusBoost:
            OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
                USigilAttributeSet::GetSpectralFocusAttribute(),
                EGameplayModOp::Additive,
                SpectralModification
            ));
            break;
    }
}

float USigilSpectralExecutionCalculation::CalculateSpectralModification(
    const FGameplayEffectCustomExecutionParameters& ExecutionParams,
    ESpectralEffectType EffectType) const
{
    // Magnitude base do efeito
    float BaseMagnitude = 10.0f;
    
    // Obter magnitude do spec se disponível
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    if (Spec.GetSetByCallerMagnitude(UGameplayTagsManager::Get().RequestGameplayTag(FName("Data.BaseMagnitude")), false, BaseMagnitude))
    {
        // Magnitude foi definida via SetByCaller
    }
    
    return BaseMagnitude;
}

float USigilSpectralExecutionCalculation::ApplyRarityMultipliers(float BaseValue, ESigilRarity Rarity) const
{
    float Multiplier = 1.0f;
    
    switch (Rarity)
    {
        case ESigilRarity::Common:
            Multiplier = SigilEffectConstants::COMMON_MULTIPLIER;
            break;
        case ESigilRarity::Rare:
            Multiplier = SigilEffectConstants::RARE_MULTIPLIER;
            break;
        case ESigilRarity::Epic:
            Multiplier = SigilEffectConstants::EPIC_MULTIPLIER;
            break;
        case ESigilRarity::Legendary:
            Multiplier = SigilEffectConstants::LEGENDARY_MULTIPLIER;
            break;
    }
    
    return BaseValue * Multiplier;
}

float USigilSpectralExecutionCalculation::CalculateSigilSynergy(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    // Calcular sinergia baseada no número de sígilos equipados
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return 1.0f;
    }
    
    // Contar sígilos ativos (simplificado)
    int32 ActiveSigils = 0;
    if (TargetASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.State.Equipped"))))
    {
        ActiveSigils = 1; // Implementar contagem real
    }
    
    // Bônus de sinergia: +5% por sigilo adicional
    float SynergyBonus = 1.0f + (ActiveSigils - 1) * 0.05f;
    return FMath::Clamp(SynergyBonus, 1.0f, 1.5f); // Máximo de +50%
}

// ========================================
// FACTORY IMPLEMENTATION
// ========================================

TSubclassOf<UGameplayEffect> USigilEffectFactory::CreateAutoFusionEffect(ESigilRarity Rarity, ESigilType Type)
{
    // Retornar classe base de fusão automática
    // Em implementação real, criaria dinamicamente baseado nos parâmetros
    return USigilAutoFusionEffect::StaticClass();
}

TSubclassOf<UGameplayEffect> USigilEffectFactory::CreateSpectralEffect(
    ESpectralEffectType EffectType, 
    ESigilRarity Rarity, 
    float Magnitude)
{
    // Retornar classe apropriada baseada no tipo de efeito
    switch (EffectType)
    {
        case ESpectralEffectType::PowerBoost:
            return USigilSpectralPowerEffect::StaticClass();
        case ESpectralEffectType::ResilienceBoost:
            return USigilSpectralResilienceEffect::StaticClass();
        case ESpectralEffectType::VelocityBoost:
            return USigilSpectralVelocityEffect::StaticClass();
        case ESpectralEffectType::FocusBoost:
            return USigilSpectralFocusEffect::StaticClass();
        case ESpectralEffectType::TeamFightBonus:
            return USigilTeamFightEffect::StaticClass();
        case ESpectralEffectType::ObjectiveBonus:
            return USigilObjectiveEffect::StaticClass();
        default:
            return USigilGameplayEffectBase::StaticClass();
    }
}

TSubclassOf<UGameplayEffect> USigilEffectFactory::CreateTeamFightEffect(float DamageBonus, float ResistanceBonus)
{
    return USigilTeamFightEffect::StaticClass();
}

TSubclassOf<UGameplayEffect> USigilEffectFactory::CreateObjectiveEffect(float DamageBonus, float AttackSpeedBonus)
{
    return USigilObjectiveEffect::StaticClass();
}

float USigilEffectFactory::GetDefaultRarityMultiplier(ESigilRarity Rarity)
{
    switch (Rarity)
    {
        case ESigilRarity::Common:
            return SigilEffectConstants::COMMON_MULTIPLIER;
        case ESigilRarity::Rare:
            return SigilEffectConstants::RARE_MULTIPLIER;
        case ESigilRarity::Epic:
            return SigilEffectConstants::EPIC_MULTIPLIER;
        case ESigilRarity::Legendary:
            return SigilEffectConstants::LEGENDARY_MULTIPLIER;
        default:
            return 1.0f;
    }
}

bool USigilEffectFactory::ValidateEffectConfig(const FSpectralEffectConfig& Config)
{
    // Validar configuração básica
    if (Config.BaseMagnitude <= 0.0f)
    {
        return false;
    }
    
    if (Config.bCanStack && Config.MaxStacks <= 0)
    {
        return false;
    }
    
    if (Config.Duration == 0.0f)
    {
        return false; // Duração zero não é válida
    }
    
    return true;
}

void USigilEffectFactory::InitializeEffectTemplates()
{
    if (EffectTemplates.Num() > 0)
    {
        return; // Já inicializado
    }
    
    // Inicializar templates de efeitos
    EffectTemplates.Add(ESpectralEffectType::PowerBoost, USigilSpectralPowerEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::ResilienceBoost, USigilSpectralResilienceEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::VelocityBoost, USigilSpectralVelocityEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::FocusBoost, USigilSpectralFocusEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::TeamFightBonus, USigilTeamFightEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::ObjectiveBonus, USigilObjectiveEffect::StaticClass());
    
    // Inicializar multiplicadores padrão
    DefaultRarityMultipliers.Add(ESigilRarity::Common, SigilEffectConstants::COMMON_MULTIPLIER);
    DefaultRarityMultipliers.Add(ESigilRarity::Rare, SigilEffectConstants::RARE_MULTIPLIER);
    DefaultRarityMultipliers.Add(ESigilRarity::Epic, SigilEffectConstants::EPIC_MULTIPLIER);
    DefaultRarityMultipliers.Add(ESigilRarity::Legendary, SigilEffectConstants::LEGENDARY_MULTIPLIER);
}